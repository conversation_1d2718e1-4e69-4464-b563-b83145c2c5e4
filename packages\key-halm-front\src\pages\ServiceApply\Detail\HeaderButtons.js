import React from 'react';
import { But<PERSON>, Modal } from 'choerodon-ui/pro';
import InspectForm from 'alm/pages/EvaluateTemp/components/InspectForm';

import { HALM_MTC } from 'alm/utils/config';
import request from 'utils/request';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';

import ApproveHistoryBtn from 'alm/components/ApproveHistoryBtn';
import ChangeLogsModal from 'alm/components/ChangeLogsModal';

const organizationId = getCurrentOrganizationId();
const saveEvaluateUrl = `${HALM_MTC}/v1/${organizationId}/sr-evaluates/batch-create-evaluate`;

const handleAppraise = (currentDetail, onReload, employeeId) => {
  const { srId, evaluateFlag, evaluateTempId } = currentDetail;
  const inspectFormRef = React.createRef();

  const ratingMode = evaluateFlag === 1 ? 'view' : 'create';
  const tempId = ratingMode === 'create' ? evaluateTempId : srId;

  const props = {
    mode: ratingMode,
    id: tempId,
    sourceTypeCode: 'SR',
    moduleId: srId,
    employeeId,
  };

  Modal.open({
    destroyOnClose: true,
    closable: true,
    title: intl.get(`${prompt}.modal.srEvaluate`).d('服务评价'),
    children: <InspectForm {...props} ref={inspectFormRef} />,
    footer: okBtn => okBtn,
    onOk: async () => {
      if (ratingMode === 'create') {
        const inspectFormData = await inspectFormRef?.current.handleSubmit();
        if (inspectFormData) {
          const { itemList, fileUrlList } = inspectFormData;
          const newSrEvaluateList = itemList.map(i => {
            return {
              ...i,
              srId,
            };
          });
          request(saveEvaluateUrl, {
            method: 'POST',
            body: {
              fileUrlList,
              srEvaluateList: newSrEvaluateList,
              srId,
            },
          }).then(res => {
            if (!res?.failed) {
              onReload(srId);
            }
          });
        } else {
          return false;
        }
      }
    },
  });
};

const HeaderButtons = props => {
  const {
    isNew,
    isEdit,
    history,
    loading,
    dispatch,
    sourcePage,
    detailFormDS,
    employeeId,
    onSave,
    onEdit,
    onCreateSub,
    onCreateWo,
    onChangeSrStatus,
    onChangeSrStatusOfCancel,
    onOpenRefuseReasonModal,
    onReload,
  } = props;

  const record = detailFormDS?.current?.toData() || {};
  const {
    srId,
    srStatus,
    srNumber,
    refuseFlag,
    workflowFlag,
    wkInstanceId,
    createWoFlag,
    canResponseFlag,
    enableEvaluateFlag,
  } = record;

  const changeLogsProps = {
    dispatch,
    moduleName: 'Sr',
    moduleNum: srNumber,
  };
  const displayApproveHistoryBtn =
    workflowFlag && wkInstanceId && !isNew && !isEdit ? { display: 'block' } : { display: 'none' };
  const approveHistoryProps = {
    wkInstanceId,
    history,
  };
  const displayFlagBtn = isNew || isEdit ? { display: 'block' } : { display: 'none' };
  const displayCreateWOBtn = !isEdit && createWoFlag === 1;
  const displayCancelBtn = isEdit ? { display: 'block' } : { display: 'none' };
  // （拟定或编辑） 显示编辑按钮
  const showEditBtn = !isNew && !isEdit && ['REJECTED', 'DRAFT'].includes(srStatus);
  // 待响应且（用户为计划员或（计划员不存在，用户为同一工作中心或上层工作中心））显示接受按钮
  const showAcceptBtn = !isEdit && canResponseFlag === 1;
  // （拟定或已拒绝）显示提交按钮
  const showSubmitBtn = !isEdit && ((!isNew && srStatus === 'DRAFT') || srStatus === 'REJECTED');
  /**
   * 1、待响应且报告人计划员不同且（用户是计划员或（计划员不存在，用户为同一工作中心或上层工作中心））
   * 2、待处理且（用户为计划员或（计划员不存在，用户为同一工作中心或上层工作中心））
   * 显示拒绝按钮
   */
  const showRefuseBtn = !isEdit && refuseFlag === 1;
  // （拟定或已拒绝） 显示取消按钮
  const showCancelBtn = !isNew && !isEdit && (srStatus === 'DRAFT' || srStatus === 'REJECTED');
  // 待处理 显示撤回按钮
  const showRevokelBtn = !isEdit && (srStatus === 'APPROVED' || srStatus === 'APPROVING');

  const showChangeLogBtn = !isEdit && srId;

  const closeBtnFlag = srStatus === 'COMPLETED';

  return (
    <>
      <ApproveHistoryBtn style={displayApproveHistoryBtn} {...approveHistoryProps} />
      {/* 我的服务申请仅编辑时显示 */}
      {(sourcePage !== 'SRC' || (sourcePage === 'SRC' && !isNew && isEdit)) && (
        <Button
          color="primary"
          style={displayFlagBtn}
          onClick={onSave}
          loading={loading.saveLoading}
          waitType="throttle"
          wait={1000}
        >
          {intl.get(`hzero.common.button.save`).d('保存')}
        </Button>
      )}
      <Button style={displayCancelBtn} onClick={onEdit}>
        {intl.get('hzero.common.button.cancel').d('取消')}
      </Button>
      {displayCreateWOBtn && [
        <Button color="primary" onClick={onCreateWo}>
          {intl.get('amtc.serviceApply.button.createWo').d('创建工单')}
        </Button>,
        <Button onClick={onCreateSub}>
          {intl.get('amtc.serviceApply.button.createSub').d('创建委外')}
        </Button>,
      ]}
      {closeBtnFlag && (
        <Button onClick={() => onChangeSrStatus('CLOSED')} color="primary">
          {intl.get(`amtc.serviceApply.button.closeStatus`).d('结束申请')}
        </Button>
      )}
      {showAcceptBtn && (
        <Button
          color={displayCreateWOBtn ? 'default' : 'primary'}
          onClick={() => onChangeSrStatus('RESPONSED')}
        >
          {intl.get(`alm.common.button.accept`).d('接受')}
        </Button>
      )}
      {showRefuseBtn && (
        <Button onClick={() => onOpenRefuseReasonModal()}>
          {intl.get(`hzero.common.button.refuse`).d('拒绝')}
        </Button>
      )}
      {showSubmitBtn && (
        <Button color="primary" onClick={() => onChangeSrStatus('APPROVING')}>
          {intl.get(`hzero.common.button.submit`).d('提交')}
        </Button>
      )}
      {showEditBtn && (
        <Button color={showSubmitBtn ? 'default' : 'primary'} onClick={onEdit}>
          {intl.get('hzero.common.button.edit').d('编辑')}
        </Button>
      )}
      {showCancelBtn && (
        <Button onClick={() => onChangeSrStatusOfCancel()}>
          {intl.get(`hzero.common.button.cancel`).d('取消')}
        </Button>
      )}
      {showRevokelBtn && (
        <Button onClick={() => onChangeSrStatus('DRAFT')}>
          {intl.get(`alm.common.button.recall`).d('撤回')}
        </Button>
      )}
      {enableEvaluateFlag === 1 && srStatus === 'CLOSED' && (
        <Button onClick={() => handleAppraise(record, onReload, employeeId)} color="primary">
          {intl.get(`amtc.serviceApply.button.serviceRating`).d('服务评价')}
        </Button>
      )}
      {showChangeLogBtn && <ChangeLogsModal {...changeLogsProps} />}
    </>
  );
};

export default HeaderButtons;
