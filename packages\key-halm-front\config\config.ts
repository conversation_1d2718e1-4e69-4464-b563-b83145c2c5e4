import { extendParentConfig } from '@hzerojs/plugin-micro';

// const path = require('path');

export default extendParentConfig({
  webpack5: {},
  devServer: {
    port: 8000, // 为了微前端本地启动，和主模块microService配置的端口对应
  },
  routes: [
    // 所有路由外部整体包裹一个routes
    /**
     * <==============   ALM-系统配置  =================>
     */
    /**
     * 默认规则
     */
    {
      path: '/apfm/frameworks',
      priority: 100,
      routes: [
        // 默认规则详情
        {
          path: '/apfm/frameworks/detail',
          priority: 100,
          component: '@/pages/FrameWork',
        },
        // 默认规则创建
        {
          path: '/apfm/frameworks/create',
          priority: 100,
          component: '@/pages/FrameWork',
        },
      ],
    },
    /**
     *  移动端首页配置
     */
    {
      path: '/amtc/character-home-show',
      priority: 100,
      routes: [
        {
          path: '/amtc/character-home-show/list',
          priority: 100,
          component: '@/pages/CharacterHomeShow',
        },
        {
          path: '/amtc/character-home-show/detail/:id',
          priority: 100,
          component: '@/pages/CharacterHomeShow/Detail',
        },
        {
          path: '/amtc/character-home-show/create',
          priority: 100,
          component: '@/pages/CharacterHomeShow/Detail',
        },
      ],
    },
    /**
     *  字段定义
     */
    {
      path: '/apfm/field-definition',
      priority: 100,
      component: '@/pages/FieldDefinition',
    },
    /**
     * 服务区域
     */
    {
      path: '/amdm/maint-sites',
      priority: 100,
      routes: [
        // 服务区域列表
        {
          path: '/amdm/maint-sites/list',
          priority: 100,
          models: ['@/models/maintSites'],
          component: '@/pages/MaintSites/List/MaintSiteListPage',
        },
        // 服务区域详情
        {
          path: '/amdm/maint-sites/detail/:maintSiteId',
          priority: 100,
          models: ['@/models/maintSites', '@/models/frameWork'],
          component: '@/pages/MaintSites/Detail/MaintSiteDetailPage',
        },
        // 服务区域创建
        {
          path: '/amdm/maint-sites/create',
          priority: 100,
          models: ['@/models/maintSites', '@/models/frameWork'],
          component: '@/pages/MaintSites/Detail/MaintSiteDetailPage',
        },
      ],
    },
    /**
     * 内部组织
     */
    {
      path: '/amdm/organization',
      priority: 100,
      routes: [
        // 组织列表
        {
          path: '/amdm/organization/list',
          priority: 100,
          models: ['@/models/organization'],
          component: '@/pages/Organization/List/OrganizationListPage',
        },
        // 组织创建
        {
          path: '/amdm/organization/create',
          priority: 100,
          models: ['@/models/organization'],
          component: '@/pages/Organization/Detail/OrganizationDetailPage',
        },
        // 组织详情
        {
          path: '/amdm/organization/detail/:id',
          priority: 100,
          models: ['@/models/organization'],
          component: '@/pages/Organization/Detail/OrganizationDetailPage',
        },
      ],
    },
    /**
     * 库房
     */
    {
      path: '/ammt/stock',
      priority: 100,
      routes: [
        // 库房列表
        {
          path: '/ammt/stock/list',
          priority: 100,
          component: '@/pages/Stock',
        },
        // 库房创建
        {
          path: '/ammt/stock/create',
          priority: 100,
          component: '@/pages/Stock/Detail',
        },
        // 库房详情
        {
          path: '/ammt/stock/detail/:id/:flag',
          priority: 100,
          component: '@/pages/Stock/Detail',
        },
        {
          path: '/ammt/stock/create-sub/:parentId',
          priority: 100,
          component: '@/pages/Stock/Detail',
        },
      ],
    },
    // 库存货位
    {
      path: '/ammt/locator',
      priority: 100,
      routes: [
        {
          path: '/ammt/locator/list',
          priority: 100,
          models: ['@/models/locator'],
          component: '@/pages/Locator',
        },
      ],
    },
    /**
     *  租户初始化
     */
    {
      path: '/apfm/tenant-init',
      priority: 100,
      component: '@/pages/TenantInit',
    },
    /**
     * 位置类型
     */
    {
      path: '/amdm/asset-locations-types',
      priority: 100,
      component: '@/pages/LocationType',
    },
    /**
     * 资产模板
     */
    {
      path: '/aori/asset-template',
      priority: 100,
      routes: [
        {
          path: '/aori/asset-template/list',
          priority: 100,
          component: '@/pages/AssetTemplate/List',
        },
        {
          path: '/aori/asset-template/detail/:code',
          priority: 100,
          component: '@/pages/AssetTemplate/Detail',
        },
        {
          path: '/aori/asset-template/create',
          priority: 100,
          component: '@/pages/AssetTemplate/Detail',
        },
      ],
    },
    /**
     * 资产分类层级
     */
    {
      path: '/aori/asset-class-level',
      priority: 100,
      component: '@/pages/AssetClassLevel',
    },
    /**
     * 资产分类
     */
    {
      path: '/aori/assetClass',
      priority: 100,
      routes: [
        {
          path: '/aori/assetClass/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/AssetClass/List',
        },
        {
          path: '/aori/assetClass/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/AssetClass/Detail',
        },
        {
          path: '/aori/assetClass/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/AssetClass/Detail',
        },
      ],
    },
    /**
     * 资产专业管理
     */
    {
      path: '/aafm/asset-specialty',
      priority: 100,
      // 资产专业管理列表
      routes: [
        {
          path: '/aafm/asset-specialty/list',
          priority: 100,
          models: ['@/models/assetSpecialty', '@/models/frameWork'],
          component: '@/pages/AssetSpecialty',
        },
        // 资产专业管理详情
        {
          path: '/aafm/asset-specialty/detail/:assetSpecialtyId',
          priority: 100,
          models: ['@/models/assetSpecialty', '@/models/frameWork'],
          component: '@/pages/AssetSpecialty/Detail',
        },
        // 资产专业管理创建
        {
          path: '/aafm/asset-specialty/create',
          priority: 100,
          models: ['@/models/assetSpecialty', '@/models/frameWork'],
          component: '@/pages/AssetSpecialty/Detail',
        },
      ],
    },
    /**
     * 资产状态
     */
    {
      path: '/aafm/asset-status',
      priority: 100,
      routes: [
        {
          path: '/aafm/asset-status/list',
          priority: 100,
          component: '@/pages/AssetStatus',
        },
        {
          path: '/aafm/asset-status/detail/:id',
          priority: 100,
          component: '@/pages/AssetStatus/Detail',
        },
        {
          path: '/aafm/asset-status/create',
          priority: 100,
          component: '@/pages/AssetStatus/Detail',
        },
      ],
    },
    /**
     * 固定资产折旧规则
     */
    {
      path: '/aafm/depreciation-rule',
      priority: 100,
      // 固定资产折旧规则列表
      routes: [
        {
          path: '/aafm/depreciation-rule/list',
          priority: 100,
          models: ['@/models/depreciationRule'],
          component: '@/pages/DepreciationRule',
        },
        // 固定资产折旧规则创建
        {
          path: '/aafm/depreciation-rule/create',
          priority: 100,
          models: ['@/models/depreciationRule'],
          component: '@/pages/DepreciationRule',
        },
        // 固定资产折旧规则详情
        {
          path: '/aafm/depreciation-rule/detail/:id',
          priority: 100,
          models: ['@/models/depreciationRule'],
          component: '@/pages/DepreciationRule',
        },
      ],
    },
    /**
     * 固定资产类别
     */
    {
      path: '/afam/fa-category',
      priority: 100,
      component: '@/pages/FaCategory',
    },
    /**
     * 资产事务处理类型
     */
    {
      path: '/aafm/asset-transaction-type',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aafm/asset-transaction-type/list',
          priority: 100,
          component: '@/pages/AssetTransactionTypes',
        },
        {
          path: '/aafm/asset-transaction-type/detail/:id/:flag',
          priority: 100,
          component: '@/pages/AssetTransactionTypes/Detail',
        },
        // 资产事务处理类型创建
        {
          path: '/aafm/asset-transaction-type/create',
          priority: 100,
          component: '@/pages/AssetTransactionTypes/Detail',
        },
      ],
    },
    /**
     * 动态字段
     */
    {
      path: '/aafm/asset-dynamic-columns',
      priority: 100,
      // 动态字段列表
      routes: [
        {
          path: '/aafm/asset-dynamic-columns/list',
          priority: 100,
          component: '@/pages/AssetDynamicColumns',
        },
      ],
    },
    /**
     * 资产盘点类型
     */
    {
      path: '/actn/counting-type',
      priority: 100,
      routes: [
        // 资产盘点类型列表
        {
          path: '/actn/counting-type/list',
          priority: 100,
          models: ['@/models/countingType'],
          component: '@/pages/CountingType/List/CountingTypeListPage',
        },
        // 资产盘点类型创建
        {
          path: '/actn/counting-type/create',
          priority: 100,
          models: ['@/models/countingType'],
          component: '@/pages/CountingType/Detail/CountingTypeDetailPage',
        },
        // 资产盘点类型详情
        {
          path: '/actn/counting-type/detail/:id',
          priority: 100,
          models: ['@/models/countingType'],
          component: '@/pages/CountingType/Detail/CountingTypeDetailPage',
        },
      ],
    },
    /**
     * 盘点任务模板
     */
    {
      path: '/actn/counting-task-tmpls',
      priority: 100,
      routes: [
        // 盘点任务模板列表
        {
          path: '/actn/counting-task-tmpls/list',
          priority: 100,
          models: ['@/models/countingTaskTmpls'],
          component: '@/pages/CountingTaskTmpls/List/CountingTaskTmplsListPage',
        },
        // 盘点任务模板详情
        {
          path: '/actn/counting-task-tmpls/detail/:id',
          priority: 100,
          models: ['@/models/countingTaskTmpls'],
          component: '@/pages/CountingTaskTmpls/Detail/CountingTaskTmplsDetailPage',
        },
        // 盘点任务模板创建
        {
          path: '/actn/counting-task-tmpls/create',
          priority: 100,
          models: ['@/models/countingTaskTmpls'],
          component: '@/pages/CountingTaskTmpls/Detail/CountingTaskTmplsDetailPage',
        },
      ],
    },
    /**
     * 物料类别
     */
    {
      path: '/ammt/materials-category',
      priority: 100,
      routes: [
        // 物料类别列表
        {
          path: '/ammt/materials-category/list',
          priority: 100,
          models: ['@/models/materialsCategory'],
          component: '@/pages/MaterialsCategory',
        },
      ],
    },
    /**
     * 物料事务类型
     */
    {
      path: '/ammt/material_ts_type',
      priority: 100,
      routes: [
        // 物料事务类型列表
        {
          path: '/ammt/material_ts_type/list',
          priority: 100,
          models: ['@/models/materialTsType'],
          component: '@/pages/MaterialTsType',
        },
        // 物料事务类型创建
        {
          path: '/ammt/material_ts_type/create',
          priority: 100,
          models: ['@/models/materialTsType'],
          component: '@/pages/MaterialTsType/Detail',
        },
        // 物料事务类型详情
        {
          path: '/ammt/material_ts_type/detail/:id',
          priority: 100,
          models: ['@/models/materialTsType'],
          component: '@/pages/MaterialTsType/Detail',
        },
      ],
    },
    /**
     * 服务申请单类型
     */
    {
      path: '/amtc/sr-type',
      priority: 100,
      routes: [
        // 服务申请单类型列表
        {
          path: '/amtc/sr-type/list',
          priority: 100,
          models: ['@/models/srType'],
          component: '@/pages/SrType',
        },
        // 服务申请单类型创建
        {
          path: '/amtc/sr-type/create',
          priority: 100,
          models: ['@/models/srType'],
          component: '@/pages/SrType/Detail',
        },
        // 服务申请单类型详情
        {
          path: '/amtc/sr-type/detail/:id',
          priority: 100,
          models: ['@/models/srType'],
          component: '@/pages/SrType/Detail',
        },
        {
          path: '/amtc/sr-type/create-sub/:id',
          priority: 100,
          models: ['@/models/srType'],
          component: '@/pages/SrType/Detail',
        },
      ],
    },
    /**
     * 工单类型
     */
    {
      path: '/amtc/wo-type',
      priority: 100,
      routes: [
        // 工单类型列表
        {
          path: '/amtc/wo-type/list',
          priority: 100,
          models: ['@/models/woType'],
          component: '@/pages/WoType',
        },
        // 工单类型创建
        {
          path: '/amtc/wo-type/create',
          priority: 100,
          models: ['@/models/woType'],
          component: '@/pages/WoType/Detail',
        },
        // 工单类型详情
        {
          path: '/amtc/wo-type/detail/:id',
          priority: 100,
          models: ['@/models/woType', '@/models/inspectList'],
          component: '@/pages/WoType/Detail',
        },
        {
          path: '/amtc/wo-type/create-sub/:id',
          priority: 100,
          models: ['@/models/woType'],
          component: '@/pages/WoType/Detail',
        },
        {
          path: '/amtc/wo-type/:from/:parentId/detail/:id',
          priority: 100,
          models: ['@/models/inspectList'],
          component: '@/pages/InspectList/Detail',
        },
        {
          path: '/amtc/wo-type/:from/:parentId/create',
          priority: 100,
          models: ['@/models/inspectList'],
          component: '@/pages/InspectList/Detail',
        },
        {
          path: '/amtc/wo-type/:from/:parentId/create-sub/:parentChecklistId',
          priority: 100,
          models: ['@/models/inspectList'],
          component: '@/pages/InspectList/Detail',
        },
      ],
    },
    /**
     * 工单升级规则
     */
    {
      path: '/amtc/wo-upgrade-rule',
      priority: 100,
      routes: [
        // 工单处理升级规则列表
        {
          path: '/amtc/wo-upgrade-rule/list',
          priority: 100,
          models: ['@/models/woUpgradeRule'],
          component: '@/pages/WoUpgradeRule',
        },
        // 工单处理升级规则创建
        {
          path: '/amtc/wo-upgrade-rule/create',
          priority: 100,
          models: ['@/models/woUpgradeRule'],
          component: '@/pages/WoUpgradeRule/Detail',
        },
        // 工单处理升级规则编辑
        {
          path: '/amtc/wo-upgrade-rule/detail/:id',
          priority: 100,
          models: ['@/models/woUpgradeRule'],
          component: '@/pages/WoUpgradeRule/Detail',
        },
      ],
    },
    /**
     * 优先级
     */
    {
      path: '/amtc/priority',
      priority: 100,
      models: ['@/models/priority'],
      component: '@/pages/Priority',
    },
    /**
     * 验收模板
     */
    {
      path: '/amtc/evaluate-temp',
      priority: 100,
      routes: [
        {
          // 列表页面
          path: '/amtc/evaluate-temp/list',
          priority: 100,
          component: '@/pages/EvaluateTemp',
        },
        // 新建页面
        {
          path: '/amtc/evaluate-temp/create',
          priority: 100,
          component: '@/pages/EvaluateTemp/Detail',
        },
        // 明细页面
        {
          path: '/amtc/evaluate-temp/detail/:id',
          priority: 100,
          component: '@/pages/EvaluateTemp/Detail',
        },
      ],
    },
    /**
     *  仪表点类型
     */
    {
      path: '/amtr/meter-types',
      priority: 100,
      routes: [
        {
          path: '/amtr/meter-types/list',
          priority: 100,
          component: '@/pages/MeterTypes',
        },
        {
          path: '/amtr/meter-types/detail/:id',
          priority: 100,
          component: '@/pages/MeterTypes/Detail',
        },
        {
          path: '/amtr/meter-types/create',
          priority: 100,
          component: '@/pages/MeterTypes/Detail',
        },
      ],
    },
    /**
     * 委外申请类型
     */
    {
      path: '/amtc/sub-type',
      priority: 100,
      component: '@/pages/SubType',
    },
    /**
     * 项目类型
     */
    {
      path: '/appm/project-type',
      priority: 100,
      models: ['@/models/projectType'],
      component: '@/pages/ProjectType',
    },
    /**
     * 项目状态
     */
    {
      path: '/appm/project-status',
      priority: 100,
      models: ['@/models/projectStatus'],
      component: '@/pages/ProjectStatus',
    },
    /**
     * 项目角色
     */
    {
      path: '/appm/project-role',
      priority: 100,
      component: '@/pages/ProjectRole',
    },
    /**
     * 项目属性组
     */
    {
      path: '/appm/attribute-set',
      priority: 100,
      routes: [
        // 项目属性组列表
        {
          path: '/appm/attribute-set/list',
          priority: 100,
          models: ['@/models/proAttributeSet'],
          component: '@/pages/ProAttributeSet',
        },
        // 项目属性组创建
        {
          path: '/appm/attribute-set/create',
          priority: 100,
          models: ['@/models/proAttributeSet'],
          component: '@/pages/ProAttributeSet/Detail',
        },
        // 项目属性组详情
        {
          path: '/appm/attribute-set/detail/:id',
          priority: 100,
          models: ['@/models/proAttributeSet'],
          component: '@/pages/ProAttributeSet/Detail',
        },
      ],
    },
    /**
     * 采购订单类型
     */
    {
      path: '/aori/po-type',
      priority: 100,
      routes: [
        // 采购订单类型列表
        {
          path: '/aori/po-type/list',
          priority: 100,
          component: '@/pages/POType',
        },
        // 采购订单类型详情
        {
          path: '/aori/po-type/detail/:id/:num',
          priority: 100,
          component: '@/pages/POType/Detail',
        },
        // 采购订单类型新建
        {
          path: '/aori/po-type/create',
          priority: 100,
          component: '@/pages/POType/Detail',
        },
      ],
    },
    /**
     * 验收单类型
     */
    {
      path: '/arcv/acceptance-type',
      priority: 100,
      // 验收单类型列表
      routes: [
        {
          path: '/arcv/acceptance-type/list',
          priority: 100,
          component: '@/pages/AcceptanceType',
        },
        // 验收单类型详情
        {
          path: '/arcv/acceptance-type/detail/:acceptanceTypeId',
          priority: 100,
          component: '@/pages/AcceptanceType/Detail',
        },
        // 验收单类型创建
        {
          path: '/arcv/acceptance-type/create',
          priority: 100,
          component: '@/pages/AcceptanceType/Detail',
        },
      ],
    },
    /**
     * 验收方式
     */
    {
      path: '/arcv/acceptance-mode',
      priority: 100,
      // 验收方式列表
      routes: [
        {
          path: '/arcv/acceptance-mode/list',
          priority: 100,
          component: '@/pages/AcceptanceMode',
        },
        // 验收方式详情
        {
          path: '/arcv/acceptance-mode/detail/:acceptanceWayId',
          priority: 100,
          component: '@/pages/AcceptanceMode/Detail',
        },
        // 验收方式创建
        {
          path: '/arcv/acceptance-mode/create',
          priority: 100,
          component: '@/pages/AcceptanceMode/Detail',
        },
      ],
    },
    /**
     * 消息通知配置
     */
    {
      path: '/amtc/msg-setting',
      priority: 100,
      routes: [
        {
          path: '/amtc/msg-setting/list',
          priority: 100,
          component: '@/pages/MsgSetting',
        },
        {
          path: '/amtc/msg-setting/detail/:id',
          priority: 100,
          component: '@/pages/MsgSetting/Detail',
        },
        {
          path: '/amtc/msg-setting/create',
          priority: 100,
          component: '@/pages/MsgSetting/Detail',
        },
      ],
    },
    /**
     * 采购申请类型
     */
    {
      path: '/aori/pr-type',
      priority: 100,
      routes: [
        {
          path: '/aori/pr-type/list',
          priority: 100,
          component: '@/pages/PRType',
        },
        {
          path: '/aori/pr-type/create',
          priority: 100,
          component: '@/pages/PRType/Detail',
        },
        {
          path: '/aori/pr-type/detail/:id',
          priority: 100,
          component: '@/pages/PRType/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-主数据  =================>
     */
    /**
     * 合作伙伴
     */
    {
      path: '/amdm/partner',
      priority: 100,
      routes: [
        // 列表
        {
          path: '/amdm/partner/list',
          priority: 100,
          component: '@/pages/Partner/List/PartnerListPage',
        },
        // 创建
        {
          path: '/amdm/partner/create',
          priority: 100,
          component: '@/pages/Partner/Detail/PartnerDetailPage',
        },
        // 详情
        {
          path: '/amdm/partner/detail/:id',
          priority: 100,
          component: '@/pages/Partner/Detail/PartnerDetailPage',
        },
      ],
    },
    /**
     * 位置
     */
    {
      path: '/amdm/location',
      priority: 100,
      routes: [
        // 位置列表
        {
          path: '/amdm/location/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Location',
        },
        // 位置创建
        {
          path: '/amdm/location/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Location/Detail',
        },
        // 位置详情
        {
          path: '/amdm/location/detail/:id/:flag',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Location/Detail',
        },
        {
          path: '/amdm/location/create-sub',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Location/Detail',
        },
      ],
    },
    /**
     * 工作中心
     */
    {
      path: '/amtc/work-center',
      priority: 100,
      routes: [
        // 工作中心列表
        {
          path: '/amtc/work-center/list',
          priority: 100,
          models: ['@/models/workCenter'],
          component: '@/pages/WorkCenter',
        },
        // 工作中心创建
        {
          path: '/amtc/work-center/create',
          priority: 100,
          models: ['@/models/workCenter', '@/models/frameWork'],
          component: '@/pages/WorkCenter/Detail',
        },
        // 工作中心详情
        {
          path: '/amtc/work-center/detail/:id',
          priority: 100,
          models: ['@/models/workCenter', '@/models/frameWork'],
          component: '@/pages/WorkCenter/Detail',
        },
      ],
    },
    /**
     * 标准作业
     */
    {
      path: '/amtc/act',
      priority: 100,
      routes: [
        // 标准作业列表
        {
          path: '/amtc/act/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Act',
        },
        // 导入
        {
          authorized: true,
          path: '/ammt/act/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
        // 标准作业创建
        {
          path: '/amtc/act/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Act/Detail',
        },
        // 标准作业详情
        {
          path: '/amtc/act/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Act/Detail',
        },
      ],
    },
    /**
     * 故障体系
     */
    {
      path: '/rc/rc-systems',
      priority: 100,
      routes: [
        // 故障缺陷列表
        {
          path: '/rc/rc-systems/list',
          priority: 100,
          models: ['@/models/rcSystems'],
          component: '@/pages/RcSystems',
        },
        // 故障缺陷创建
        {
          path: '/rc/rc-systems/create',
          priority: 100,
          models: ['@/models/rcSystems'],
          component: '@/pages/RcSystems/Detail',
        },
        // 故障缺陷详情
        {
          path: '/rc/rc-systems/detail/:id',
          priority: 100,
          models: ['@/models/rcSystems'],
          component: '@/pages/RcSystems/Detail',
        },
      ],
    },
    /**
     * 故障字典
     */
    {
      path: '/rc/rc-assesment',
      priority: 100,
      routes: [
        // 故障缺陷评估项列表
        {
          path: '/rc/rc-assesment/list',
          priority: 100,
          models: ['@/models/rcAssesment'],
          component: '@/pages/RcAssesment',
        },
        // 故障缺陷评估项创建
        {
          path: '/rc/rc-assesment/create',
          priority: 100,
          models: ['@/models/rcAssesment'],
          component: '@/pages/RcAssesment/Detail',
        },
        // 故障缺陷评估项详情
        {
          path: '/rc/rc-assesment/detail/:id',
          priority: 100,
          models: ['@/models/rcAssesment'],
          component: '@/pages/RcAssesment/Detail',
        },
      ],
    },
    // Mttr
    {
      authorized: true,
      path: '/aori/production-shutdown/mttr',
      priority: 100,
      key: '/aori/production-shutdown/mttr',
      models: ['@/models/frameWork'],
      component: '@/pages/ProductionShutdown/Mttr',
    },
    // Mtbf
    {
      authorized: true,
      path: '/aori/production-shutdown/mtbf',
      priority: 100,
      key: '/aori/production-shutdown/mtbf',
      models: ['@/models/frameWork'],
      component: '@/pages/ProductionShutdown/Mtbf',
    },
    /**
     * <==============   ALM-设备/资产  =================>
     */
    /**
     * 资产报表
     */
    {
      path: '/aori/asset-report',
      priority: 100,
      component: '@/pages/AssetReport',
    },
    /**
     * 闲置资产库
     */
    {
      path: '/aori/idle-assets',
      priority: 100,
      component: '@/pages/IdleAsset',
    },
    /**
     * 资产工作台
     */
    {
      path: '/aatn/asset-work-bench',
      priority: 100,
      // 动态字段列表
      routes: [
        {
          path: '/aatn/asset-work-bench/list',
          priority: 100,
          component: '@/pages/AssetWorkBench',
        },
        {
          path: '/aatn/asset-work-bench/detail/:id',
          priority: 100,
          component: '@/pages/AssetWorkBench/Detail',
        },
        {
          path: '/aatn/asset-work-bench/custom-inventory',
          priority: 100,
          component: '@/pages/AssetWorkBench/CustomInventory',
        },
      ],
    },
    /**
     * 设备/资产
     */
    {
      path: '/aafm/equipment-asset',
      priority: 100,
      // 设备/资产列表
      routes: [
        {
          path: '/aafm/equipment-asset/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/EquipmentAsset/List',
        },
        // 设备/资产详情
        {
          path: '/aafm/equipment-asset/detail/:assetId',
          priority: 100,
          models: ['@/models/equipmentAsset', '@/models/frameWork'],
          component: '@/pages/EquipmentAsset/Detail',
        },
        // 设备/资产创建
        {
          path: '/aafm/equipment-asset/create',
          priority: 100,
          models: ['@/models/equipmentAsset', '@/models/frameWork'],
          component: '@/pages/EquipmentAsset/Detail',
        },
        // 设备/资产导入
        {
          authorized: true,
          path: '/aafm/equipment-asset/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * BOM结构清单
     */
    {
      path: '/amtc/bom',
      priority: 100,
      routes: [
        // BOM结构清单列表
        {
          path: '/amtc/bom/list',
          priority: 100,
          models: ['@/models/bom'],
          component: '@/pages/Bom/List',
        },
        // BOM结构清单创建
        {
          path: '/amtc/bom/create',
          priority: 100,
          models: ['@/models/bom'],
          component: '@/pages/Bom/Detail',
        },
        // BOM结构清单详情
        {
          path: '/amtc/bom/detail/:id',
          priority: 100,
          models: ['@/models/bom'],
          component: '@/pages/Bom/Detail',
        },
        // BOM结构清单详情
        {
          path: '/amtc/bom/createChild/:parentId/:parentName',
          priority: 100,
          models: ['@/models/bom'],
          component: '@/pages/Bom/Detail',
        },
        // 导入BOM结构清单
        {
          authorized: true,
          path: '/amtc/bom/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 设备/资产变更记录
     */
    {
      path: '/aafm/change-logs',
      priority: 100,
      component: '@/pages/ChangeLogs',
    },
    /**
     * 导览工作台
     */
    {
      path: '/agwb/guide-workbench',
      priority: 100,
      component: '@/pages/GuidedWorkbench',
    },
    /**
     * 定检工作台
     */
    {
      path: '/aori/regular-check-workbench',
      priority: 100,
      routes: [
        // 定检工作台列表
        {
          path: '/aori/regular-check-workbench/list',
          priority: 100,
          component: '@/pages/RegularCheckWorkbench',
          models: ['@/models/frameWork'],
        },
        // 定检工作台结果更新
        {
          path: '/aori/regular-check-workbench/results-update',
          priority: 100,
          component: '@/pages/RegularCheckWorkbench/ResultsUpdate',
        },
        // 定检工作台批量更新
        {
          path: '/aori/regular-check-workbench/batch-update',
          priority: 100,
          component: '@/pages/RegularCheckWorkbench/BatchUpdate',
        },
        // 定检更新结果查看
        {
          path: '/aori/regular-check-workbench/results-view',
          priority: 100,
          component: '@/pages/RegularCheckWorkbench/ResultsView',
        },
      ],
    },
    // 定检通知
    {
      path: '/aori/regular-check-notice',
      priority: 100,
      routes: [
        {
          path: '/aori/regular-check-notice/list',
          priority: 100,
          component: '@/pages/RegularCheckNotice',
        },
      ],
    },
    /**
     * 标准资产
     */
    {
      path: '/aori/standard-asset',
      priority: 100,
      // 标准资产列表
      routes: [
        {
          path: '/aori/standard-asset/list',
          priority: 100,
          component: '@/pages/StandardAsset',
        },
        // 标准资产详情
        {
          path: '/aori/standard-asset/detail/:id',
          priority: 100,
          component: '@/pages/StandardAsset/Detail',
        },
        // 标准资产创建
        {
          path: '/aori/standard-asset/create',
          priority: 100,
          component: '@/pages/StandardAsset/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/aori/standard-asset/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    // 工艺装备
    {
      path: '/aori/tooling',
      priority: 100,
      routes: [
        {
          path: '/aori/tooling/list',
          priority: 100,
          component: '@/pages/Tooling',
        },
        {
          path: '/aori/tooling/detail/:id',
          priority: 100,
          component: '@/pages/Tooling/Detail',
        },
        {
          path: '/aori/tooling/create',
          priority: 100,
          component: '@/pages/Tooling/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/aori/tooling/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    // 设备出入库平台
    {
      path: '/aori/eqp-storage-platform',
      priority: 100,
      // title: '设备出入库平台',
      // authorized: true,
      component: '@/pages/EqpStoragePlatform',
    },
    // /**
    //  * <==============   ALM-资产验收  =================>
    //  */

    // /**
    //  * 采购订单
    //  */
    {
      path: '/aori/purchase-order',
      priority: 100,
      routes: [
        // 采购订单列表
        {
          path: '/aori/purchase-order/list',
          priority: 100,
          component: '@/pages/PurchaseOrder',
        },
        // 采购订单创建
        {
          path: '/aori/purchase-order/create',
          priority: 100,
          component: '@/pages/PurchaseOrder/Detail',
        },
        // 采购订单详情
        {
          path: '/aori/purchase-order/detail/:id/:num',
          priority: 100,
          component: '@/pages/PurchaseOrder/Detail',
        },
      ],
    },
    /**
     * 交付清单行
     */
    {
      path: '/arcv/delivery-list',
      priority: 100,
      // 交付清单行列表
      routes: [
        {
          path: '/arcv/delivery-list/list',
          priority: 100,
          component: '@/pages/DeliveryList',
        },
        // 交付清单行详情
        {
          path: '/arcv/delivery-list/detail/:id',
          priority: 100,
          component: '@/pages/DeliveryList/Detail',
        },
        // 交付清单行创建
        {
          path: '/arcv/delivery-list/create',
          priority: 100,
          component: '@/pages/DeliveryList/Detail',
        },
      ],
    },
    /**
     * 验收单
     */
    {
      path: '/arcv/acceptance',
      priority: 100,
      // 验收单列表
      routes: [
        {
          path: '/arcv/acceptance/list',
          priority: 100,
          component: '@/pages/Acceptance',
        },
        // 验收单详情
        {
          path: '/arcv/acceptance/detail/:id',
          priority: 100,
          component: '@/pages/Acceptance/Detail',
        },
        // 验收单创建
        {
          path: '/arcv/acceptance/create',
          priority: 100,
          component: '@/pages/Acceptance/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-资产跟踪  =================>
     */
    /**
     * 五种资产事务处理-新   资产处置
     */
    {
      path: '/aatn/asset-transaction-basic-type/dispose',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/dispose/list',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/dispose/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/dispose/create',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/dispose/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/dispose/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产变更
     */
    {
      path: '/aatn/asset-transaction-basic-type/change',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/change/list',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/change/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/change/create',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/change/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/change/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产借出归还
     */
    {
      path: '/aatn/asset-transaction-basic-type/handover',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/handover/list',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/handover/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/handover/create',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    // 资产领用
    {
      path: '/aatn/asset-transaction-basic-type/receipt',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/receipt/list',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/receipt/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/receipt/create',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/handover/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/handover/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产报废
     */
    {
      path: '/aatn/asset-transaction-basic-type/scrap',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/scrap/list',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/scrap/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/scrap/create',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/scrap/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/scrap/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail',
    },
    /**
     * 五种资产事务处理-新   资产调拨
     */
    {
      path: '/aatn/asset-transaction-basic-type/transfer',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/transfer/list',
          priority: 100,
          component: '@/pages/AssetTransactions/Transfer/List',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/transfer/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactions/Transfer/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/transfer/create',
          priority: 100,
          component: '@/pages/AssetTransactions/Transfer/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/transfer/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactions/Transfer/Detail/',
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/transfer/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactions/Transfer/Detail',
    },
    /**
     * 资产闲置
     */
    {
      path: '/aatn/asset-transaction-basic-type/idle',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/idle/list',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/idle/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
        // 事务处理新建
        {
          path: '/aatn/asset-transaction-basic-type/idle/create',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/idle/detail/:tsTypeId/:id',
      priority: 100,
      models: [],
      component: '@/pages/AssetTransactionBasicType/Detail/',
    },

    {
      authorized: true,
      path: '/pub/aatn/asset-transaction-basic-type/idle/detail/:tsTypeId/:id',
      priority: 100,
      component: '@/pages/AssetTransactionBasicType/Detail',
    },

    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/dispose',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/dispose/list',
          priority: 100,
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/dispose/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/change',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/change/list',
          priority: 100,
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/change/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/handover',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/handover/list',
          priority: 100,
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/handover/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/scrap',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/scrap/list',
          priority: 100,
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/scrap/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/transfer',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/transfer/list',
          priority: 100,
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/transfer/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactions/Transfer/Detail',
        },
      ],
    },
    {
      path: '/aatn/asset-transaction-basic-type/execute-handle/idle',
      priority: 100,
      // 资产事务处理类型列表
      routes: [
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/idle/list',
          priority: 100,
          models: ['@/models/assetTransacionBasicType'],
          component: '@/pages/AssetTransactionBasicType/ExecuteHandle',
        },
        // 事务处理详情
        {
          path: '/aatn/asset-transaction-basic-type/execute-handle/idle/detail/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/Detail',
        },
      ],
    },

    // 资产事务-审批表单
    {
      authorized: true,
      path: '/pub/asset-transaction',
      priority: 100,
      key: '/pub/asset-transaction',
      routes: [
        // 调拨转移审批表单
        {
          authorized: true,
          path: '/pub/asset-transaction/transfer/:id',
          priority: 100,
          component: '@/pages/AssetTransactions/Transfer/ApprovalForm',
        },
        {
          authorized: true,
          path: '/pub/asset-transaction/:tsTypeId/:id',
          priority: 100,
          component: '@/pages/AssetTransactionBasicType/ApprovalForm',
        },
      ],
    },
    /**
     * <==============   ALM-资产盘点  =================>
     */
    /**
     * 资产盘点计划
     */
    {
      path: '/actn/counting-batchs',
      priority: 100,
      authorized: true,
      routes: [
        {
          path: '/actn/counting-batchs/list',
          priority: 100,
          component: '@/pages/CountingBatch',
        },
        {
          path: '/actn/counting-batchs/create',
          priority: 100,
          component: '@/pages/CountingBatch/Detail',
        },
        {
          path: '/actn/counting-batchs/detail/:id',
          priority: 100,
          component: '@/pages/CountingBatch/Detail',
        },
      ],
    },
    /**
     * 盘点任务
     */
    {
      path: '/actn/counting-tasks',
      priority: 100,
      routes: [
        // 盘点任务列表
        {
          path: '/actn/counting-tasks/list',
          priority: 100,
          models: ['@/models/countingTasks'],
          component: '@/pages/CountingTask',
        },
        // 盘点任务详情
        {
          path: '/actn/counting-tasks/detail/:id',
          priority: 100,
          models: ['@/models/countingTasks', '@/models/countingLines'],
          component: '@/pages/CountingTask/Detail',
        },
        // 盘点任务创建
        {
          path: '/actn/counting-tasks/create',
          priority: 100,
          models: ['@/models/countingTasks'],
          component: '@/pages/CountingTask/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-固定资产  =================>
     */
    /**
     * 固定资产
     */
    {
      path: '/afam/fixed-assets',
      priority: 100,
      // 固定资产列表
      routes: [
        {
          path: '/afam/fixed-assets/list',
          priority: 100,
          component: '@/pages/FixedAssets',
        },
        // 固定资产详情
        {
          path: '/afam/fixed-assets/detail/:id',
          priority: 100,
          component: '@/pages/FixedAssets/Detail',
        },
        // 固定资产创建
        {
          path: '/afam/fixed-assets/create',
          priority: 100,
          component: '@/pages/FixedAssets/Detail',
        },
      ],
    },
    /**
     * 固定资产账簿
     */
    {
      path: '/aafm/fa-account-book',
      priority: 100,
      routes: [
        // 固定资产账簿列表
        {
          path: '/aafm/fa-account-book/list',
          priority: 100,
          models: ['@/models/faAccountBook'],
          component: '@/pages/FaAccountBook',
        },
        // 新建
        {
          path: '/aafm/fa-account-book/create',
          priority: 100,
          models: ['@/models/faAccountBook'],
          component: '@/pages/FaAccountBook/Detail',
        },
        // 详情查询id
        {
          path: '/aafm/fa-account-book/detail/:id',
          priority: 100,
          models: ['@/models/faAccountBook'],
          component: '@/pages/FaAccountBook/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-维修/维保  =================>
     */
    /**
     * 计划调度中心
     */
    {
      path: '/aori/schedule-center',
      priority: 100,
      models: ['@/models/frameWork'],
      component: '@/pages/ScheduleCenter',
    },
    /**
     * 服务中心
     */
    {
      path: '/amtc/service-center',
      priority: 100,
      routes: [
        // 服务中心列表
        {
          path: '/amtc/service-center/list',
          priority: 100,
          models: ['@/models/serviceApply'],
          component: '@/pages/ServiceCenter',
        },
        // 服务中心创建
        {
          path: '/amtc/service-center/create',
          priority: 100,
          models: ['@/models/serviceApply', '@/models/frameWork', '@/models/woMalfunction'],
          component: '@/pages/ServiceCenter/Detail',
        },
        // 服务中心详情
        {
          path: '/amtc/service-center/Detail/:srId',
          priority: 100,
          models: ['@/models/serviceApply', '@/models/frameWork', '@/models/woMalfunction'],
          component: '@/pages/ServiceCenter/Detail',
        },
      ],
    },
    /**
     * 我的服务申请
     */
    {
      path: '/amtc/service-apply-current',
      priority: 100,
      routes: [
        // 我的服务申请列表
        {
          path: '/amtc/service-apply-current/list',
          priority: 100,
          component: '@/pages/ServiceApplyCurrent',
        },
        // 我的服务申请新建
        {
          path: '/amtc/service-apply-current/create',
          priority: 100,
          models: ['@/models/serviceApply', '@/models/woMalfunction', '@/models/frameWork'],
          component: '@/pages/ServiceApplyCurrent/Detail',
        },
        // 我的服务申请编辑
        {
          path: '/amtc/service-apply-current/detail/:srId',
          priority: 100,
          models: ['@/models/serviceApply', '@/models/woMalfunction', '@/models/frameWork'],
          component: '@/pages/ServiceApplyCurrent/Detail',
        },
      ],
    },
    // 服务申请单-审批表单
    {
      authorized: true,
      path: '/pub/service-apply',
      priority: 100,
      key: '/pub/service-apply',
      routes: [
        {
          authorized: true,
          path: '/pub/service-apply/:id',
          priority: 100,
          component: '@/pages/ServiceApplyCurrent/ApprovalForm',
        },
      ],
    },
    /**
     * 工作单
     */
    {
      path: '/amtc/work-order',
      priority: 100,
      routes: [
        // 工作单列表
        {
          path: '/amtc/work-order/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder',
        },
        // 工作单创建
        {
          path: '/amtc/work-order/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder/Detail',
        },
        // 工作单详情
        {
          path: '/amtc/work-order/detail/:woId/:woNum',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder/Detail',
        },
      ],
    },
    // 工作单-审批表单
    {
      authorized: true,
      path: '/pub/workorder',
      priority: 100,
      key: '/pub/workorder',
      routes: [
        {
          authorized: true,
          path: '/pub/workorder/:woId',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/WorkOrder/ApprovalForm',
        },
      ],
    },
    /**
     * 委外申请
     */
    {
      path: '/amtc/sub-requisition',
      priority: 100,
      routes: [
        // 委外申请列表
        {
          path: '/amtc/sub-requisition/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/SubRequisition',
        },
        // 委外申请新建
        {
          path: '/amtc/sub-requisition/create',
          priority: 100,
          models: [
            '@/models/frameWork',
            '@/models/serviceApply',
            '@/models/equipmentAsset',
            '@/models/location',
          ],
          component: '@/pages/SubRequisition/Detail',
        },
        // 委外申请详情
        {
          path: '/amtc/sub-requisition/Detail/:woId',
          priority: 100,
          models: ['@/models/frameWork', '@/models/serviceApply'],
          component: '@/pages/SubRequisition/Detail',
        },
      ],
    },
    // 委外申请-审批表单
    {
      authorized: true,
      path: '/pub/sub-requisition',
      priority: 100,
      key: '/pub/sub-requisition',
      routes: [
        {
          authorized: true,
          path: '/pub/sub-requisition/:id',
          priority: 100,
          models: ['@/models/frameWork', '@/models/serviceApply'],
          component: '@/pages/SubRequisition/ApprovalForm',
        },
      ],
    },
    /**
     *  仪表点
     */
    {
      path: '/amtr/meters',
      priority: 100,
      routes: [
        {
          path: '/amtr/meters/list',
          priority: 100,
          component: '@/pages/Meters',
        },
        {
          path: '/amtr/meters/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Meters/Detail',
        },
        {
          path: '/amtr/meters/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Meters/Detail',
        },
        // 导入读数
        {
          authorized: true,
          path: '/amtr/meters/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 告警工作台
     */
    {
      path: '/aori/alarm-record',
      priority: 100,
      routes: [
        {
          path: '/aori/alarm-record/list',
          priority: 100,
          component: '@/pages/AlarmRecord',
        },
      ],
    },
    /**
     * 维保作业项
     */
    {
      path: '/amtc/maintain-operation',
      priority: 100,
      routes: [
        // 维保作业项列表
        {
          path: '/amtc/maintain-operation/list',
          priority: 100,
          component: '@/pages/MaintainOperation/List',
        },
        // 创建
        {
          path: '/amtc/maintain-operation/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainOperation/Detail',
        },
        // 详情
        {
          path: '/amtc/maintain-operation/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainOperation/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/amtc/maintain-operation/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 维保规则
     */
    {
      path: '/amtc/strategy-rule',
      priority: 100,
      routes: [
        // 策略规则列表
        {
          path: '/amtc/strategy-rule/list',
          priority: 100,
          component: '@/pages/MaintainRule/List',
        },
        // 策略规则创建
        {
          path: '/amtc/strategy-rule/create',
          priority: 100,
          component: '@/pages/MaintainRule/Detail',
        },
        // 策略规则详情
        {
          path: '/amtc/strategy-rule/detail/:id',
          priority: 100,
          component: '@/pages/MaintainRule/Detail',
        },
        // 策略规则导入
        {
          authorized: true,
          path: '/amtc/strategy-rule/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 维保周期
     */
    {
      path: '/amtc/maintain-cycle',
      priority: 100,
      routes: [
        // 点巡检预测列表
        {
          path: '/amtc/maintain-cycle/list',
          priority: 100,
          component: '@/pages/MaintainCycle/List',
        },
        // 创建
        {
          path: '/amtc/maintain-cycle/create',
          priority: 100,
          component: '@/pages/MaintainCycle/Detail',
        },
        // 详情
        {
          path: '/amtc/maintain-cycle/detail/:id',
          priority: 100,
          component: '@/pages/MaintainCycle/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/amtc/maintain-cycle/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 点巡检预测
     */
    {
      path: '/amtc/pmf',
      priority: 100,
      routes: [
        // 点巡检预测列表
        {
          path: '/amtc/pmf/list',
          priority: 100,
          component: '@/pages/PMForecasts',
        },
        // 创建
        {
          path: '/amtc/pmf/create',
          priority: 100,
          component: '@/pages/PMForecasts/Detail',
        },
        // 详情
        {
          path: '/amtc/pmf/detail/:id',
          priority: 100,
          component: '@/pages/PMForecasts/Detail',
        },
      ],
    },
    /**
     * 维保计划
     */
    {
      path: '/aori/maintain-plans',
      priority: 100,
      routes: [
        {
          path: '/aori/maintain-plans/list',
          priority: 100,
          component: '@/pages/MaintainPlans',
        },
        {
          path: '/aori/maintain-plans/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainPlans/Detail',
        },
        {
          path: '/aori/maintain-plans/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/MaintainPlans/Detail',
        },
        // 导入
        {
          authorized: true,
          path: '/aori/maintain-plans/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 计划预测
     */
    {
      path: '/aori/plan-sched',
      priority: 100,
      routes: [
        {
          path: '/aori/plan-sched/list',
          priority: 100,
          component: '@/pages/PlanSched',
        },
        {
          path: '/aori/plan-sched/create',
          priority: 100,
          component: '@/pages/PlanSched/Detail',
        },
        {
          path: '/aori/plan-sched/detail/:id',
          priority: 100,
          component: '@/pages/PlanSched/Detail',
        },
      ],
    },
    /**
     * 策略场景切换
     */
    {
      path: '/amtc/policy-scene-switch',
      priority: 100,
      routes: [
        // 策略场景切换列表
        {
          path: '/amtc/policy-scene-switch/list',
          priority: 100,
          component: '@/pages/PolicySceneSwitch',
        },
        // 历史查询
        {
          path: '/amtc/policy-scene-switch/history',
          priority: 100,
          component: '@/pages/PolicySceneSwitch/History',
        },
        // 详情查询id
        {
          path: '/amtc/policy-scene-switch/detail/:id',
          priority: 100,
          component: '@/pages/PolicySceneSwitch/Detail',
        },
        // 详情查询
        {
          path: '/amtc/policy-scene-switch/detail/query',
          priority: 100,
          component: '@/pages/PolicySceneSwitch/Detail',
        },
      ],
    },
    /**
     * 维保策略场景
     */
    {
      path: '/amtc/strategies-scene',
      priority: 100,
      models: ['@/models/strategiesScene'],
      component: '@/pages/StrategiesScene',
    },
    /**
     * <==============   ALM-库存物料  =================>
     */
    /**
     * 仓库工作台
     */
    {
      path: '/ammt/material_ts_Multi',
      priority: 100,
      routes: [
        // 仓库工作台创建
        {
          path: '/ammt/material_ts_Multi/create',
          priority: 100,
          models: ['@/models/materialTsMulti'],
          component: '@/pages/MaterialTsMulti/Create',
        },
        // 仓库工作台列表
        {
          path: '/ammt/material_ts_Multi/list',
          priority: 100,
          models: ['@/models/materialTsMulti'],
          component: '@/pages/MaterialTsMulti/List',
        },
        // 物料凭证详情
        {
          path: '/ammt/material_ts_Multi/detail/:id',
          priority: 100,
          models: ['@/models/materialTsMulti', '@/models/materialTsType'],
          component: '@/pages/MaterialTsMulti/Detail',
        },
      ],
    },
    /**
     * 库存历史变动
     */
    {
      path: '/ammt/trans-history',
      priority: 100,
      routes: [
        // 库存历史变动列表
        {
          path: '/ammt/trans-history/list',
          priority: 100,
          models: ['@/models/transHistory'],
          component: '@/pages/TransHistory',
        },
      ],
    },
    /**
     * 库存现有量
     */
    {
      path: '/ammt/onhand-quantity',
      priority: 100,
      routes: [
        // 现有量列表
        {
          path: '/ammt/onhand-quantity/list',
          priority: 100,
          component: '@/pages/OnHandQuantity',
        },
      ],
    },
    /**
     * 物料
     */
    {
      path: '/ammt/materials',
      priority: 100,
      routes: [
        // 物料列表
        {
          path: '/ammt/materials/list',
          priority: 100,
          component: '@/pages/Materials',
        },
        // 物料创建
        {
          path: '/ammt/materials/create',
          priority: 100,
          component: '@/pages/Materials/Detail',
        },
        // 物料详情
        {
          path: '/ammt/materials/detail/:id',
          priority: 100,
          component: '@/pages/Materials/Detail',
        },
        // 物料导入
        {
          authorized: true,
          path: '/ammt/materials/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 物料申请单
     */
    {
      path: '/ammt/item-requisition',
      priority: 100,
      routes: [
        // 物料申请单列表
        {
          path: '/ammt/item-requisition/list',
          priority: 100,
          component: '@/pages/ItemRequisition',
        },
        // 物料申请单创建
        {
          path: '/ammt/item-requisition/create',
          priority: 100,
          component: '@/pages/ItemRequisition/Detail',
        },
        // 物料申请单详情
        {
          path: '/ammt/item-requisition/detail/:id',
          priority: 100,
          component: '@/pages/ItemRequisition/Detail',
        },
      ],
    },
    // 物料申请单-审批表单
    {
      authorized: true,
      path: '/pub/item-requisition',
      priority: 100,
      key: '/pub/item-requisition',
      routes: [
        {
          authorized: true,
          path: '/pub/item-requisition/:id',
          priority: 100,
          component: '@/pages/ItemRequisition/ApprovalForm',
        },
      ],
    },
    /**
     * 物料采购建议
     */
    {
      path: '/amtc/material-purchase-adv',
      priority: 100,
      routes: [
        {
          path: '/amtc/material-purchase-adv/list',
          priority: 100,
          component: '@/pages/MaterialPurchaseAdv',
        },
      ],
    },
    /**
     * <==============   ALM-项目管理  =================>
     */
    /**
     * 项目工作台
     */
    {
      path: '/appm/pro-basic-info',
      priority: 100,
      routes: [
        // 项目基础信息列表
        {
          path: '/appm/pro-basic-info/list',
          priority: 100,
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo',
        },
        // 项目基础信息创建
        {
          path: '/appm/pro-basic-info/create',
          priority: 100,
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // 项目基础信息创建-复制自项目
        {
          path: '/appm/pro-basic-info/create-from/project/:id',
          priority: 100,
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // 项目基础信息创建-复制自模板
        {
          path: '/appm/pro-basic-info/template/project/:id',
          priority: 100,
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // 项目基础信息详情
        {
          path: '/appm/pro-basic-info/detail/:id',
          priority: 100,
          models: ['@/models/proBasicInfo'],
          component: '@/pages/ProBasicInfo/Detail',
        },
        // wbs计划维护
        {
          path: '/appm/pro-basic-info/wbs/:wbsHeaderId',
          priority: 100,
          models: ['@/models/wbsPlanMaintain'],
          component: '@/pages/WBSPlanMaintain',
        },
      ],
    },
    /**
     * 项目进度
     */
    {
      path: '/appm/project-schedule',
      priority: 100,
      models: ['@/models/projectSchedule'],
      component: '@/pages/ProjectSchedule',
    },
    /**
     * 项目模板
     */
    {
      path: '/appm/project-template',
      priority: 100,
      routes: [
        // 项目模板列表
        {
          path: '/appm/project-template/list',
          priority: 100,
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate',
        },
        {
          path: '/appm/project-template/detail/:id',
          priority: 100,
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate/Detail',
        },
        {
          path: '/appm/project-template/new-detail/:id',
          priority: 100,
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate/Detail',
        },
        {
          path: '/appm/project-template/create',
          priority: 100,
          models: ['@/models/projectTemplate'],
          component: '@/pages/ProjectTemplate/Detail',
        },
        // WBS结构模板
        {
          path: '/appm/project-template/task/:proTemplateId',
          priority: 100,
          models: ['@/models/taskTemplate'],
          component: '@/pages/TaskTemplate',
        },
      ],
    },
    /**
     * <==============   ALM-服务协议  =================>
     */
    // 服务协议类型
    {
      path: '/amtc/service-agreemt-type',
      priority: 100,
      routes: [
        {
          path: '/amtc/service-agreemt-type/list',
          priority: 100,
          component: '@/pages/ServiceAgreemtType',
        },
        {
          path: '/amtc/service-agreemt-type/create',
          priority: 100,
          component: '@/pages/ServiceAgreemtType/Detail',
        },
        {
          path: '/amtc/service-agreemt-type/detail/:agreementTypeId',
          priority: 100,
          component: '@/pages/ServiceAgreemtType/Detail',
        },
      ],
    },
    // 服务协议
    {
      path: '/amtc/service-agreemt',
      priority: 100,
      routes: [
        {
          path: '/amtc/service-agreemt/list',
          priority: 100,
          component: '@/pages/ServiceAgreemt',
        },
        {
          path: '/amtc/service-agreemt/create',
          priority: 100,
          component: '@/pages/ServiceAgreemt/Detail',
        },
        {
          path: '/amtc/service-agreemt/detail/:serviceAgreementId',
          priority: 100,
          component: '@/pages/ServiceAgreemt/Detail',
        },
      ],
    },
    /**
     * <==============   ALM-点巡检  =================>
     */
    /**
     * 路线
     */
    {
      path: '/aori/route',
      priority: 100,
      routes: [
        {
          path: '/aori/route/list',
          priority: 100,
          component: '@/pages/Route',
        },
        {
          path: '/aori/route/create',
          priority: 100,
          component: '@/pages/Route/Detail',
        },
        {
          path: '/aori/route/detail/:id',
          priority: 100,
          component: '@/pages/Route/Detail',
        },
      ],
    },
    /**
     * 点位
     */
    {
      path: '/aori/point-info',
      priority: 100,
      routes: [
        {
          path: '/aori/point-info',
          priority: 100,
          component: '@/pages/PointInfo',
        },
        {
          authorized: true,
          path: '/aori/point/data-import/:code',
          priority: 100,
          component: '@/pages/Import/CommentImport',
        },
      ],
    },
    /**
     * 标准检查组
     */
    {
      path: '/aori/checklist-group',
      priority: 100,
      routes: [
        {
          path: '/aori/checklist-group/list',
          priority: 100,
          component: '@/pages/ChecklistGroup',
        },
        {
          path: '/aori/checklist-group/create',
          priority: 100,
          component: '@/pages/ChecklistGroup/Detail',
        },
        {
          path: '/aori/checklist-group/detail/:id',
          priority: 100,
          component: '@/pages/ChecklistGroup/Detail',
        },
      ],
    },
    /**
     * 点巡检类型
     */
    {
      path: '/aori/psc-type',
      priority: 100,
      routes: [
        // 点巡检类型列表
        {
          path: '/aori/psc-type/list',
          priority: 100,
          models: ['@/models/woType'],
          component: '@/pages/PscType',
        },
        // 点巡检类型创建
        {
          path: '/aori/psc-type/create',
          priority: 100,
          models: ['@/models/woType'],
          component: '@/pages/PscType/Detail',
        },
        // 点巡检类型详情
        {
          path: '/aori/psc-type/detail/:id',
          priority: 100,
          models: ['@/models/woType', '@/models/inspectList'],
          component: '@/pages/PscType/Detail',
        },
      ],
    },
    /**
     * 点巡检单
     */
    {
      path: '/aori/patrol-spot-check',
      priority: 100,
      routes: [
        {
          path: '/aori/patrol-spot-check/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PatrolSpotCheck',
        },
        {
          path: '/aori/patrol-spot-check/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PatrolSpotCheck/Detail',
        },
        {
          path: '/aori/patrol-spot-check/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PatrolSpotCheck/Detail',
        },
      ],
    },
    /**
     * 点巡检计划
     */
    {
      path: '/aori/psc-plan',
      priority: 100,
      routes: [
        {
          path: '/aori/psc-plan/list',
          priority: 100,
          component: '@/pages/PscPlan',
        },
        {
          path: '/aori/psc-plan/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PscPlan/Detail',
        },
        {
          path: '/aori/psc-plan/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PscPlan/Detail',
        },
      ],
    },

    /**
     * <==============   ALM-采购申请  =================>
     */
    /**
     * 采购申请
     */
    {
      path: '/aori/pr',
      priority: 100,
      routes: [
        {
          path: '/aori/pr/list',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder',
        },
        {
          path: '/aori/pr/create',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder/Detail',
        },
        {
          path: '/aori/pr/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder/Detail',
        },
      ],
    },
    // 采购申请-审批表单
    {
      authorized: true,
      path: '/pub/pr',
      priority: 100,
      key: '/pub//pr',
      routes: [
        {
          authorized: true,
          path: '/pub/pr/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/PROrder/ApprovalForm',
        },
      ],
    },
    /**
     * 权限
     */
    {
      path: '/aori/permission-config',
      priority: 100,
      routes: [
        {
          path: '/aori/permission-config/list',
          priority: 100,
          component: '@/pages/Permission',
        },
        {
          path: '/aori/permission-config/detail/:id',
          priority: 100,
          models: ['@/models/frameWork'],
          component: '@/pages/Permission/Detail',
        },
      ],
    },
    /**
     * 权限检查
     */
    {
      path: '/aori/permission-check',
      priority: 100,
      component: '@/pages/PermissionCheck',
    },
  ],

  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          // components: 'hzero-front/lib/components',
          // utils: 'hzero-front/lib/utils',
          // services: 'hzero-front/lib/services',
          // '@': './src',
          alm: './src',
          // halmComponents: 'halm-front/lib/components',
          // halmUtils: 'halm-front/lib/utils',
          // halmServices: 'halm-front/lib/services',
        },
      },
    ],
  ],

  presets: [
    // require.resolve('@hzerojs/preset-hzero'),根目录下已经配置过了
  ],
  hzeroMicro: {
    microConfig: {
      // registerRegex: '(\\/halm)|(\\/orderPerfomace)|(\\/pub/halm)',
      // 原先在.hzerorc.json使用的是"initLoad": true,现在使用的模块加载规则：当匹配到对应的路由后，才加载对应的模块。
      // 主模块下microServices下registerRegex优先级最高
    },
    mfExposes: {
      '_hzero_layout_cards:ALM.ALARM_STATISTICS_CARD': '@/cards/AlarmStatisticsCard',
      '_hzero_layout_cards:ALM.ASSET_HANDING_CARD': '@/cards/AssetHandingCard',
      '_hzero_layout_cards:ALM.ASSET_QUANTITY_CARD': '@/cards/AssetQuantityCard',
      '_hzero_layout_cards:ALM.ASSET_REPORT_CARD': '@/cards/AssetReportCard',
      '_hzero_layout_cards:ALM.ASSET_REPORT_ONE_CARD': '@/cards/AssetReportOneCard',
      '_hzero_layout_cards:ALM.ASSET_REPORT_TWO_CARD': '@/cards/AssetReportTwoCard',
      '_hzero_layout_cards:ALM.ASSET_USAGE_CARD': '@/cards/AssetUsageCard',
      '_hzero_layout_cards:ALM.FREE_ASSETS_CARD': '@/cards/FreeAssetsCard',
      '_hzero_layout_cards:ALM.IDLE_ASSET_CARD': '@/cards/IdleAssetCard',
      '_hzero_layout_cards:ALM.PERSONAL_KPI_CARD': '@/cards/PersonalKpiCard',
      '_hzero_layout_cards:ALM.QUICK_OPERA_CARD': '@/cards/QuickOperaCard',
      '_hzero_layout_cards:ALM.RUN_STATE_CARD': '@/cards/RunStateCard',
      '_hzero_layout_cards:ALM.TASK_ANALYSIS_CARD': '@/cards/TaskAnalysisCard',
      '_hzero_layout_cards:ALM.TODO_ORDER_CARD': '@/cards/TodoOrderCard',
      '_hzero_layout_cards:ALM.TODO_TABLE_CARD': '@/cards/TodoTableCard',
      '_hzero_layout_cards:ALM.WARRANTY_COUNT_CARD': '@/cards/WarrantyCountCard',
    },
  },
});
