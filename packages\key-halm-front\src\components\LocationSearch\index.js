/**
 * 详细地址查询组件 - 使用百度地图
 *
 * 客户化参数如下:
 * @param ds 地址字段关联的ds
 * @param fieldName 地址字段在ds中的name
 * @param isNew 是否新建
 * @param isEdit 是否编辑
 */
import React from 'react';
import { isFunction } from 'lodash';
import { TextField, Form, Output } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';

import MapModal from './MapModal';

export default class LocationSearch extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      mapModalVisible: false, // 地图modal是否可见
    };
  }

  @Bind
  openModal() {
    this.setState({
      mapModalVisible: true,
    });
  }

  @Bind()
  closeModal() {
    this.setState({
      mapModalVisible: false,
    });
  }

  /**
   * 修改位置信息
   * @param addressData 地址信息
   */
  @Bind()
  changeAddress(addressData = {}) {
    const { ds, isString, onChangeRegionValue } = this.props;
    const { address, province, city, district, lng, lat, zoom, region } = addressData;
    const regionIds = region ? region.join(',') : null;
    const regionPara = isString ? region.map(i => `${i}`) : region;
    ds.current.set('address', address);
    ds.current.set('lng', lng);
    ds.current.set('lat', lat);
    ds.current.set('zoom', zoom);

    ds.current.set('region', region);
    ds.current.set('regionIds', regionIds);
    ds.current.set('regionName', `${province}/${city}/${district}`);
    if (isFunction(onChangeRegionValue)) {
      // 改变 省份/城市/区县 字段控件的显示
      onChangeRegionValue(regionPara);
    }
  }

  render() {
    const { fieldName = '', ds, isEdit = false, isNew = false } = this.props;
    const { mapModalVisible } = this.state;
    const address = ds.current.get('address');
    const pcd = ds.current.get('regionName') || ''; // 省市区 格式：湖南省/长沙市/市辖区
    const pcdArr = pcd.split('/');
    const [province = '', city = '', district = ''] = pcdArr;

    const lng = ds.current.get('lng') || '';
    const lat = ds.current.get('lat') || '';
    const zoom = ds.current.get('zoom');
    const modalProps = {
      onClose: this.closeModal,
      onChangeAddress: this.changeAddress,
      locationData: {
        address, // 详细地址
        province, // 省
        city, // 市
        district, // 区
        lng,
        lat,
        zoom,
      },
    };

    return (
      <React.Fragment>
        {isNew || isEdit ? (
          <Form dataSet={ds} columns={3} className="edit-row">
            <div name={fieldName} colSpan={2} newLine>
              <TextField name={fieldName} style={{ width: 'calc(100% - 18px)' }} />
              <Icon type="edit_location" onClick={this.openModal} />
            </div>
          </Form>
        ) : (
          <Form dataSet={ds} columns={3} className="read-row">
            <Output name={fieldName} />
          </Form>
        )}
        {mapModalVisible && <MapModal {...modalProps} />}
      </React.Fragment>
    );
  }
}
