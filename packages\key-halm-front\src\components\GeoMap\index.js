/**
 * 高德地图组件
 * 官方文档地址:https://elemefe.github.io/react-amap/articles/start
 *
 * 客户化参数如下:
 * @param moduleName 必输，模块名称
 * @param moduleId 必输，模块Id
 * @param onSetMarkerLocation 非必输，回调函数，参数为坐标
 * @param hideEditMarkerBtn 非必输，隐藏按钮组
 */
import React from 'react';
import { Bind } from 'lodash-decorators';
import { Map, Marker } from 'react-amap';
// import Switch from 'components/Switch';
import { getResponse, getCurrentOrganizationId } from 'utils/utils';
import { BDToGCJ } from '../../utils/mapUtils';
import { saveMarker, searchMarker, deleteMarker } from '../../services/api';
import signImgurl from '../../assets/mark-location.png';
import unsignImgurl from '../../assets/unmarked-location.png';

export default class GeoMap extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // amapkey: "1ce5f4aef24df9b3315632813c7fcaa2", // 这个是高德地图给开发者分配的开发者 Key；可以在高德开放平台申请，此处和老版资产云一致
      // what: '点击下方按钮进行标记',
      // currentPosition: '',
      showDetailFlag: false,
      createMarkerFlag: false,
      markerPosition: {},
      markerObj: {},
    };
    this.markerEvents = {
      // created: (instance) => {
      //   console.log('Marker 实例创建成功；如果你需要对原生实例进行操作，可以从这里开始；');
      //   console.log(instance);
      // },
      click: () => {
        // this.clickMarker(e);
        this.setState({ showDetailFlag: !this.state.showDetailFlag });
      },
      // dblclick: (e) => {
      //   console.log("你刚刚双击了这个图标；调用参数为：");
      //   console.log(e);
      // },
    };
  }

  componentDidMount() {
    this.handleSearchMarker();
  }

  // @Bind()
  // clickMarker(e) {
  //   const {
  //     lnglat: { lng, lat },
  //   } = e;
  //   this.setState({ currentPosition: `(${lng},${lat})` });
  // }

  @Bind()
  drawMarker() {
    this.setState({
      // what: '标记坐标点',
      createMarkerFlag: true,
    });
  }

  @Bind()
  close() {
    this.setState({
      // what: '点击下方按钮进行标记',
      createMarkerFlag: false,
    });
  }

  /**
   * 清除marker
   */
  @Bind()
  clearMarker() {
    const { markerObj } = this.state;
    this.setState({ markerPosition: {}, showDetailFlag: false });
    if (markerObj.mapId) {
      getResponse(deleteMarker(markerObj));
    }
  }

  /**
   * 创建marker
   */
  @Bind()
  createMarker(obj) {
    const { onSetMarkerLocation, moduleName, moduleId } = this.props;
    const { createMarkerFlag } = this.state;
    const {
      lnglat: { lng, lat },
    } = obj;

    // this.setState({ currentPosition: `(${lng},${lat})` });
    this.setState();
    if (createMarkerFlag) {
      this.setState({ markerPosition: { longitude: lng, latitude: lat }, showDetailFlag: true });
      if (onSetMarkerLocation) {
        onSetMarkerLocation({ lng, lat });
      }
      // 调用保存接口
      if (lng && moduleId && moduleName) {
        getResponse(
          saveMarker({
            tenantId: getCurrentOrganizationId(),
            moduleName,
            moduleId,
            longitude: lng,
            latitude: lat,
            mapType: 'GaodeMap',
          })
        ).then(res => {
          if (res) {
            this.setState({
              markerObj: res,
            });
          }
        });
      }
    } else {
      // 点击地图其他地方隐藏信息框
      this.setState({ showDetailFlag: false });
    }
  }

  /**
   * 查询marker
   */
  @Bind()
  handleSearchMarker() {
    const { moduleName, moduleId } = this.props;
    if (moduleId) {
      getResponse(
        searchMarker({
          tenantId: getCurrentOrganizationId(),
          moduleName,
          moduleId,
        })
      ).then(res => {
        if (res && res.content[0]) {
          if (res.content[0].mapType !== 'GaodeMap') {
            const ll = BDToGCJ(res.content[0].longitude, res.content[0].latitude);
            this.setState({
              markerPosition: { ...ll },
              markerObj: res.content[0],
            });
          } else {
            this.setState({
              markerPosition: {
                longitude: res.content[0].longitude,
                latitude: res.content[0].latitude,
              },
              markerObj: res.content[0],
            });
          }
        }
      });
    }
  }

  @Bind()
  drawMarkerSwitchChange() {
    if (!this.state.createMarkerFlag) {
      this.drawMarker();
    } else {
      this.close();
    }
  }

  @Bind()
  cilckMarker() {
    this.setState({ showDetailFlag: !this.state.showDetailFlag });
  }

  render() {
    const { hideEditMarkerBtn } = this.props;
    const {
      // amapkey,
      createMarkerFlag,
      // what,
      // currentPosition,
      markerPosition,
      showDetailFlag,
    } = this.state;
    const editMarkerBtnStyle = hideEditMarkerBtn ? { display: 'none' } : {};
    const layerStyle = {
      padding: '10px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '4px',
      position: 'absolute',
      top: '10px',
      left: '10px',
    };
    const popStyle = {
      padding: '10px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '4px',
      minWidth: '160px',
      minHeight: '65px',
    };
    const events = {
      // created: (ins) => {
      //   console.log(ins);
      // },
      click: obj => {
        this.createMarker(obj);
      },
    };

    return (
      <React.Fragment>
        <Map
          // amapkey={amapkey}
          plugins={['ToolBar']}
          events={events}
          center={markerPosition.longitude ? markerPosition : ''}
          cursor={createMarkerFlag ? 'Crosshair' : 'Pointer'}
          zoom={14}
        >
          {markerPosition.longitude ? (
            // content={`<img src=${imgurl} alt="标记地点" />`}
            // <Marker id="marker" position={markerPosition} clickable events={this.markerEvents}>
            <Marker id="marker" position={markerPosition} clickable>
              {markerPosition.longitude && (
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <img
                    style={{ width: '20px', height: '20px', marginTop: '15px' }}
                    onClick={this.cilckMarker}
                    src={signImgurl}
                    alt="标记地点"
                  />
                  {showDetailFlag && (
                    <div style={popStyle}>
                      {/* {`${currentPosition}`} */}
                      {`${markerPosition.longitude}，${markerPosition.latitude}`}
                      <br />
                      <button
                        type="button"
                        onClick={() => {
                          this.clearMarker();
                        }}
                      >
                        {' '}
                        删除标记{' '}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </Marker>
          ) : (
            ''
          )}
          <div style={layerStyle}>
            {/* {`${what} ${currentPosition}`}
            <br /> */}
            <div style={editMarkerBtnStyle}>
              {/* <Switch style={{ marginRight: 10 }} onChange={this.drawMarkerSwitchChange} /> */}
              <img
                style={{ width: '25px', height: '25px' }}
                src={createMarkerFlag ? unsignImgurl : signImgurl}
                title="标记地点"
                alt="标记地点"
                onClick={this.drawMarkerSwitchChange}
              />
            </div>
          </div>
        </Map>
      </React.Fragment>
    );
  }
}
