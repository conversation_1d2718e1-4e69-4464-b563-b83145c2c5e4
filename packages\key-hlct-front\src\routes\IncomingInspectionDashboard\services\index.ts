/*
 * @Description: 来料检验看板接口服务
 */
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';

// 接口数据类型定义
export interface ProgressStatsData {
  pending: number;
  overdue: number;
  inProgress: number;
  completed: number;
}

export interface DefectiveStatsData {
  name: string;
  count: number;
  ratio: number;
}

export interface DefectiveDetailData {
  defectiveItem: string;
  inspectionId: string;
  inspectDocId: number;
  materialId: number;
  materialCode: string;
  material: string;
  supplierId: number;
  supplierCode: string;
  supplier: string;
  inspector: string | null;
  inspectorId: number;
  creationDate: string;
}

export interface DefectiveDetailResponse {
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
  size: number;
  number: number;
  content: DefectiveDetailData[];
  empty: boolean;
}

export interface MaterialStatsData {
  materialId: number;
  materialCode: string;
  material: string;
  arrivalBatchCount: number;
  totalInspections: number;
  passedInspections: number;
  passRateNum: number;
  passRate: string;
}

export interface MaterialStatsResponse {
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
  size: number;
  number: number;
  content: MaterialStatsData[];
  empty: boolean;
}

export interface SupplierStatsData {
  supplierId: number;
  supplierCode: string;
  supplier: string;
  arrivalBatchCount: number;
  totalInspections: number;
  passedInspections: number;
  passRateNum: number;
  passRate: string;
}

export interface SupplierStatsResponse {
  totalPages: number;
  totalElements: number;
  numberOfElements: number;
  size: number;
  number: number;
  content: SupplierStatsData[];
  empty: boolean;
}

// 接口配置
const API_BASE = '/inja-qms-tznq/v1';
const getOrganizationId = () => getCurrentOrganizationId() || '25018';

// 接口请求函数
export const fetchProgressStats = async (startDate: string, endDate: string): Promise<ProgressStatsData> => {
  const organizationId = getOrganizationId();
  const url = `${API_BASE}/${organizationId}/qms-inspect-iqc-dashboards/progress-stats/ui`;
  const params = {
    startDate: `${startDate} 00:00:00`,
    endDate: `${endDate} 23:59:59`,
  };
  
  const response = await request(url, {
    method: 'GET',
    query: params,
  });
  
  return response;
};

export const fetchDefectiveStats = async (startDate: string, endDate: string): Promise<DefectiveStatsData[]> => {
  const organizationId = getOrganizationId();
  const url = `${API_BASE}/${organizationId}/qms-inspect-iqc-dashboards/defective-stats/ui`;
  const params = {
    startDate: `${startDate} 00:00:00`,
    endDate: `${endDate} 23:59:59`,
  };
  
  const response = await request(url, {
    method: 'GET',
    query: params,
  });
  
  return response;
};

export const fetchDefectiveDetails = async (
  startDate: string, 
  endDate: string, 
  defectiveItem?: string,
  pageNum: number = 0,
  pageSize: number = 20
): Promise<DefectiveDetailResponse> => {
  const organizationId = getOrganizationId();
  const url = `${API_BASE}/${organizationId}/qms-inspect-iqc-dashboards/defective-stats/details/ui`;
  const params: any = {
    startDate: `${startDate} 00:00:00`,
    endDate: `${endDate} 23:59:59`,
    size: pageSize,
    page: pageNum,
  };
  
  if (defectiveItem) {
    params.defectiveItem = defectiveItem;
  }
  
  const response = await request(url, {
    method: 'GET',
    query: params,
  });
  
  return response;
};

export const fetchMaterialStats = async (
  startDate: string, 
  endDate: string, 
  supplierId?: number,
  pageNum: number = 0,
  pageSize: number = 20
): Promise<MaterialStatsResponse> => {
  const organizationId = getOrganizationId();
  const url = `${API_BASE}/${organizationId}/qms-inspect-iqc-dashboards/material-stats/ui`;
  const params: any = {
    startDate: `${startDate} 00:00:00`,
    endDate: `${endDate} 23:59:59`,
    size: pageSize,
    page: pageNum,
  };
  
  if (supplierId) {
    params.supplierId = supplierId;
  }
  
  const response = await request(url, {
    method: 'GET',
    query: params,
  });
  
  return response;
};

export const fetchSupplierStats = async (
  startDate: string, 
  endDate: string, 
  materialId?: number,
  pageNum: number = 0,
  pageSize: number = 20
): Promise<SupplierStatsResponse> => {
  const organizationId = getOrganizationId();
  const url = `${API_BASE}/${organizationId}/qms-inspect-iqc-dashboards/supplier-stats/ui`;
  const params: any = {
    startDate: `${startDate} 00:00:00`,
    endDate: `${endDate} 23:59:59`,
    size: pageSize,
    page: pageNum,
  };
  
  if (materialId) {
    params.materialId = materialId;
  }
  
  const response = await request(url, {
    method: 'GET',
    query: params,
  });
  
  return response;
};

// 使用真实接口的服务
export const dashboardService = {
  async getProgressStats(startDate: string, endDate: string) {
    try {
      return await fetchProgressStats(startDate, endDate);
    } catch (error) {
      console.error('获取进度统计数据失败:', error);
      // 返回默认数据
      return {
        pending: 0,
        overdue: 0,
        inProgress: 0,
        completed: 0,
      };
    }
  },

  async getDefectiveStats(startDate: string, endDate: string) {
    try {
      return await fetchDefectiveStats(startDate, endDate);
    } catch (error) {
      console.error('获取不良统计数据失败:', error);
      return [];
    }
  },

  async getDefectiveDetails(
    startDate: string,
    endDate: string,
    defectiveItem?: string,
    pageNum: number = 0,
    pageSize: number = 20
  ) {
    try {
      return await fetchDefectiveDetails(startDate, endDate, defectiveItem, pageNum, pageSize);
    } catch (error) {
      console.error('获取不良详情数据失败:', error);
      return {
        totalPages: 0,
        totalElements: 0,
        numberOfElements: 0,
        size: pageSize,
        number: pageNum,
        content: [],
        empty: true,
      };
    }
  },

  async getMaterialStats(
    startDate: string,
    endDate: string,
    supplierId?: number,
    pageNum: number = 0,
    pageSize: number = 20
  ) {
    try {
      return await fetchMaterialStats(startDate, endDate, supplierId, pageNum, pageSize);
    } catch (error) {
      console.error('获取物料统计数据失败:', error);
      return {
        totalPages: 0,
        totalElements: 0,
        numberOfElements: 0,
        size: pageSize,
        number: pageNum,
        content: [],
        empty: true,
      };
    }
  },

  async getSupplierStats(
    startDate: string,
    endDate: string,
    materialId?: number,
    pageNum: number = 0,
    pageSize: number = 20
  ) {
    try {
      return await fetchSupplierStats(startDate, endDate, materialId, pageNum, pageSize);
    } catch (error) {
      console.error('获取供应商统计数据失败:', error);
      return {
        totalPages: 0,
        totalElements: 0,
        numberOfElements: 0,
        size: pageSize,
        number: pageNum,
        content: [],
        empty: true,
      };
    }
  },
};
