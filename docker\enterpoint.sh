#!/bin/bash
###
 # @Description:
 # <AUTHOR> <EMAIL>
 # @Date: 2023-06-14 10:18:31
 # @LastEditTime: 2024-09-05 10:17:38
 # <AUTHOR> <EMAIL>
###
set -e
export TIME_ID=`date +%s`
sed -i "s BUILD_BUILD_ENV_ID $TIME_ID g" /usr/share/nginx/html/packages/microConfig.json

find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_PATH ${BUILD_BASE_PATH:-/} g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.html' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.css' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_API_HOST $BUILD_API_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CLIENT_ID $BUILD_CLIENT_ID g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_WEBSOCKET_HOST $BUILD_WEBSOCKET_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_PLATFORM_VERSION $BUILD_PLATFORM_VERSION g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_IM_ENABLE $BUILD_IM_ENABLE g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_IM_WEBSOCKET_HOST $BUILD_IM_WEBSOCKET_HOST g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TRACE_LOG_ENABLE $BUILD_TRACE_LOG_ENABLE g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CUSTOMIZE_ICON_NAME $BUILD_CUSTOMIZE_ICON_NAME g"

find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_HMES_BASIC /inja-qms-mes g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_COMMON /inja-qms-tznc g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_HRPT_COMMON /hrpt g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_MODEL /inja-qms-tznm g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_REPORT /inja-qms-tznr g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_METHOD /inja-qms-tznd g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_SAMPLING /inja-qms-tznq g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_HSPC /inja-qms-tzns g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_TARZAN_HOHR /inja-qms-mes g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_METHODTZND /inja-qms-tznd g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_COMMON /inja-qms-tznc g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_APS_METHOD /inja-qms-aps g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_POOL_QUERY query g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPLAN /inja-qms-aps-mltp g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPURCHASE /inja-qms-aps-purchase g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_BASE_SERVER /inja-qms-aps g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_CUSZ_CODE_BEFORE KEY_FOCUS g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_LOV_CODE_BEFORE MT g"
find /usr/share/nginx/html -name '*.js' | xargs sed -i "s BUILD_API_PREFIX /aori g"



exec "$@"
