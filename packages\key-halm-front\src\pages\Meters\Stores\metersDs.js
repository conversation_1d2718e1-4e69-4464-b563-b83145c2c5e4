/**
 * @since 2020-08-18
 * <AUTHOR>
 * @copyright Copyright (c) 2020, Hand
 */
import intl from 'utils/intl';
import { isNil } from 'lodash';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const organizationId = getCurrentOrganizationId();

const commonModelPrompt = 'amtc.meters.model.meters';

// 查询列表数据
const queryListUrl = `${HALM_MTC}/v1/${organizationId}/meters`;
// 查询明细表单数据
const queryFormUrl = `${HALM_MTC}/v1/${organizationId}/meters`;
// 查询仪表读数
const queryReadingsUrl = `${HALM_MTC}/v1/${organizationId}/meter-readings`;
// 实时采集接口
const monitoringUrl = `${HALM_MTC}/v1/${organizationId}/meters/iot/read`;

function getDataPointLovPara(record) {
  let lovPara = '';
  if (record.get('assetId')) {
    lovPara = record.get('assetNum');
  } else if (record.get('assetLocationId')) {
    lovPara = `LOCATION${record.get('assetLocationId')}`;
  } else {
    lovPara = undefined;
  }
  return lovPara;
}

function getRuleConfigAct(ruleConfigDS) {
  const res = ruleConfigDS.reduce((pre, record) => {
    const { handleType, actId = undefined } = record.get(['handleType', 'actId']);
    if (handleType === 'CREATE_WO' && actId) {
      pre.push(actId);
    }
    return pre;
  }, []);
  return [...new Set(res)];
}

function tableDs() {
  return {
    autoQuery: true,
    selection: false,
    primaryKey: 'meterId',
    queryFields: [
      {
        name: 'asset',
        label: intl.get(`${commonModelPrompt}.asset`).d('设备'),
        type: 'object',
        lovCode: 'AAFM.JOINT_ASSET_RECEIPT',
        dynamicProps: {
          lovPara: ({ record }) => {
            const assetLocationId = record.get('assetLocationId');
            return {
              assetLocationId,
              tenantId: organizationId,
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'assetId',
        type: 'number',
        bind: 'asset.assetId',
      },
      {
        name: 'assetLocation',
        label: intl.get(`${commonModelPrompt}.assetLocationName`).d('位置'),
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        lovPara: {
          tenantId: organizationId,
        },
        ignore: 'always',
      },
      {
        name: 'assetLocationId',
        type: 'number',
        bind: 'assetLocation.assetLocationId',
      },
      {
        name: 'enabledFlag',
        label: intl.get(`${commonModelPrompt}.enabledFlag`).d('是否启用'),
        type: 'number',
        lookupCode: 'HPFM.FLAG',
        defaultValue: 1,
      },
      {
        name: 'meterName',
        label: intl.get(`${commonModelPrompt}.meterName`).d('名称'),
        type: 'string',
      },
      {
        name: 'meterCode',
        label: intl.get(`${commonModelPrompt}.meterCode`).d('代码'),
        type: 'string',
      },
      {
        name: 'meterType',
        label: intl.get(`${commonModelPrompt}.meterTypeName`).d('仪表点类型'),
        type: 'object',
        lovCode: 'AMTR.METER_TYPES',
        lovPara: {
          tenantId: organizationId,
          enabledFlag: 1,
        },
        ignore: 'always',
      },
      {
        name: 'meterTypeId',
        type: 'number',
        bind: 'meterType.meterTypeId',
      },
    ],
    fields: [
      {
        name: 'meterName',
        label: intl.get(`${commonModelPrompt}.meterName`).d('名称'),
        type: 'string',
      },
      {
        name: 'meterCode',
        label: intl.get(`${commonModelPrompt}.meterCode`).d('代码'),
        type: 'string',
      },
      {
        name: 'asset',
        label: intl.get(`${commonModelPrompt}.asset`).d('设备/资产'),
        type: 'string',
      },
      {
        name: 'assetLocationName',
        label: intl.get(`${commonModelPrompt}.assetLocationName`).d('位置'),
        type: 'string',
      },
      {
        name: 'meterTypeName',
        label: intl.get(`${commonModelPrompt}.meterTypeName`).d('仪表点类型'),
        type: 'string',
      },
      {
        name: 'meterClassCodeMeaning',
        label: intl.get(`${commonModelPrompt}.meterClassCode`).d('仪表点分类'),
        type: 'string',
      },
      {
        name: 'readingType',
        label: intl.get(`${commonModelPrompt}.readingType`).d('读数方式'),
        type: 'string',
      },
      {
        name: 'readingDirection',
        label: intl.get(`${commonModelPrompt}.readingDirection`).d('读数方向'),
        type: 'string',
      },
      {
        name: 'meterUom',
        label: intl.get(`${commonModelPrompt}.meterUom`).d('仪表单位'),
        type: 'string',
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: queryListUrl,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
        };
      },
    },
    events: {
      load: ({ dataSet }) => {
        const data = dataSet.queryDataSet.toData()[0];
        dataSet.handleExportParams(data);
      },
    },
  };
}

// 明细页
function basicInfoFormDS(ruleDs) {
  return {
    autoCreate: true,
    fields: [
      {
        name: 'meterTypeName',
        label: intl.get(`${commonModelPrompt}.meterTypeName`).d('仪表点类型'),
        type: 'object',
        lovCode: 'AMTR.METER_TYPES',
        lovPara: {
          tenantId: organizationId,
          enabledFlag: 1,
        },
        dynamicProps: {
          disabled: ({ record }) => record.get('meterTypeName'),
        },
        required: true,
      },
      {
        name: 'asset',
        label: intl.get(`${commonModelPrompt}.asset`).d('设备/资产'),
        type: 'object',
        lovCode: 'AAFM.METER_ASSET',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              assetLocationId: record.get('assetLocationId'),
              relActIds: getRuleConfigAct(ruleDs, record).join(','),
            };
          },
        },
      },
      {
        name: 'assetLocationName',
        label: intl.get(`${commonModelPrompt}.assetLocationName`).d('位置'),
        type: 'object',
        lovCode: 'AMDM.LOCATIONS',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              relActIds: getRuleConfigAct(ruleDs, record).join(','),
            };
          },
        },
        required: true,
      },
      {
        name: 'maintSiteName',
        type: 'string',
      },
      {
        name: 'maintSiteId',
        type: 'number',
      },
      {
        name: 'meterName',
        label: intl.get(`${commonModelPrompt}.meterName`).d('名称'),
        type: 'intl',
        required: true,
        maxLength: 40,
      },
      {
        name: 'meterCode',
        label: intl.get(`${commonModelPrompt}.meterCode`).d('代码'),
        type: 'string',
        required: true,
        maxLength: 40,
      },
      {
        name: 'parentMeterName',
        label: intl.get(`${commonModelPrompt}.parentMeterName`).d('父仪表点'),
        type: 'object',
        lovCode: 'AMTR.METERS',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              meterTypeId: record.get('meterTypeId'),
            };
          },
        },
      },
      {
        name: 'readingTypeCode',
        label: intl.get(`${commonModelPrompt}.readingType`).d('读数方式'),
        type: 'string',
        lookupCode: 'AMTR.METER_READING_TYPE',
      },
      {
        name: 'readingDirectionCode',
        label: intl.get(`${commonModelPrompt}.readingDirection`).d('读数方向'),
        type: 'string',
        lookupCode: 'AMTR.METER_READING_DIRECTION',
      },
      {
        name: 'meterClassCode',
        label: intl.get(`${commonModelPrompt}.meterClassCode`).d('仪表点分类'),
        type: 'string',
        lookupCode: 'AMTR.METER_CLASS',
        dynamicProps: {
          disabled: ({ record }) => record.get('meterClassCode'),
        },
        required: true,
      },
      {
        name: 'keyMeterFlag',
        label: intl.get(`${commonModelPrompt}.keyMeterFlag`).d('关键仪表'),
        type: 'number',
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'readingFlag',
        label: intl.get(`${commonModelPrompt}.readingFlag`).d('扫码抄表'),
        type: 'number',
        defaultValue: 1,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'enabledFlag',
        label: intl.get(`${commonModelPrompt}.enabledFlag`).d('是否启用'),
        type: 'number',
        defaultValue: 1,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'meterUom',
        label: intl.get(`${commonModelPrompt}.meterUom`).d('仪表单位'),
        type: 'string',
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'dataPointLov',
        type: 'object',
        lovCode: 'HALM.DATA_POINT',
        label: intl.get(`${commonModelPrompt}.dataPoint`).d('数据点'),
        noCache: true,
        ignore: 'always',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              assetNum: getDataPointLovPara(record),
            };
          },
        },
      },
      {
        name: 'dataPointCode',
        type: 'string',
        bind: 'dataPointLov.propertyCode',
      },
      {
        name: 'propertyModelName',
        type: 'string',
        bind: 'dataPointLov.propertyName',
        label: intl.get(`${commonModelPrompt}.dataPoint`).d('数据点'),
      },
      {
        name: 'collectMode',
        label: intl.get(`${commonModelPrompt}.collectMode`).d('采集方式'),
        type: 'string',
        lookupCode: 'HALM.COLLECT_MODE',
        dynamicProps: {
          disabled: ({ record }) => {
            return !record.get('dataPointCode');
          },
          required: ({ record }) => {
            return !!record.get('dataPointCode');
          },
        },
      },
      {
        name: 'collectFrequency',
        type: 'number',
        label: intl.get(`${commonModelPrompt}.acquisitionFrequency`).d('采集频率'),
        dynamicProps: {
          disabled: ({ record }) => {
            return (
              !record.get('dataPointCode') ||
              !record.get('collectMode') ||
              record.get('collectMode') !== 'REGULAR'
            );
          },
          required: ({ record }) => {
            return (
              !!record.get('dataPointCode') &&
              !!record.get('collectMode') &&
              record.get('collectMode') === 'REGULAR'
            );
          },
        },
      },
      {
        name: 'collectUom',
        type: 'string',
        lookupCode: 'HALM.COLLECT_UOM',
        label: intl.get(`${commonModelPrompt}.collectUom`).d('采集单位'),
        dynamicProps: {
          disabled: ({ record }) => {
            return (
              !record.get('dataPointCode') ||
              !record.get('collectMode') ||
              record.get('collectMode') !== 'REGULAR'
            );
          },
          required: ({ record }) => {
            return (
              !!record.get('dataPointCode') &&
              !!record.get('collectMode') &&
              record.get('collectMode') === 'REGULAR'
            );
          },
        },
      },
      {
        name: 'lastestReadingValue',
        label: intl.get(`${commonModelPrompt}.lastestReadingValue`).d('读数值'),
        type: 'number',
        step: 0.01,
        required: true,
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
        dynamicProps: {
          lookupCode: ({ record }) => {
            const meterClassCode = record.get('meterClassCode');
            if (meterClassCode === 'RUN_STATUS') {
              return 'AMTR.METER_STATUS';
            } else if (meterClassCode === 'MONITOR_STATUS') {
              return 'AMTC.READING_VALUE';
            }
          },
        },
      },
      {
        name: 'offType',
        label: intl.get(`${commonModelPrompt}.offType`).d('停机类型'),
        type: 'string',
        lookupCode: 'HALM.OFF_TYPE',
        dynamicProps: {
          required: ({ record }) => {
            if (
              record.get('meterClassCode') === 'RUN_STATUS' &&
              record.get('lastestReadingValue') === 0 &&
              record.status === 'add'
            ) {
              return true;
            } else {
              return false;
            }
          },
        },
      },
      {
        name: 'readingTime',
        label: intl.get(`${commonModelPrompt}.readingDate`).d('读数日期'),
        type: 'dateTime',
        defaultValue: new Date(),
        required: true,
      },
      {
        name: 'dailyChangeValue',
        label: intl.get(`${commonModelPrompt}.dailyChangeValue`).d('日变化量'),
        type: 'number',
        step: 0.01,
      },
      {
        name: 'enableAlarmFlag',
        label: intl.get(`${commonModelPrompt}.enableAlert`).d('启用告警管理'),
        type: 'number',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 0,
      },
      {
        name: 'alarmAccordType',
        label: intl.get(`${commonModelPrompt}.alarmAccordType`).d('告警状态影响'),
        lookupCode: 'HALM.ALARM_ACCORD_TYPE',
        type: 'string',
        dynamicProps: {
          required: ({ record }) => record.get('enableAlarmFlag') === 1,
        },
        defaultValue: 'METER',
      },
    ],
    transport: {
      read: ({ dataSet }) => {
        return {
          url: `${queryFormUrl}/${dataSet.meterId}`,
          method: 'GET',
        };
      },
    },
    events: {
      update: ({ record, name, value }) => {
        if ((name === 'asset' || name === 'assetLocationName') && value?.maintSiteId) {
          if (value) {
            record.set('maintSiteName', value.maintSiteName);
            record.set('maintSiteId', value.maintSiteId);
          }
        }
        // 读数值切换为‘正常’后，清空停机类型
        if (name === 'lastestReadingValue' && value) {
          record.init('offType');
        }
      },
    },
  };
}

// 仪表读数
function readingDs(formDS) {
  return {
    primaryKey: 'meterReadingId',
    selection: false,
    queryFields: [
      {
        name: 'readingTime',
        label: intl.get(`${commonModelPrompt}.readingTime`).d('读数时间'),
        type: 'date',
        range: ['readingTimeStart', 'readingTimeEnd'],
        ignore: true,
      },
      {
        name: 'readingTimeStart',
        type: 'date',
        bind: 'readingTime.readingTimeStart',
      },
      {
        name: 'readingTimeEnd',
        type: 'date',
        bind: 'readingTime.readingTimeEnd',
      },
      {
        label: intl.get(`${commonModelPrompt}.dataSource`).d('数据来源'),
        name: 'sourceTypeCode',
        type: 'string',
        lookupCode: 'HALM.METER_READ_TYPE',
      },
    ],
    fields: [
      {
        name: 'readingName',
        label: intl.get(`${commonModelPrompt}.readingName`).d('名称'),
        type: 'string',
      },
      {
        name: 'changeValue',
        label: intl.get(`${commonModelPrompt}.changeValue`).d('变化值'),
        type: 'number',
      },
      {
        name: 'totalValue',
        label: intl.get(`${commonModelPrompt}.totalValue`).d('总值'),
        type: 'number',
      },
      {
        name: 'meterUom',
        label: intl.get(`${commonModelPrompt}.meterUom`).d('仪表单位'),
        type: 'string',
      },
      {
        name: 'readingTime',
        label: intl.get(`${commonModelPrompt}.readingTime`).d('读数时间'),
        type: 'dateTime',
        required: true,
      },
      {
        name: 'readingValue',
        label: intl.get(`${commonModelPrompt}.readingValue`).d('该次读数'),
        type: 'number',
        required: true,
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
        precision: 5,
        pattern: /^-?\d{1,11}(\.\d{1,5})?$/,
        defaultValidationMessages: {
          patternMismatch: '要求小数点前11位，后5位',
        },
        dynamicProps: {
          lookupCode: () => {
            const meterClassCode = formDS?.current?.get('meterClassCode');
            if (meterClassCode === 'RUN_STATUS') {
              return 'AMTR.METER_STATUS';
            } else if (meterClassCode === 'MONITOR_STATUS') {
              return 'AMTC.READING_VALUE';
            } else {
              return null;
            }
          },
        },
      },
      {
        name: 'offType',
        label: intl.get(`${commonModelPrompt}.offType`).d('停机类型'),
        type: 'string',
        lookupCode: 'HALM.OFF_TYPE',
        dynamicProps: {
          required: ({ record }) => {
            if (
              formDS.toData()[0].meterClassCode === 'RUN_STATUS' &&
              record.get('readingValue') === 0
            ) {
              return true;
            } else {
              return false;
            }
          },
          disabled: ({ record }) => {
            if (
              formDS.toData()[0].meterClassCode === 'RUN_STATUS' &&
              record.get('readingValue') === 0
            ) {
              return false;
            } else {
              return true;
            }
          },
        },
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'inactiveFlag',
        label: intl.get(`${commonModelPrompt}.inactiveFlag`).d('标记为无效读数'),
        type: 'number',
        defaultValue: 0,
        trueValue: 1,
        falseValue: 0,
      },
      {
        name: 'meterClass',
        lookupCode: 'METER_CLASS',
        type: 'number',
      },
      {
        name: 'sourceTypeCodeMeaning',
        label: intl.get(`${commonModelPrompt}.sourceType`).d('读数来源'),
        type: 'string',
      },
      {
        name: 'alarmRecordCode',
        label: intl.get(`${commonModelPrompt}.alarmRecordCode`).d('告警记录编码'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params, data }) => {
        const newData = {
          ...data,
          readingTime: undefined,
          readingTimeStart: data?.readingTimeStart
            ? `${data?.readingTimeStart?.slice(0, -8)}00:00:00`
            : undefined,
          readingTimeEnd: data?.readingTimeEnd
            ? `${data?.readingTimeEnd?.slice(0, -8)}23:59:59`
            : undefined,
        };
        return {
          url: queryReadingsUrl,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
          data: newData,
        };
      },
    },
  };
}

function graphQueryDs() {
  return {
    autoCreate: true,
    modifiedCheck: false,
    fields: [
      {
        name: 'readingTime',
        label: intl.get(`${commonModelPrompt}.readingTime`).d('读数时间'),
        type: 'date',
        range: ['readingTimeStart', 'readingTimeEnd'],
        ignore: true,
      },
      {
        name: 'readingTimeStart',
        type: 'date',
        bind: 'readingTime.readingTimeStart',
      },
      {
        name: 'readingTimeEnd',
        type: 'date',
        bind: 'readingTime.readingTimeEnd',
      },
    ],
  };
}

// 实时监控
function monitoringDs() {
  return {
    // primaryKey: 'meterReadingId',
    selection: false,
    fields: [
      {
        name: 'readingName',
        label: intl.get(`${commonModelPrompt}.readingName`).d('名称'),
        type: 'string',
      },
    ],
    transport: {
      read: ({ params }) => {
        return {
          url: monitoringUrl,
          method: 'GET',
          params: {
            ...params,
            tenantId: organizationId,
          },
        };
      },
    },
  };
}

function ruleConfigDs() {
  return {
    selection: false,
    primaryKey: 'alarmRuleId',
    fields: [
      {
        name: 'alarmRuleId',
        type: 'number',
        ignore: true,
      },
      {
        name: 'order',
        label: intl.get(`${commonModelPrompt}.order`).d('序号'),
        type: 'number',
        maxLength: 20,
        ignore: true,
      },
      {
        name: 'condition',
        label: intl.get(`${commonModelPrompt}.condition`).d('读数条件'),
        type: 'number',
      },
      {
        name: 'lowerLimitValue',
        type: 'number',
        max: 'upperLimitValue',
        dynamicProps: {
          required: ({ record }) =>
            isNil(record.get('lowerLimitValue')) && isNil(record.get('upperLimitValue')),
        },
        pattern: /^(-)?[1-9]\d{0,19}(\.\d{1,4})?$|^(-)?0(\.\d{1,4})?$/,
        defaultValidationMessages: {
          patternMismatch: intl
            .get(`${commonModelPrompt}.limitMsg`)
            .d('请输入整数位在20内，小数位在4以内的读数'),
        },
      },
      {
        name: 'lowerLimitInclude',
        type: 'number',
        dynamicProps: {
          required: ({ record }) => record.get('lowerLimitValue'),
        },
      },
      {
        name: 'upperLimitValue',
        type: 'number',
        min: 'lowerLimitValue',
        dynamicProps: {
          required: ({ record }) =>
            isNil(record.get('lowerLimitValue')) && isNil(record.get('upperLimitValue')),
        },
        pattern: /^(-)?[1-9]\d{0,19}(\.\d{1,4})?$|^(-)?0(\.\d{1,4})?$/,
        defaultValidationMessages: {
          patternMismatch: intl
            .get(`${commonModelPrompt}.limitMsg`)
            .d('请输入整数位在20内，小数位在4以内的读数'),
        },
      },
      {
        name: 'upperLimitInclude',
        type: 'number',
        dynamicProps: {
          required: ({ record }) => record.get('upperLimitValue'),
        },
      },
      {
        name: 'repeatTime',
        label: intl.get(`${commonModelPrompt}.repeatTime`).d('告警重复过滤时间'),
        type: 'number',
        min: 0,
        required: true,
        precision: 0,
        pattern: /^\d{1,16}$/,
        defaultValidationMessages: {
          patternMismatch: intl.get(`${commonModelPrompt}.timeMsg`).d('请输入16位以内的整数'),
        },
      },
      {
        name: 'repeatTimeUnit',
        type: 'string',
        lookupCode: 'HALM.COLLECT_UOM',
        defaultValue: 'MINUTE',
        required: true,
      },
      {
        name: 'handleType',
        label: intl.get(`${commonModelPrompt}.treatmentType`).d('处理类型'),
        type: 'string',
        lookupCode: 'HALM.ALARM_HANDLE_TYPE',
        defaultValue: 'MANUAL',
        required: true,
      },
      {
        name: 'description',
        label: intl.get(`${commonModelPrompt}.description`).d('描述'),
        type: 'string',
        maxLength: 240,
      },
      {
        name: 'status',
        label: intl.get(`${commonModelPrompt}.enabledFlag`).d('是否启用'),
        type: 'number',
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'msgObjList',
        label: intl.get(`${commonModelPrompt}.msgObj`).d('消息通知对象'),
        type: 'string',
        lookupCode: 'HALM.ALARM_MSG_SET',
        multiple: true,
        dynamicProps: {
          required: ({ record }) => record.get('handleType') === 'MSG_SEND',
        },
      },
      {
        name: 'msgPositionList',
        label: intl.get(`${commonModelPrompt}.msgPosition`).d('岗位'),
        type: 'object',
        lovCode: 'LOV_POSITION',
        multiple: true,
      },
      {
        name: 'msgEmployeeList',
        label: intl.get(`${commonModelPrompt}.msgEmployee`).d('员工'),
        type: 'object',
        lovCode: 'HALM.EMPLOYEE',
        lovPara: { tenantId: organizationId },
        multiple: true,
      },
      {
        name: 'messageLov',
        label: intl.get(`${commonModelPrompt}.msgSendingConfig`).d('消息发送配置'),
        type: 'object',
        lovCode: 'ALM.MSG_TEMP_SERVER',
        valueField: 'messageCode',
        textField: 'messageName',
        ignore: 'always',
        dynamicProps: {
          required: ({ record }) => record.get('handleType') === 'MSG_SEND',
        },
      },
      {
        name: 'messageName',
        type: 'string',
        bind: 'messageLov.messageName',
      },
      {
        name: 'messageCode',
        type: 'string',
        bind: 'messageLov.messageCode',
      },
      {
        name: 'srTypeLov',
        type: 'object',
        lovCode: 'AMTC.SRTYPES',
        textField: 'srTypeName',
        valueField: 'srTypeId',
        label: intl.get(`${commonModelPrompt}.srType`).d('服务申请单类型'),
        ignore: 'always',
        dynamicProps: {
          required: ({ record }) => record.get('handleType') === 'CREATE_SR',
        },
      },
      {
        name: 'srTypeId',
        type: 'number',
        bind: 'srTypeLov.srTypeId',
      },
      {
        name: 'srTypeName',
        type: 'string',
        bind: 'srTypeLov.srTypeName',
      },
      {
        name: 'actLov',
        label: intl.get(`${commonModelPrompt}.act`).d('标准作业'),
        type: 'object',
        lovCode: 'AMTC.ACT',
        dynamicProps: {
          disabled: ({ record }) => record.get('woTypeId') && !record.get('actId'),
          lovPara: ({ record }) => {
            return {
              tenantId: organizationId,
              assetId: record?.getState('assetId'),
              ignoreNullFlag: 1,
              assetLocationId: record?.getState('assetLocationId'),
              assetLocationUnionFlag: true,
              maintSiteId: record?.get('maintSiteId'),
            };
          },
        },
        ignore: 'always',
      },
      {
        name: 'actId',
        type: 'number',
        bind: 'actLov.actId',
      },
      {
        name: 'actName',
        type: 'string',
        bind: 'actLov.actName',
      },
      {
        name: 'woTypeLov',
        label: intl.get(`${commonModelPrompt}.woType`).d('工单类型'),
        type: 'object',
        lovCode: 'AMTC.WORKORDERTYPES',
        lovPara: {
          organizationId,
        },
        dynamicProps: {
          disabled: ({ record }) => record.get('actId'),
          required: ({ record }) => record.get('handleType') === 'CREATE_WO',
        },
        ignore: 'always',
      },
      {
        name: 'woTypeId',
        type: 'number',
        bind: 'woTypeLov.woTypeId',
      },
      {
        name: 'woTypeName',
        type: 'string',
        bind: 'woTypeLov.woTypeName',
      },
      {
        name: 'maintSiteLov',
        label: intl.get(`${commonModelPrompt}.maintSite`).d('服务区域'),
        type: 'object',
        lovCode: 'AMDM.ASSET_MAINT_SITE',
        lovPara: {
          tenantId: organizationId,
        },
        dynamicProps: {
          required: ({ record }) => record.get('handleType') !== 'MSG_SEND',
        },
        ignore: 'always',
      },
      {
        name: 'maintSiteName',
        type: 'string',
        bind: 'maintSiteLov.maintSiteName',
      },
      {
        name: 'maintSiteId',
        type: 'number',
        bind: 'maintSiteLov.maintSiteId',
      },
      {
        name: 'woBasicType',
        type: 'string',
      },
      {
        name: 'textLov',
        type: 'object',
        lovCode: 'HALM.STATIC_TEXT',
        dynamicProps: {
          required: ({ record }) => {
            const handleType = record.get('handleType');
            return handleType === 'CREATE_WO' || handleType === 'CREATE_SR';
          },
        },
        optionsProps: {
          childrenField: 'children',
        },
        ignore: 'always',
      },
      {
        name: 'textId',
        type: 'number',
        bind: 'textLov.textId',
      },
      {
        name: 'title',
        type: 'string',
        bind: 'textLov.title',
      },
      {
        name: 'defaultJobFlag',
        type: 'boolean',
        label: intl.get(`${commonModelPrompt}.defaultJobFlag`).d('优先职务默认'),
        trueValue: 1,
        falseValue: 0,
        defaultValue: 1,
      },
      {
        name: 'plannerGroupId',
        type: 'number',
      },
      {
        name: 'plannerGroupName',
        label: intl.get(`${commonModelPrompt}.plannerGroup`).d('计划员组'),
        type: 'string',
        dynamicProps: {
          required: ({ record }) =>
            !record.get('defaultJobFlag') && record.get('handleType') === 'CREATE_WO',
        },
      },
      {
        name: 'plannerId',
        type: 'number',
      },
      {
        name: 'plannerName',
        label: intl.get(`${commonModelPrompt}.planner`).d('计划员'),
        type: 'string',
      },
      {
        name: 'ownerGroupId',
        type: 'number',
      },
      {
        name: 'ownerGroupName',
        label: intl.get(`${commonModelPrompt}.ownerGroup`).d('负责人组'),
        type: 'string',
        dynamicProps: {
          required: ({ record }) =>
            !record.get('defaultJobFlag') && record.get('handleType') === 'CREATE_WO',
        },
      },
      {
        name: 'ownerId',
        type: 'number',
      },
      {
        name: 'ownerName',
        label: intl.get(`${commonModelPrompt}.owner`).d('负责人'),
        type: 'string',
      },
    ],
    events: {
      update: ({ record, name, value }) => {
        if (name === 'actLov' && value) {
          record.set('woBasicType', value.woBasicType);
          record.set('woTypeId', value.woTypeId);
          record.set('woTypeName', value.woTypeName);
        }
        if (name === 'woTypeLov') {
          if (value) {
            record.set('woBasicType', value.woBasicType);
          } else {
            record.set('actLov', null);
          }
        }
      },
    },
  };
}

export { tableDs, basicInfoFormDS, readingDs, graphQueryDs, monitoringDs, ruleConfigDs };
