/**
 * @Description: 不良记录单管理平台 - services
 * @Author: <EMAIL>
 * @Date: 2023/3/9 14:39
 */
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC, HZERO_PLATFORM } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 保存不良记录单
 * @function SaveNcReportDoc
 * @returns {object} fetch Promise
 */
export function SaveNcReportDoc(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-nc-report/workflow/cache/ui`,
    method: 'POST',
  };
}

/**
 * 根据检验业务类型查询所需信息
 * @function GetInspectBusInfo
 * @returns {object} fetch Promise
 */
export function GetInspectBusInfo(): object {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-disposition-group-member/property/list/ui`,
    method: 'GET',
  };
}

/**
 * 查询检验单行数据
 * @function QueryInspectDocLine
 * @returns {object} fetch Promise
 */
export function QueryInspectDocLine(): object {
  return {
    url: `${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/mt-inspect-doc/source-doc-ids`,
    method: 'GET',
  };
}


/**
 * 统一查询独立、SQL、URL类型的值集
 * {HZERO_PLATFORM}/v1/lovs/data
 * @param {String} lovCode - 值集code
 * @param {Object} params - 额外的查询参数
 */

export function QueryUnifyIdpValue(): object {
  return {
    url: `${HZERO_PLATFORM}/v1/${tenantId}/lovs/data`,
    method: 'GET',
  };
}
