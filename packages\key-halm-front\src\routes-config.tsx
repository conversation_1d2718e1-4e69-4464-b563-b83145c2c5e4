import React, { ReactNode } from 'react';
import { ConfigProvider, configure } from 'choerodon-ui';
import { ModalProvider } from 'choerodon-ui/pro';
import { loadConfig } from 'hzero-front/lib/utils/c7nUiConfig';
import intl from 'utils/intl';
import { handleResponse } from 'alm/utils/response';
import emptyPng from 'alm/components/EmptyState/assets/empty.png';
import 'alm/global.module.less';

if (process.env.ADDITIONAL === 'true') {
  loadConfig(configure);
}
console.log('halm_choerodon-ui_version: ', require('choerodon-ui/lib/version'));

export const moduleResponseIntercept = (url, status, data) => {
  if (data && data.failed) {
    return handleResponse(data);
  }
};

const defaultRenderEmpty = (componentName?: string): ReactNode => {
  switch (componentName) {
    case 'Table':
      return (
        <div style={{ textAlign: 'center' }}>
          <img style={{ height: '120px', width: '120px' }} src={emptyPng} alt="" />
          <p>{intl.get('hzero.common.model.noData').d('暂无数据')}</p>
        </div>
      );
    case 'Output':
      return '-';
    default:
      return '-';
  }
};

const props: any = {
  // lovQueryBar: 'professionalBar',
  tableCustomizable: true,
  lovTableProps: {
    selectionMode: 'dblclick',
    alwaysShowRowBox: true,
    selectionBoxRenderer: ({ element }) => {
      return React.cloneElement(element, { onDoubleClick: e => e.stopPropagation() });
    },
  },
  lovAutoSelectSingle: true,
  renderEmpty: defaultRenderEmpty,
};

export const PageWrapper = () => {
  const PageWrapperComponent = ({ children }: any) => {
    return (
      <ConfigProvider {...props}>
        <ModalProvider>{children}</ModalProvider>
      </ConfigProvider>
    );
  };
  return PageWrapperComponent;
};

export const addRoutes = async () => {
  const loadedMap = {};
  const loadScripts = (url: string) => {
    if (loadedMap[url]) {
      return Promise.resolve(null);
    } else {
      loadedMap[url] = 1;
    }
    return new Promise((resolve, reject) => {
      const element = document.createElement('script');
      element.src = url;
      element.type = 'text/javascript';
      element.async = true;

      const clean = () => {
        document.head.removeChild(element);
      };

      const listenError = error => {
        const errorSrc = error.filename;
        if (errorSrc && errorSrc.includes(url)) {
          window.removeEventListener('error', listenError);
          clean();
          reject(new Error(`Dynamic Script Error: ${url}`));
        }
      };

      element.onload = () => {
        window.removeEventListener('error', listenError);
        // console.log(`Dynamic Script Loaded: ${args.url}`)
        resolve(null);
      };

      element.onerror = e => {
        window.removeEventListener('error', listenError);
        // console.error(`Dynamic Script Error: ${args.url}`)
        clean();
        reject(e || new Error(`Dynamic Script Error: ${url}`));
      };

      window.addEventListener('error', listenError);
      document.head.appendChild(element);
    });
  };

  await loadScripts(
    'http://api.map.baidu.com/getscript?v=2.0&ak=WNR24E0CgiCjLY8eVXuzGfLx9f8dcc1u&services=&t='
  );
  await Promise.all(
    [
      'http://api.map.baidu.com/library/TrafficControl/1.4/src/TrafficControl_min.js',
      'http://api.map.baidu.com/library/LuShu/1.2/src/LuShu_min.js',
    ].map(loadScripts)
  );

  return [];
};

export default {};
