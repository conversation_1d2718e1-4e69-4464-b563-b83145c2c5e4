/**
 * Materials - 仪表点
 * @since 2020-08-18
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MTC } from 'alm/utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import ExcelExport from 'components/ExcelExport';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';

import { tableDs } from './Stores/metersDs';

const commonViewPrompt = 'amtc.meters.view';

const organizationId = getCurrentOrganizationId();

@formatterCollections({
  code: ['alm.common', 'alm.component', 'amtc.meters'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDs());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
export default class Meters extends Component {
  constructor(props) {
    super(props);
    this.state = {
      exportParams: {}, // 导出组件的参数
    };
  }

  componentDidMount() {
    this.props.listDS.handleExportParams = this.handleExportParams;
  }

  @Bind()
  handleExportParams(data) {
    this.setState({
      exportParams: data,
    });
  }

  /**
   * 查询表单数据
   */
  @Bind()
  fetchList() {
    this.props.listDS.query();
  }

  /**
   * 新增
   * 跳转到新增明细页
   */
  @Bind()
  handleAdd() {
    this.props.history.push({ pathname: `/amtr/meters/create` });
  }

  /**
   * 跳转到查看页面
   */
  handleView(id, flag = false) {
    this.props.history.push({ pathname: `/amtr/meters/detail/${id}`, state: { isEdit: flag } });
  }

  /**
   * 跳转到编辑页面
   */
  @Bind()
  handleEdit(id) {
    this.props.history.push({ pathname: `/amtr/meters/detail/${id}`, state: { isEdit: true } });
  }

  /**
   *  设备资产导入
   */
  @Bind()
  handleImport() {
    openTab({
      key: `/amtr/meters/data-import/AMTR.METER_READINGS`,
      title: intl.get(`${commonViewPrompt}.title.import`).d('计量点读数导入'),
      search: queryString.stringify({
        action: intl.get(`${commonViewPrompt}.title.import`).d('计量点读数导入'),
      }),
    });
  }

  /**
   *  仪表点主数据导入
   */
  @Bind()
  handleMeterImport() {
    openTab({
      key: `/amtr/meters/data-import/AMTR.METER`,
      title: intl.get(`${commonViewPrompt}.title.meterimport`).d('仪表点导入'),
      search: queryString.stringify({
        action: intl.get(`${commonViewPrompt}.title.meterimport`).d('仪表点导入'),
      }),
    });
  }

  get columns() {
    return [
      {
        name: 'meterName',
        align: 'left',
        width: 150,
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleView(record.get('meterId'))}>{value}</a>;
        },
      },
      {
        name: 'meterCode',
        align: 'left',
        width: 150,
      },
      {
        name: 'asset',
        align: 'left',
        width: 120,
      },
      {
        name: 'assetLocationName',
        align: 'left',
        width: 140,
      },
      {
        name: 'meterTypeName',
        align: 'left',
        width: 150,
      },
      {
        name: 'meterClassCodeMeaning',
        align: 'left',
        width: 80,
      },
      {
        name: 'readingType',
        align: 'left',
        width: 90,
      },
      {
        name: 'readingDirection',
        align: 'left',
        width: 140,
      },
      {
        name: 'meterUom',
        align: 'left',
        width: 150,
      },
      {
        name: 'description',
        align: 'left',
      },
      {
        name: 'operate',
        width: 60,
        align: 'left',
        header: intl.get('hzero.common.button.action').d('操作'),
        renderer: ({ record }) => {
          return (
            <a onClick={() => this.handleEdit(record.get('meterId'))}>
              {intl.get('hzero.common.button.edit').d('编辑')}
            </a>
          );
        },
      },
    ];
  }

  render() {
    const { exportParams } = this.state;
    return (
      <React.Fragment>
        <Header title={intl.get(`${commonViewPrompt}.title.meters`).d('仪表点')}>
          <Button color="primary" icon="add" onClick={this.handleAdd} key="create">
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
          <ExcelExport
            requestUrl={`${HALM_MTC}/v1/${organizationId}/meters/export`}
            queryParams={exportParams}
          />
          <Button icon="file_upload" onClick={this.handleMeterImport} key="meterImport">
            {intl.get('hzero.common.button.import').d('导入')}
          </Button>
          <Button icon="file_upload" onClick={this.handleImport} key="import">
            {intl.get(`${commonViewPrompt}.button.import`).d('读数导入')}
          </Button>
        </Header>
        <Content>
          <Table
            key="metersList"
            queryBar="filterBar"
            searchCode="AORI.METERS.LIST"
            customizedCode="AORI.METERS.LIST"
            dataSet={this.props.listDS}
            columns={this.columns}
            queryBarProps={{
              fuzzyQueryPlaceholder: intl
                .get(`${commonViewPrompt}.keyPlaceHolder`)
                .d('请输入名称、代码'),
              queryFieldsLimit: 3,
            }}
          />
        </Content>
      </React.Fragment>
    );
  }
}
