/**
 * ServiceApplyCurrent - 我的服务申请
 * @since 2020-08-26
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { DataSet, Modal, Table, Button, Select, DateTimePicker } from 'choerodon-ui/pro';
import { Tag, Divider } from 'choerodon-ui';

import moment from 'moment';
import { isUndefined } from 'lodash';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import request from 'utils/request';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { HALM_MTC } from 'alm/utils/config';
import { HZERO_PLATFORM } from 'utils/config';
import Icon from 'alm/components/IconSelect';

import { statusColors } from 'alm/utils/constants';
import InspectForm from '../EvaluateTemp/components/InspectForm';
import { tableDS, recallDS } from './Stores';

const organizationId = getCurrentOrganizationId();
const saveEvaluateUrl = `${HALM_MTC}/v1/${organizationId}/sr-evaluates/batch-create-evaluate`;

const prompt = 'amtc.serviceApply';
const promptCode = 'amtc.serviceApply.model.serviceApply';

@formatterCollections({
  code: [
    'alm.common',
    'alm.component',
    'amtc.serviceApply',
    'aatn.equipmentAsset',
    'amtc.srEvaluateTemp',
  ],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
export default class serviceApplyCurrent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentEmp: {},
    };
    this.recallDs = new DataSet(recallDS());
    this.inspectFormRef = React.createRef();
    this.handleSearchCurrentEmployee();
  }

  /**
   * 新建
   */
  @Bind()
  handleCreate() {
    this.props.history.push('/amtc/service-apply-current/create');
  }

  /**
   * 删除
   */
  @Bind()
  handleDelete() {
    if (this.props.listDS.selected.length > 0) {
      this.props.listDS.delete(this.props.listDS.selected);
    } else {
      notification.error({
        message: intl.get(`${prompt}.view.message.validate`).d('请勾选数据!'),
      });
    }
  }

  /**
   * 跳转到编辑页面
   */
  handleEdit(record) {
    this.props.history.push({
      pathname: `/amtc/service-apply-current/detail/${record.get('srId')}`,
      state: {
        isEdit: true,
      },
    });
  }

  /**
   * 跳转到编辑页面
   */
  handleView(record) {
    this.props.history.push({
      pathname: `/amtc/service-apply-current/detail/${record.get('srId')}`,
    });
  }

  /**
   * 跳转到工单编辑页面
   */
  handleWoView(type, id, num) {
    let url;
    switch (type) {
      case 0:
        url = `/amtc/work-order/detail/${id}/${num}`;
        break;
      case 1:
        url = `/amtc/sub-requisition/detail/${id}`;
        break;
      default:
        break;
    }
    this.props.history.push({
      pathname: url,
    });
  }

  @Bind()
  async handleRecall(record) {
    this.recallDs.create(record.toData());
    await this.recallDs.submit();
    this.props.listDS.query();
    this.recallDs.reset();
  }

  @Bind()
  handleObjectInfo(record) {
    const assetLocationName = record.get('assetLocationName');
    const descAndLabel = record.get('descAndLabel');
    let info = '';
    if (!isUndefined(assetLocationName)) {
      if (!isUndefined(descAndLabel)) {
        info = `${assetLocationName}/${descAndLabel}`;
      } else {
        info = assetLocationName;
      }
    } else if (isUndefined(assetLocationName) && !isUndefined(descAndLabel)) {
      info = descAndLabel;
    }
    return info;
  }

  @Bind()
  handleSearchCurrentEmployee() {
    // 查询当前用户在当前租户下的员工
    const getCurrentEmployeeUrl = `${HZERO_PLATFORM}/v1/${organizationId}/employee-users/employee`;
    request(getCurrentEmployeeUrl, {
      method: 'GET',
      query: {
        enabledFlag: 1,
      },
    }).then(res => {
      if (res && !res.failed) {
        this.setState({
          currentEmp: res,
        });
      }
    });
  }

  @Bind()
  handleAppraise(record) {
    const { currentEmp } = this.state;
    const srId = record.get('srId');
    const evaluateFlag = record.get('evaluateFlag'); // 评价标识
    const evaluateTempId = record.get('evaluateTempId');
    const ratingMode = evaluateFlag === 1 ? 'view' : 'create';
    const tempId = ratingMode === 'create' ? evaluateTempId : srId;

    const props = {
      mode: ratingMode,
      id: tempId,
      sourceTypeCode: 'SR',
      moduleId: srId,
      employeeId: currentEmp?.employeeId,
    };

    Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${prompt}.modal.srEvaluate`).d('服务评价'),
      children: <InspectForm {...props} ref={this.inspectFormRef} />,
      footer: okBtn => okBtn,
      onOk: async () => {
        if (ratingMode === 'create') {
          const inspectFormData = await this.inspectFormRef?.current.handleSubmit();
          if (inspectFormData) {
            const { itemList, fileUrlList } = inspectFormData;
            const newSrEvaluateList = itemList.map(i => {
              return {
                ...i,
                srId,
              };
            });
            request(saveEvaluateUrl, {
              method: 'POST',
              body: {
                fileUrlList,
                srEvaluateList: newSrEvaluateList,
                srId,
              },
            }).then(res => {
              if (!res?.failed) {
                this.props.listDS.query();
              }
            });
          } else {
            return false;
          }
        }
      },
    });
  }

  get btns() {
    return [
      <Button color="primary" onClick={this.handleCreate} key="create">
        {intl.get('hzero.common.button.create').d('新建')}
      </Button>,
      // <Button onClick={this.handleDelete} key="delete">
      //   {intl.get('hzero.common.button.delete').d('删除')}
      // </Button>,
    ];
  }

  get columns() {
    const menuIconStyle = {
      width: 14,
      height: 14,
    };
    return [
      {
        width: 75,
        header: intl.get(`${promptCode}.icon`).d('图标'),
        renderer: ({ record }) => {
          return (
            <Icon
              size={28}
              title={record.get('srTypeIcon')}
              type={record.get('srTypeIcon')}
              style={menuIconStyle}
              color={record.get('priorityColor')}
            />
          );
        },
      },
      {
        name: 'srNumber',
        width: 100,
        renderer: ({ value, record }) => {
          return <a onClick={() => this.handleView(record)}>{value}</a>;
        },
      },
      {
        name: 'srName',
        width: 200,
      },
      {
        name: 'object',
        width: 150,
        renderer: ({ record }) => {
          return this.handleObjectInfo(record);
        },
      },
      {
        name: 'srStatusMeaning',
        width: 135,
        renderer: ({ record }) => {
          const { srStatus, srStatusMeaning } = record.toData();
          return statusColors[srStatus] ? (
            <Tag
              style={{ color: statusColors[srStatus].fontColor, border: 0 }}
              color={statusColors[srStatus].bgColor}
            >
              {srStatusMeaning}
            </Tag>
          ) : (
            <Tag style={{ color: '#000', border: 0 }} color="#fff">
              {srStatusMeaning}
            </Tag>
          );
        },
      },
      {
        name: 'plannerName',
        width: 100,
      },
      {
        name: 'basicTypeFlag',
        renderer: ({ value, record }) =>
          record.get('woId')
            ? value
              ? intl.get(`${prompt}.view.message.sub`).d('委外')
              : intl.get(`${prompt}.view.message.wo`).d('工作单')
            : '-',
      },
      {
        name: 'woNum',
        width: 100,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() =>
                this.handleWoView(record.get('basicTypeFlag'), record.get('woId'), value)
              }
            >
              {value || '-'}
            </a>
          );
        },
      },
      {
        name: 'ownerName',
        width: 100,
      },
      {
        name: 'operate',
        width: 165,
        align: 'center',
        lock: 'right',
        header: intl.get('hzero.common.button.action').d('操作'),
        renderer: ({ record }) => {
          const canEditFlag =
            record.get('srStatus') !== 'DRAFT' && record.get('srStatus') !== 'REJECTED';
          const canRecallFlag = !['APPROVED', 'APPROVING'].includes(record.get('srStatus'));
          const canAppraiseFlag =
            record.get('srStatus') === 'CLOSED' && record.get('enableEvaluateFlag') === 1;
          return (
            <React.Fragment>
              <a
                disabled={canEditFlag}
                onClick={() => this.handleEdit(record)}
                style={{ marginRight: 5 }}
              >
                {intl.get('hzero.common.button.edit').d('编辑')}
              </a>
              <Divider type="vertical" />
              <a
                disabled={canRecallFlag}
                onClick={() => this.handleRecall(record)}
                style={{ margin: '0 5px' }}
              >
                {intl.get('hzero.common.status.recall').d('撤回')}
              </a>
              <Divider type="vertical" />
              <a
                disabled={!canAppraiseFlag}
                onClick={() => this.handleAppraise(record)}
                style={{ marginLeft: 5 }}
              >
                {intl.get(`${prompt}.button.appraise`).d('评价')}
              </a>
            </React.Fragment>
          );
        },
      },
    ];
  }

  render() {
    return (
      <React.Fragment>
        <Header title={intl.get(`${prompt}.view.title.serviceApplyCurrent`).d('我的服务申请')}>
          {this.btns}
        </Header>
        <Content>
          <Table
            key="serviceApplyCurrentList"
            customizedCode="AORI.SERVICE_APPLY_CURRENT.LIST"
            dataSet={this.props.listDS}
            columns={this.columns}
            queryFieldsLimit={3}
            queryFields={{
              srStatus: <Select dataSet={this.props.listDS} name="srStatus" />,
              reportDateTo: (
                <DateTimePicker
                  dataSet={this.props.listDS}
                  name="reportDateTo"
                  defaultTime={moment().endOf('day')}
                />
              ),
            }}
          />
        </Content>
      </React.Fragment>
    );
  }
}
