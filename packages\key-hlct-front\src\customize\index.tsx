import { mapCustomize } from 'utils/customize';
// 这个方法必须
const setWorkflowApproveForm = formConfig => {
  mapCustomize.set({
    module: 'hzero-front-hwkf',
    feature: 'workflowApproveForm',
    key: formConfig.code,
    data: { component: formConfig.component },
  });
};
// 审批表单测试
setWorkflowApproveForm({
  code: 'NC_REPORT_NUM_YQ', // 表单编码，和表单url上的对应
  // component参数目前支持Function component或者import导入的es module
  component: () => import('./components/NcReportComponent/NcReportDetail'),
});
