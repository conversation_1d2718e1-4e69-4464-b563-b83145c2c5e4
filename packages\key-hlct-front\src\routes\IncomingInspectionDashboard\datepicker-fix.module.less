/*
 * DatePicker样式修复文件
 * 修复选中状态和今天状态冲突的问题
 */

// 只修复必要的样式冲突，不覆盖主样式文件中的完整定义
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  // 确保弹窗正确显示

  // 确保日历头部显示正确
  .c7n-pro-calendar-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #ffffff !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;
    height: auto !important;

    // 年月显示文字
    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select,
    .c7n-pro-calendar-header-year,
    .c7n-pro-calendar-header-month {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #ffffff !important;
      background: transparent !important;
      border: none !important;
      font-size: 18px !important;
      font-weight: 500 !important;
      cursor: pointer !important;
      line-height: 1.2 !important;

      &:hover {
        color: #4a90e2 !important;
      }
    }

    // 所有导航按钮样式
    button,
    .c7n-pro-calendar-prev-year-btn,
    .c7n-pro-calendar-prev-month-btn,
    .c7n-pro-calendar-next-month-btn,
    .c7n-pro-calendar-next-year-btn,
    .c7n-pro-calendar-header-prev-year,
    .c7n-pro-calendar-header-prev-month,
    .c7n-pro-calendar-header-next-month,
    .c7n-pro-calendar-header-next-year {
      display: inline-flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #b9d4ff !important;
      background: transparent !important;
      border: none !important;
      font-size: 18px !important;
      cursor: pointer !important;
      align-items: center !important;
      justify-content: center !important;
      width: 28px !important;
      height: 28px !important;
      line-height: 1 !important;

      &:hover {
        color: #4a90e2 !important;
        background: rgba(74, 144, 226, 0.1) !important;
        border-radius: 4px !important;
      }

      // 确保按钮内的图标或文字显示
      * {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
      }
    }
  }

  // 日历主体样式
  .c7n-pro-calendar-body {
    table {
      td,
      th {
        text-align: center !important;
        vertical-align: middle !important;
        padding: 4px !important;
        width: 14.28% !important;
        height: 36px !important;
        box-sizing: border-box !important;
      }
    }
  }

  // 日历底部样式
  .c7n-pro-calendar-footer {
    display: none;
  }

  // 修复选中状态和今天状态的冲突
  .c7n-pro-calendar-body tbody td {
    // 确保所有日期单元格都显示白色文字
    color: #ffffff !important;

    // 基础日期样式
    .c7n-pro-calendar-date {
      color: #ffffff !important;
      background: transparent !important;
      border: none !important;
      border-radius: 50% !important;
      width: 32px !important;
      height: 32px !important;
      line-height: 32px !important;
      text-align: center !important;
      font-size: 14px !important;
      margin: 1px !important;
      padding: 0 !important;
      box-sizing: border-box !important;
      cursor: pointer !important;
      transition: all 0.2s ease !important;
    }

    // cell-inner 元素样式
    .c7n-pro-calendar-cell-inner {
      color: #ffffff !important;
      width: 100% !important;
      height: 100% !important;
      font-size: 16px !important;
      line-height: 32px !important;
      text-align: center !important;
      font-weight: 500 !important;
    }

    // 选中状态样式
    &.c7n-pro-calendar-selected {
      .c7n-pro-calendar-cell-inner {
        background: #4f8efe !important;
        color: #ffffff !important;
        border-radius: 50% !important;
        width: 32px !important;
        height: 32px !important;
        line-height: 32px !important;
        text-align: center !important;
        font-size: 16px !important;
        font-weight: bold !important;
        box-shadow: 0 0 8px rgba(79, 142, 254, 0.6) !important;
      }
    }

    // 今天日期 - 仅当不是选中状态时，圆形浅蓝色背景
    &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
      background: rgba(74, 144, 226, 0.5) !important;
      color: #ffffff !important;
      border: none !important;
      border-radius: 50% !important;
      font-weight: bold !important;
      width: 32px !important;
      height: 32px !important;
      line-height: 32px !important;
    }

    // 悬停效果 - 圆形
    &:hover .c7n-pro-calendar-date {
      background-color: rgba(74, 144, 226, 0.3) !important;
      color: #ffffff !important;
      border: none !important;
      border-radius: 50% !important;
      width: 32px !important;
      height: 32px !important;
      line-height: 32px !important;
    }

    // 非本月日期样式
    &.c7n-pro-calendar-old,
    &.c7n-pro-calendar-new {
      .c7n-pro-calendar-cell-inner {
        opacity: 0.5 !important;
        color: #d1d8eb !important;
        background: transparent !important;
        border: none !important;
        border-radius: 50% !important;
        width: 32px !important;
        height: 32px !important;
        line-height: 32px !important;
        text-align: center !important;
        font-size: 16px !important;
        font-weight: 500 !important;
      }

      // 非本月日期的悬停效果
      &:hover .c7n-pro-calendar-cell-inner {
        background-color: rgba(101, 106, 120, 0.2) !important;
        color: #d1d8eb !important;
        opacity: 0.7 !important;
      }
    }
  }

  // 确保日历头部显示正确
  .c7n-pro-calendar-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #ffffff !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;
    height: auto !important;

    // 左侧导航按钮组
    .c7n-pro-calendar-header-left {
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
    }

    // 中间年月显示
    .c7n-pro-calendar-header-center {
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
    }

    // 右侧导航按钮组
    .c7n-pro-calendar-header-right {
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
    }

    // 年月显示文字
    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select,
    .c7n-pro-calendar-header-year,
    .c7n-pro-calendar-header-month {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #ffffff !important;
      background: transparent !important;
      border: none !important;
      font-size: 18px !important;
      font-weight: 500 !important;
      cursor: pointer !important;
      line-height: 1.2 !important;

      &:hover {
        color: #4a90e2 !important;
      }
    }

    // 所有导航按钮样式
    button,
    .c7n-pro-calendar-prev-year-btn,
    .c7n-pro-calendar-prev-month-btn,
    .c7n-pro-calendar-next-month-btn,
    .c7n-pro-calendar-next-year-btn,
    .c7n-pro-calendar-header-prev-year,
    .c7n-pro-calendar-header-prev-month,
    .c7n-pro-calendar-header-next-month,
    .c7n-pro-calendar-header-next-year {
      display: inline-flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #b9d4ff !important;
      background: transparent !important;
      border: none !important;
      font-size: 18px !important;
      cursor: pointer !important;
      align-items: center !important;
      justify-content: center !important;
      width: 28px !important;
      height: 28px !important;
      line-height: 1 !important;

      &:hover {
        color: #4a90e2 !important;
        background: rgba(74, 144, 226, 0.1) !important;
        border-radius: 4px !important;
      }

      // 确保按钮内的图标或文字显示
      * {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
      }
    }
  }

  // 确保星期标题行是白色
  .c7n-pro-calendar-body thead th {
    color: #ffffff !important;
  }

  // 确保底部按钮是白色
  .c7n-pro-calendar-footer {
    display: none;
    border: 0;
  }
}

// 额外的全局样式确保所有日历弹窗都显示白色字体
:global {
  .c7n-pro-calendar-picker-popup,
  .c7n-pro-popup {
    &[class*='custom-datepicker'] {
      // 确保所有文字都是白色
      color: #ffffff !important;

      // 强制显示头部 - 最高优先级
      .c7n-pro-calendar-header,
      .c7n-pro-calendar-picker-header,
      .c7n-pro-calendar-panel-header {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 100% !important;
        height: auto !important;
        min-height: 40px !important;
        padding: 8px 12px !important;
        justify-content: space-between !important;
        align-items: center !important;
        background: transparent !important;
        position: relative !important;
        z-index: 10 !important;

        // 强制显示所有子元素
        * {
          display: inline-block !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #ffffff !important;
          font-size: 16px !important;
        }

        // 导航按钮
        button,
        a,
        span {
          display: inline-flex !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #b9d4ff !important;
          background: transparent !important;
          border: none !important;
          cursor: pointer !important;
          align-items: center !important;
          justify-content: center !important;
          min-width: 24px !important;
          min-height: 24px !important;

          &:hover {
            color: #4a90e2 !important;
          }
        }
      }

      // 日历单元格
      .c7n-pro-calendar-cell {
        color: #ffffff !important;

        .c7n-pro-calendar-date {
          color: #ffffff !important;
        }

        .c7n-pro-calendar-cell-inner {
          color: #ffffff !important;
          font-size: 16px !important;
          font-weight: 500 !important;
          line-height: 32px !important;
        }

        // 非本月日期样式
        .c7n-pro-calendar-old,
        .c7n-pro-calendar-new {
          .c7n-pro-calendar-cell-inner {
            color: #d1d8eb !important;
            opacity: 0.5 !important;
            font-size: 16px !important;
            font-weight: 500 !important;
          }
        }

        // 选中状态样式
        .c7n-pro-calendar-selected {
          .c7n-pro-calendar-cell-inner {
            background: #4f8efe !important;
            color: #ffffff !important;
            border-radius: 50% !important;
            font-weight: bold !important;
            font-size: 16px !important;
            box-shadow: 0 0 8px rgba(79, 142, 254, 0.6) !important;
          }
        }
      }

      // 星期标题
      .c7n-pro-calendar-body thead th {
        color: #ffffff !important;
      }

      // 月份年份选择器和导航按钮
      .c7n-pro-calendar-header {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #ffffff !important;
        justify-content: space-between !important;
        align-items: center !important;

        .c7n-pro-calendar-year-select,
        .c7n-pro-calendar-month-select {
          display: inline-block !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #ffffff !important;
          background: transparent !important;
          border: none !important;
          font-size: 16px !important;
          font-weight: 500 !important;
        }

        button,
        .c7n-pro-calendar-prev-year-btn,
        .c7n-pro-calendar-prev-month-btn,
        .c7n-pro-calendar-next-month-btn,
        .c7n-pro-calendar-next-year-btn {
          display: inline-flex !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #b9d4ff !important;
          background: transparent !important;
          border: none !important;
          cursor: pointer !important;
        }
      }

      // 确保选中状态是圆形
      .c7n-pro-calendar-body tbody td {
        &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
          background-color: #4a90e2 !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 50% !important;
          font-weight: bold !important;
          box-shadow: 0 0 8px rgba(74, 144, 226, 0.6) !important;
          width: 32px !important;
          height: 32px !important;
          line-height: 32px !important;
        }

        &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
          background: rgba(74, 144, 226, 0.5) !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 50% !important;
          font-weight: bold !important;
          width: 32px !important;
          height: 32px !important;
          line-height: 32px !important;
        }

        &:hover .c7n-pro-calendar-date {
          background-color: rgba(74, 144, 226, 0.3) !important;
          border-radius: 50% !important;
          width: 32px !important;
          height: 32px !important;
          line-height: 32px !important;
        }
      }

      // 底部按钮
      .c7n-pro-calendar-footer {
        display: none;
      }

      // 确保所有子元素都继承白色
      * {
        color: inherit !important;
      }
    }
  }
}

// 最强制的头部显示规则 - 针对所有可能的情况
:global {
  // 针对所有日历弹窗
  .c7n-pro-calendar-picker-popup,
  .c7n-pro-popup,
  .c7n-pro-calendar-popup {
    // 头部区域强制显示
    .c7n-pro-calendar-header,
    .c7n-pro-calendar-picker-header,
    .c7n-pro-calendar-panel-header,
    div[class*="header"],
    div[class*="Header"] {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      width: 100% !important;
      height: auto !important;
      min-height: 40px !important;
      padding: 8px 12px !important;
      justify-content: space-between !important;
      align-items: center !important;
      background: transparent !important;
      position: relative !important;
      z-index: 10 !important;
      color: #ffffff !important;

      // 所有子元素强制显示
      > * {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #ffffff !important;
        font-size: 16px !important;
        line-height: 1.2 !important;
      }

      // 按钮和链接
      button,
      a,
      span,
      div {
        display: inline-flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #b9d4ff !important;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 20px !important;
        min-height: 20px !important;
        font-size: 16px !important;

        &:hover {
          color: #4a90e2 !important;
        }
      }
    }

    // 确保年月文字显示
    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select,
    span[class*="year"],
    span[class*="month"],
    div[class*="year"],
    div[class*="month"] {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #ffffff !important;
      font-size: 18px !important;
      font-weight: 500 !important;
      background: transparent !important;
      border: none !important;
    }

    // 确保导航按钮显示
    button[class*="prev"],
    button[class*="next"],
    a[class*="prev"],
    a[class*="next"],
    span[class*="prev"],
    span[class*="next"] {
      display: inline-flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #b9d4ff !important;
      font-size: 18px !important;
      width: 28px !important;
      height: 28px !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
}


