/*
 * DatePicker样式修复文件 - 简化版本
 * 只修复核心显示问题，减少性能影响
 */

// 核心弹窗样式
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: #0c1a3e !important;
  border: 1px solid #1a3a8b !important;
  border-radius: 4px !important;
  padding: 1rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
  min-width: 320px !important;
  width: 320px !important;
  min-height: 350px !important;
  z-index: 9999 !important;

  // 基础文字颜色
  * {
    color: #ffffff !important;
  }

  // 日历头部
  .c7n-pro-calendar-header {
    display: flex !important;
    visibility: visible !important;
    color: #ffffff !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;

    // 导航按钮
    button {
      color: #b9d4ff !important;
      background: transparent !important;
      border: none !important;
      cursor: pointer !important;

      &:hover {
        color: #4a90e2 !important;
      }
    }
  }

  // 日期单元格
  .c7n-pro-calendar-body tbody td {
    color: #ffffff !important;

    .c7n-pro-calendar-date {
      color: #ffffff !important;
      border-radius: 50% !important;
      width: 32px !important;
      height: 32px !important;
      line-height: 32px !important;
      cursor: pointer !important;
    }

    // 选中状态
    &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
      background: #4f8efe !important;
      color: #ffffff !important;
      font-weight: bold !important;
    }

    // 今天日期
    &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
      background: rgba(74, 144, 226, 0.5) !important;
      font-weight: bold !important;
    }

    // 悬停效果
    &:hover .c7n-pro-calendar-date {
      background-color: rgba(74, 144, 226, 0.3) !important;
    }
  }

}

