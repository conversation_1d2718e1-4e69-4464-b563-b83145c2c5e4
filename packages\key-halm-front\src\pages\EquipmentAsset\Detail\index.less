// 头部高度
@header-height: 48px;
// 内容区上方留白高度
@content-margin-top: 8px;
// 左侧隐藏时图标的宽度
@hidden-icon-width: 38px;
// 左侧和右侧中间的间隙宽度
@left-and-right-middle-width: 8px;

.text-over-flow {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.clear-float {
  display: block;
  clear: both;
  content: '';
}

// 清浮
.clear-fix {
  .clear-float();

  &::before,
  &::after {
    .clear-float();
  }
}

// 页面容器
.container {
  height: inherit;

  .content-margin-top {
    height: @content-margin-top;
  }

  .content {
    position: relative;
    height: calc(100% - @header-height - @content-margin-top);
    min-height: max-content;
    overflow-y: auto;
    background: #fff;

    // 清除子类浮动
    .clear-fix();

    // 新建时左侧隐藏
    .new-left-none {
      display: none;
    }

    // 新建时左侧和右侧中间间隙消失
    .new-left-and-right-middle-none {
      display: none;
    }

    // 新建时右侧占全部宽度
    .new-right-all-width {
      width: 100%;
      background: #fff;
    }

    // 当左侧展开时，左侧的布局
    .left-is-show {
      // float: left;
      position: fixed;
      width: 340px;
      background: #fff;
    }

    // 当左侧隐藏时，左侧的布局
    .left-is-hidden {
      // float: left;
      position: fixed;
      width: @hidden-icon-width;
      background: #fff;
    }

    // 左侧和右侧之间的间隙
    .left-and-right-middle-width {
      position: relative;
      float: left;
      width: @left-and-right-middle-width;
      height: 100%;
      min-height: 100%;

      div {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f4f5f7;
      }
    }

    .nav-list-show-margin-left {
      margin-left: 340px;
    }

    .nav-list-hidden-margin-left {
      margin-left: @hidden-icon-width;
    }

    // 当左侧展开时，右侧的布局
    .right-layout-when-left-show {
      float: right;
      width: calc(100% - 340px - @left-and-right-middle-width);
    }

    // 当左侧隐藏时，右侧的布局
    .right-layout-when-left-hidden {
      float: right;
      width: calc(100% - @hidden-icon-width - @left-and-right-middle-width);
    }

    // 右侧上
    .right-top {
      height: auto;
      padding: 12px 16px 12px 12px;
      display: flex;
      .right-top-img {
        margin-right: 12px;
      }

      .right-top-text {
        flex: 1;
        .title-line {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          line-height: 20px;
          margin-top: 4px;
        }
        .num-line {
          margin: 8px 0;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #4f5972;
          font-weight: 400;
          line-height: 17px;
        }
        .divider {
          height: 1px;
          background: #eee;
        }
        .others-line {
          display: flex;
          font-size: 12px;
          color: #666660;
          line-height: 17px;
          margin: 8px 0 3px 0;
          .field {
            height: 17px;
            width: 25%;
            margin-right: 8px;
            .field-label {
              font-family: PingFangSC-Regular;
              font-weight: 400;
              text-align: right;
              .text-over-flow();
              width: 40%;
            }
            .field-value {
              font-family: PingFangSC-Medium;
              font-weight: 500;
              .text-over-flow();
              width: 60%;
            }
          }
        }
      }
    }

    // 右侧上的底部留白
    .right-top-margin-bottom {
      height: 8px;
      background: #f4f5f7;
    }

    // 右侧下
    .right-bot {
      padding: 16px;
    }
  }
}

.equipment-asset-detail {
  :global {
    // tree父节点节点收起展开的图标设置为三角形，不显示文件夹的图片
    .c7n-tree.c7n-tree .c7n-tree-treenode span.c7n-tree-switcher {
      background-image: none;
    }

    .c7n-tree.c7n-tree .c7n-tree-treenode span.c7n-tree-switcher.c7n-tree-switcher_open {
      background-image: none;
    }

    .c7n-tree.c7n-tree .c7n-tree-treenode span.c7n-tree-switcher .icon {
      display: inline-block;
    }

    // 叶节点
    .c7n-tree.c7n-tree .c7n-tree-treenode span.c7n-tree-switcher.c7n-tree-switcher-noop {
      background-image: none;
    }

    // 改变tree节点被选中的样式
    .c7n-tree-treenode.c7n-tree-treenode-selected {
      color: #3889ff;
      background: rgba(79, 125, 231, 0.1);
    }

    .c7n-tree-treenode.c7n-tree-treenode-selected:before {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: #3889ff !important;
      border: none;
      content: '';
    }

    .c7n-tree.c7n-tree .c7n-tree-treenode .c7n-tree-node-content-wrapper.c7n-tree-node-selected {
      background-color: transparent;
    }

    // 表格出现重复的右边框
    .c7n-pro-table-thead .c7n-pro-table-cell:last-child {
      border-right: none !important;
    }
    .c7n-pro-upload {
      width: 100%;
      // 编辑图片预览
      .c7n-pro-upload-list-item {
        .c7n-pro-upload-list-item-name {
          overflow: hidden;
          white-space: pre;
          text-overflow: ellipsis;
        }
      }
    }
  }

  :global {
    .zmage-img {
      width: 1.5rem;
    }
  }
}

:global {
  #zmageControl {
    top: 0;
    right: 0;
    height: 0.6rem;
  }

  #zmageBackground {
    opacity: 0.5 !important;
  }

  #zmageControlRotateLeft {
    width: 0.5rem;
    height: 0.5rem;

    svg {
      width: 0.5rem;
      height: 0.5rem;
    }
  }

  #zmageControlRotateRight {
    width: 0.5rem;
    height: 0.5rem;

    svg {
      width: 0.5rem;
      height: 0.5rem;
    }
  }

  #zmageControlZoom {
    width: 0.5rem;
    height: 0.5rem;

    svg {
      width: 0.5rem;
      height: 0.5rem;
    }
  }

  #zmageControlClose {
    width: 0.5rem;
    height: 0.5rem;

    svg {
      width: 0.5rem;
      height: 0.5rem;
    }
  }
}

// 变更记录侧弹窗样式
.change-logs-modal-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .title-desc {
    flex: 1;
    font-weight: bold;
  }

  .title-img {
    width: 20px;
    object-fit: contain;
  }
}

.detail-record {
  width: 95%;
  padding-top: 10px;
  padding-right: 20px;
  font-weight: normal;
  font-size: 12px;
  text-align: right;
  border-top: 1px solid #bfbfbf;
}
