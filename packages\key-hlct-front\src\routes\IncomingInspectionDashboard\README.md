# 来料检验实时看板模块文档

## 模块概述

来料检验实时看板（IncomingInspectionDashboard）是一个用于实时监控来料检验状态的数据可视化看板系统。该模块提供了全面的检验进度跟踪、不良统计分析、物料和供应商统计等功能，帮助质量管理人员实时掌握来料检验情况。

## 目录结构

```
IncomingInspectionDashboard/
├── index.tsx                    # 主组件文件
├── index.module.less           # 主样式文件
├── datepicker-fix.module.less  # 日期选择器样式修复
├── pieChartHelper.ts           # 3D饼图辅助函数
├── components/                 # 组件目录
│   └── ECharts.js             # 自定义ECharts组件
├── services/                   # 服务层
│   ├── index.ts               # API接口定义和服务
│   ├── mockData.ts            # 模拟数据
│   └── mockService.ts         # 模拟服务
└── stores/                     # 数据存储（如有）
```

## 核心功能模块

### 1. 时间控制模块
- **功能**: 提供时间范围选择功能
- **默认时间**: 当前时间往前一个月到当前时间
- **特性**: 
  - 动态计算默认时间范围
  - 支持自定义时间范围查询
  - 实时时钟显示

### 2. 进度统计模块
- **功能**: 显示来料检验的整体进度情况
- **数据类型**: `ProgressStatsData`
- **统计维度**:
  - 待检验数量 (pending)
  - 超期数量 (overdue) 
  - 检验中数量 (inProgress)
  - 已完成数量 (completed)
- **可视化**: 3D饼图展示

### 3. 不良统计模块
- **功能**: 统计和展示不良项目的分布情况
- **数据类型**: `DefectiveStatsData[]`
- **统计维度**:
  - 不良项目名称
  - 不良数量
  - 不良比例
- **可视化**: Top5不良项目柱状图

### 4. 不良明细模块
- **功能**: 展示具体的不良检验记录
- **数据类型**: `DefectiveDetailData`
- **包含信息**:
  - 不良项目详情
  - 检验单号
  - 物料信息
  - 供应商信息
  - 检验员信息
  - 创建时间
- **特性**: 
  - 支持分页加载
  - 点击检验单号可跳转详情
  - 新加载数据动画效果

### 5. 物料统计模块
- **功能**: 按物料维度统计检验情况
- **数据类型**: `MaterialStatsData`
- **统计维度**:
  - 物料基本信息
  - 到货批次数量
  - 检验总数
  - 合格数量
  - 合格率
- **特性**:
  - 支持供应商筛选
  - 分页加载
  - 动画效果

### 6. 供应商统计模块
- **功能**: 按供应商维度统计检验情况
- **数据类型**: `SupplierStatsData`
- **统计维度**:
  - 供应商基本信息
  - 到货批次数量
  - 检验总数
  - 合格数量
  - 合格率
- **特性**:
  - 支持物料筛选
  - 分页加载
  - 动画效果

## 技术架构

### 前端技术栈
- **React**: 主框架
- **TypeScript**: 类型安全
- **ECharts**: 数据可视化
- **Moment.js**: 时间处理
- **Choerodon UI Pro**: UI组件库
- **Less**: 样式预处理

### 架构设计
模块采用分层架构设计，包含用户界面层、状态管理层、服务层、数据层和工具层。各层职责清晰，便于维护和扩展。

### 数据流架构
```
用户交互 → 主组件 → 服务层 → API接口 → 状态管理 → UI更新
```

数据流采用单向数据流设计，通过React Hooks进行状态管理，确保数据的一致性和可预测性。

### 状态管理
使用React Hooks进行状态管理：
- `progressStats`: 进度统计数据
- `defectiveStats`: 不良统计数据
- `materialStats`: 物料统计数据
- `supplierStats`: 供应商统计数据
- 各种分页状态和加载状态

## API接口

### 基础配置
- **API基础路径**: `/inja-qms-tznq/v1`
- **组织ID**: 通过 `getCurrentOrganizationId()` 获取

### 主要接口

#### 1. 进度统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/progress-stats/ui
参数: startDate, endDate
返回: ProgressStatsData
```

#### 2. 不良统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/defective-stats/ui
参数: startDate, endDate
返回: DefectiveStatsData[]
```

#### 3. 不良明细接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/defective-detail/ui
参数: startDate, endDate, supplierId?, materialId?, pageNum, pageSize
返回: DefectiveDetailResponse
```

#### 4. 物料统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/material-stats/ui
参数: startDate, endDate, supplierId?, pageNum, pageSize
返回: MaterialStatsResponse
```

#### 5. 供应商统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/supplier-stats/ui
参数: startDate, endDate, materialId?, pageNum, pageSize
返回: SupplierStatsResponse
```

## 数据类型定义

### 核心数据类型
```typescript
// 进度统计数据
interface ProgressStatsData {
  pending: number;      // 待检验
  overdue: number;      // 超期
  inProgress: number;   // 检验中
  completed: number;    // 已完成
}

// 不良统计数据
interface DefectiveStatsData {
  name: string;         // 不良项目名称
  count: number;        // 不良数量
  ratio: number;        // 不良比例
}

// 物料统计数据
interface MaterialStatsData {
  materialId: number;
  materialCode: string;
  material: string;
  arrivalBatchCount: number;
  totalInspections: number;
  passedInspections: number;
  passRateNum: number;
  passRate: string;
}

// 供应商统计数据
interface SupplierStatsData {
  supplierId: number;
  supplierCode: string;
  supplier: string;
  arrivalBatchCount: number;
  totalInspections: number;
  passedInspections: number;
  passRateNum: number;
  passRate: string;
}
```

### 扩展类型
为了支持前端状态管理，定义了扩展类型：
```typescript
interface ExtendedSupplierStatsData extends SupplierStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}

interface ExtendedMaterialStatsData extends MaterialStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}
```

## 特色功能

### 1. 3D饼图可视化
- 使用ECharts GL实现3D饼图效果
- 支持鼠标悬停和选中交互
- 动态数据更新

### 2. 分页加载机制
- 支持无限滚动加载
- 新数据加载动画效果
- 加载状态管理

### 3. 数据筛选联动
- 物料和供应商数据相互筛选
- 动态更新筛选选项
- 保持筛选状态

### 4. 全屏显示
- 支持看板全屏模式
- 响应式布局适配
- 快捷键支持

### 5. 实时时钟
- 实时显示当前时间
- 星期显示
- 格式化时间展示

## 样式特性

### 1. 深色主题
- 深蓝色背景色调
- 科技感UI设计
- 高对比度文字

### 2. 响应式设计
- 适配不同屏幕尺寸
- 弹性布局
- 组件自适应

### 3. 动画效果
- 数据加载动画
- 悬停交互效果
- 平滑过渡动画

## 开发和维护

### 开发环境配置
1. 确保已安装所需依赖
2. 配置API接口地址
3. 启动开发服务器

### Mock数据支持
- 提供完整的Mock数据服务
- 支持开发环境调试
- 模拟真实API响应

### 错误处理
- API请求错误处理
- 数据异常处理
- 用户友好的错误提示

### 性能优化
- 使用React.memo优化渲染
- 防抖处理用户交互
- 图表实例复用

## 使用说明

### 基本使用
1. 选择查询时间范围（默认为最近一个月）
2. 查看各项统计数据
3. 点击相关数据进行详细查看
4. 使用筛选功能精确查询

### 交互操作
- 点击检验单号跳转详情页面
- 使用物料/供应商筛选功能
- 滚动加载更多数据
- 全屏查看看板

### 注意事项
- 确保网络连接正常
- 注意时间范围选择的合理性
- 大数据量查询可能需要等待时间

## 扩展开发

### 添加新的统计维度
1. 在services中定义新的数据类型
2. 添加对应的API接口
3. 在主组件中添加状态管理
4. 实现UI展示组件

### 自定义图表类型
1. 在components中创建新的图表组件
2. 配置ECharts选项
3. 集成到主看板中

### 样式定制
1. 修改index.module.less中的样式变量
2. 添加新的样式类
3. 确保响应式兼容性

## 组件详细说明

### 主组件 (index.tsx)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/index.tsx`

**主要功能**:
- 看板整体布局和状态管理
- 数据获取和处理逻辑
- 用户交互事件处理
- 全屏模式控制

**核心Hooks**:
```typescript
// 时间状态
const [startDate, setStartDate] = useState(moment().subtract(1, 'month').startOf('day'));
const [endDate, setEndDate] = useState(moment().endOf('day'));

// 数据状态
const [progressStats, setProgressStats] = useState<ProgressStatsData>();
const [defectiveStats, setDefectiveStats] = useState<DefectiveStatsData[]>([]);
const [materialPageData, setMaterialPageData] = useState<ExtendedMaterialStatsData[]>([]);
const [supplierPageData, setSupplierPageData] = useState<ExtendedSupplierStatsData[]>([]);

// 分页状态
const [materialPageInfo, setMaterialPageInfo] = useState({
  current: 1,
  pageSize: 20,
  total: 0,
  hasMore: true,
  loading: false,
});
```

### ECharts组件 (components/ECharts.js)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/components/ECharts.js`

**主要功能**:
- 封装ECharts实例管理
- 支持强制撑满宽度模式
- 自动响应窗口大小变化
- 图表配置动态更新

**核心方法**:
```javascript
// 初始化图表
initChart() {
  if (this.echart_react.current) {
    this.echart = echarts.init(this.echart_react.current);
  }
}

// 调整配置以适应全宽度
adjustOptionForFullWidth(option) {
  // 强制设置图表宽度相关配置
  return modifiedOption;
}
```

### 3D饼图辅助函数 (pieChartHelper.ts)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/pieChartHelper.ts`

**主要功能**:
- 生成3D饼图的参数方程
- 计算扇形的曲面参数
- 处理选中和悬停状态

**核心函数**:
```typescript
// 生成扇形的曲面参数方程
export function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
  // 计算中点比例和弧度
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;

  // 返回参数方程对象
  return {
    u: { min: -Math.PI, max: Math.PI * 3, step: Math.PI / 32 },
    v: { min: 0, max: Math.PI * 2, step: Math.PI / 20 },
    x: (u, v) => { /* 计算x坐标 */ },
    y: (u, v) => { /* 计算y坐标 */ },
    z: (u, v) => { /* 计算z坐标 */ }
  };
}

// 生成3D饼图数据
export function getPie3D(pieData, internalDiameterRatio) {
  // 处理饼图数据，生成3D效果所需的series配置
}
```

## 服务层详细说明

### API服务 (services/index.ts)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/services/index.ts`

**服务架构**:
```typescript
// 服务对象
export const dashboardService = {
  // 获取进度统计
  async getProgressStats(startDate: string, endDate: string): Promise<ProgressStatsData>

  // 获取不良统计
  async getDefectiveStats(startDate: string, endDate: string): Promise<DefectiveStatsData[]>

  // 获取不良明细（分页）
  async getDefectiveDetail(startDate, endDate, supplierId?, materialId?, pageNum, pageSize): Promise<DefectiveDetailResponse>

  // 获取物料统计（分页）
  async getMaterialStats(startDate, endDate, supplierId?, pageNum, pageSize): Promise<MaterialStatsResponse>

  // 获取供应商统计（分页）
  async getSupplierStats(startDate, endDate, materialId?, pageNum, pageSize): Promise<SupplierStatsResponse>
};
```

**错误处理机制**:
- 每个服务方法都包含try-catch错误处理
- 失败时返回默认数据结构
- 控制台错误日志记录

### Mock服务 (services/mockService.ts)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/services/mockService.ts`

**主要功能**:
- 提供开发环境的模拟数据服务
- 模拟真实API的响应延迟
- 支持分页和筛选逻辑

**Mock数据特点**:
- 数据结构与真实API完全一致
- 包含分页信息和业务逻辑
- 支持参数筛选模拟

## 样式系统详细说明

### 主样式文件 (index.module.less)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/index.module.less`

**样式架构**:
```less
// 全局容器
.dashboardContainer {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1a3e 0%, #1a3a8b 100%);
  color: #ffffff;
  overflow: hidden;
}

// 头部样式
.header {
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  background: rgba(255, 255, 255, 0.05);
}

// 面板样式
.panel {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

// 表格样式
.customTable {
  .tableHeader, .tableHeader2 {
    background: rgba(255, 255, 255, 0.1);
    display: grid;
    padding: 12px 16px;
    font-weight: bold;
  }

  .tableRow, .tableRow2 {
    display: grid;
    padding: 8px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    &.entering {
      animation: slideInFromRight 0.5s ease-out;
    }
  }
}
```

**动画效果**:
```less
// 新数据加载动画
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 悬停效果
.clickableLink {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #64b5f6 !important;
    text-shadow: 0 0 8px rgba(100, 181, 246, 0.6);
  }
}
```

### 日期选择器样式修复 (datepicker-fix.module.less)
**文件路径**: `packages/key-hlct-front/src/routes/IncomingInspectionDashboard/datepicker-fix.module.less`

**主要功能**:
- 修复DatePicker在深色主题下的显示问题
- 自定义弹窗样式以匹配看板主题
- 确保日历组件的正确显示

## 数据流和状态管理

### 数据获取流程
```mermaid
graph TD
    A[组件挂载] --> B[useEffect触发]
    B --> C[调用fetchData]
    C --> D[并行调用多个API]
    D --> E[getProgressStats]
    D --> F[getDefectiveStats]
    D --> G[getMaterialStats]
    D --> H[getSupplierStats]
    E --> I[更新progressStats状态]
    F --> J[更新defectiveStats状态]
    G --> K[更新materialPageData状态]
    H --> L[更新supplierPageData状态]
    I --> M[触发UI重新渲染]
    J --> M
    K --> M
    L --> M
```

### 分页加载机制
```typescript
// 加载更多数据的通用逻辑
const loadMoreData = async (pageInfo, setPageData, setPageInfo, apiCall) => {
  if (pageInfo.loading || !pageInfo.hasMore) return;

  setPageInfo(prev => ({ ...prev, loading: true }));

  try {
    const response = await apiCall(pageInfo.current, pageInfo.pageSize);

    // 为新数据添加动画标记
    const newData = response.content.map((item, index) => ({
      ...item,
      id: pageInfo.current * pageInfo.pageSize + index + 1,
      isNewlyLoaded: true,
    }));

    // 更新数据和分页信息
    setPageData(prev => [...prev, ...newData]);
    setPageInfo(prev => ({
      ...prev,
      current: prev.current + 1,
      loading: false,
      hasMore: response.number < response.totalPages - 1,
      total: response.totalElements,
    }));

    // 移除动画标记
    setTimeout(() => {
      setPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
    }, 100);
  } catch (error) {
    console.error('加载数据失败:', error);
    setPageInfo(prev => ({ ...prev, loading: false }));
  }
};
```

## 性能优化策略

### 1. 组件优化
- 使用React.memo包装子组件
- 合理使用useMemo和useCallback
- 避免不必要的重新渲染

### 2. 数据处理优化
- 分页加载减少单次数据量
- 防抖处理用户输入
- 缓存计算结果

### 3. 图表性能优化
- ECharts实例复用
- 按需更新图表配置
- 合理设置动画时长

### 4. 内存管理
- 组件卸载时清理定时器
- 移除事件监听器
- 销毁图表实例

## 常见问题和解决方案

### 1. 图表显示异常
**问题**: 3D饼图不显示或显示不完整
**解决方案**:
- 检查容器尺寸是否正确
- 确保ECharts GL库已正确加载
- 调用resize方法重新计算尺寸

### 2. 数据加载失败
**问题**: API请求失败或返回空数据
**解决方案**:
- 检查网络连接和API地址
- 验证请求参数格式
- 查看控制台错误信息

### 3. 样式显示问题
**问题**: 深色主题下某些组件显示异常
**解决方案**:
- 检查CSS优先级
- 使用!important强制覆盖
- 确保样式文件正确引入

### 4. 性能问题
**问题**: 大数据量时页面卡顿
**解决方案**:
- 启用虚拟滚动
- 减少单页数据量
- 优化渲染逻辑

---

*文档版本: v1.0*
*最后更新: 2025-08-01*
