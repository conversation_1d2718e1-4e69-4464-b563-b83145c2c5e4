#!/usr/bin/env bash

set -ex # 报错不继续执行

# cicd 函数定义 脚本文件

if [[ ! -n "$TEMP_DIR" ]] ;then
  export TEMP_DIR=/cache/${CI_PROJECT_NAME}-${CI_PROJECT_ID}-${CI_COMMIT_REF_NAME/__*/}-front
fi

export WEBPACK_FS_CACHE=none
export BABEL_CACHE=none

LOCK_FILE=${LOCK_FILE:-yarn.lock}
PKG_Management=${PKG_Management:-yarn}

function install_node_modules(){
  if [[ "$PKG_Management" == "yarn" ]]; then

    yarn install --no-progress --non-interactive
  else
    $PKG_Management install
  fi
}

function transpile_packages(){

  if [ "$NEED_TRANSPILE" != "false" ]; then
    node ./node_modules/umi/bin/umi.js hzero-transpile --common
  fi

}


# 安装 node_modules 依赖
function set_build_cache(){

  echo "gitlab-ci -- 缓存目录: $TEMP_DIR"

  if  [[ "$IS_INSTALL_STAGE" =~ "true" && "$CLEAR_TEMP_DIR" =~ "true" ]] ;then
      echo "==: 清空缓存目录：$TEMP_DIR"
      rm -rf $TEMP_DIR
      rm -rf dist* node_modules
  fi

  if [ ! -d "$TEMP_DIR" ] ;then
    mkdir -p $TEMP_DIR
  fi

  export CURRENT_GIT_HEAD=`git log -1 --pretty=format:"%H"` # 获取当前提交代码版本

  if [ -f "$TEMP_DIR/.commitId" ]; then
    export LAST_BUILD_PARENT_GIT_HEAD=`cat $TEMP_DIR/.commitId` # 获取上一次 build 父项目时的 提交代码版本
    export YARN_LOCK_CHANGE_LOG=`git diff $LAST_BUILD_PARENT_GIT_HEAD $CURRENT_GIT_HEAD --summary --shortstat -- $LOCK_FILE  || echo error` # 对比两次提交版本中的 $LOCK_FILE 是否变化
    if [[ -n "$YARN_LOCK_CHANGE_LOG" && "$IS_INSTALL_STAGE" =~ "true" ]] ;then
      # 如果 $LOCK_FILE 发生变化, 需要更新缓存。
      echo -e "gitlab-ci -- $LOCK_FILE 发生变化, 需要清除之前编译时留下来的缓存。\n\t $YARN_LOCK_CHANGE_LOG"
      export CLEAR_TEMP_DIR=true
      echo "==: $LOCK_FILE 变更, 重新编译"
    else
      echo "==: 执行增量编译"
    fi
  else
      export CLEAR_TEMP_DIR=true
      echo "==: 第一次编译（非增量编译）"
  fi


  if [[ "$CLEAR_TEMP_DIR" == "true" && "x$IS_INSTALL_STAGE" == "xtrue" ]] ; then
    rm -rf ./node_modules
    rm -rf $TEMP_DIR
  fi

  if [[ -f "$TEMP_DIR/node_modules.tar.gz" ]]; then
    tar -zxf $TEMP_DIR/node_modules.tar.gz || (rm -rf node_modules)
  fi

  if  [[  ! -d "node_modules" ]] ;then
      echo "==: 初始化缓存目录：$TEMP_DIR"
      mkdir -p $TEMP_DIR
      rm -rf ./node_modules
      echo "==: 开始安装依赖"
      install_node_modules
      echo $CURRENT_GIT_HEAD > $TEMP_DIR/.commitId
      tar -zcf $TEMP_DIR/node_modules.tar.gz ./node_modules
      echo "==: 结束安装依赖"
      node ./node_modules/umi/bin/umi.js hzero-info
  fi

  if  [[ "$IS_INSTALL_STAGE" =~ "true" ]] ;then
    transpile_packages
  fi

}

# 编译 singleapp
function build_singleapp() {

  # if [ -f "$TEMP_DIR/dist_singleapp.tar.gz" ]; then
  #   tar -zxf $TEMP_DIR/dist_singleapp.tar.gz
  # fi

  rm -rf ./dist_singleapp/packages/hzero_front/package.json
  SKIP_NO_CHANGE_MODULE=true BUILD_SINGLE_PUBLIC_MS=true SKIP_BUILD_DEP=true SKIP_BUILD_CMF=true SKIP_BUILD_DLL=true UMI_ENV=prod BUILD_DIST_PATH=./dist_singleapp NODE_OPTIONS='--max_old_space_size=8196' NODE_PROFILE=production node ./node_modules/umi/bin/umi.js hzero-build --only-build-parent
  # tar -zcf $TEMP_DIR/dist_singleapp.tar.gz ./dist_singleapp

  rm -rf ./dist
  mv ./dist_singleapp ./dist

}

# 编译 parent
function build_parent() {

  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    if [ -f "$TEMP_DIR/dist_parent.tar.gz" ]; then
      tar -zxf $TEMP_DIR/dist_parent.tar.gz || (rm -rf ./dist_parent)
    fi
  fi

  UMI_ENV=prod PARENT_SRC_FILES_REG='^src/|^public/' SKIP_NO_CHANGE_MODULE=${SKIP_NO_CHANGE_MODULE:-true} SKIP_BUILD_DEP=true SKIP_BUILD_DLL=true BUILD_DIST_PATH=./dist_parent NODE_OPTIONS='--max_old_space_size=8196' NODE_PROFILE=production node ./node_modules/umi/bin/umi.js hzero-build --only-build-parent

  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    tar -zcf $TEMP_DIR/dist_parent.tar.gz ./dist_parent

    rm -rf ./dist
    mv ./dist_parent ./dist
  fi

}


# 分布式编译子模块
function build_package() {

  if [ ! -z "$1" ] ;then
    export BUILD_PACKAGE_LIST=$1
  fi

  if [ -z "$BUILD_PACKAGE_LIST" ] ;then
    echo 未传编译子模块的参数: build_package hzero-front-hiam 或者设置 BUILD_PACKAGE_LIST 环境变量, 当前会编译所有子模块
    export BUILD_PACKAGE_LIST="--all-packages"
  fi

  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    if [ -f "$TEMP_DIR/dist_package.tar.gz" ]; then
      tar -zxf $TEMP_DIR/dist_package.tar.gz  || (rm -rf ./dist_package)
    fi
  fi

  echo "==: 开始编译:"$1
  UMI_ENV=prod SKIP_NO_CHANGE_MODULE=${SKIP_NO_CHANGE_MODULE:-true} BUILD_DIST_PATH=./dist_package NODE_OPTIONS='--max_old_space_size=8196' NODE_PROFILE=production node ./node_modules/umi/bin/umi.js hzero-build --only-build-micro $BUILD_PACKAGE_LIST

  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    tar -zcf $TEMP_DIR/dist_package.tar.gz ./dist_package
    mkdir -p dist
    cp -r -n -u ./dist_package/* ./dist/
  fi

}

# 分布式编译子模块
function build_packages() {

  export BUILD_PACKAGES=`node .cicd/gitlab_parallel_get_current_packages.js`;
  if [ -z "$BUILD_PACKAGES" ]; then
    exit
  fi

  if [ -f "$TEMP_DIR/dist_packages_${CI_NODE_INDEX:-1}.tar.gz" ]; then
    tar -zxf $TEMP_DIR/dist_packages_${CI_NODE_INDEX:-1}.tar.gz  || (rm -rf ./dist_packages_${CI_NODE_INDEX:-1})
  fi

  echo "==: 开始编译:"$1
  UMI_ENV=prod SKIP_NO_CHANGE_MODULE=${SKIP_NO_CHANGE_MODULE:-true} BUILD_DIST_PATH=./dist_packages_${CI_NODE_INDEX:-1} NODE_OPTIONS='--max_old_space_size=8196' NODE_PROFILE=production node ./node_modules/umi/bin/umi.js hzero-build --only-build-micro $BUILD_PACKAGES
  tar -zcf $TEMP_DIR/dist_packages_${CI_NODE_INDEX:-1}.tar.gz ./dist_packages_${CI_NODE_INDEX:-1}

  # mv ./dist_packages ./dist
}

# 合并 dist 文件夹函数
function _merge_packages(){
  if  [[ -z "$1" || -z "$2" ]] ; then
    echo "必须传入合并的 dist 文件路径!"
    exit 1
  fi
  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    export MODULE_NAME=`echo $2 | sed -r "s/^.*${1}_(.*)\.tar\.gz/\1/g"`
  else
    export MODULE_NAME=`echo $2 | sed -r "s/^.*${1}_(.*)/\1/g"`
  fi
  echo "==: 合并:"$MODULE_NAME
  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    tar -zxf $TEMP_DIR/${1}_$MODULE_NAME.tar.gz
  fi
  mkdir -p ./${1}/
  cp -r -n -u ./${1}_$MODULE_NAME/* ./${1}/
  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    rm -rf ./${1}_$MODULE_NAME
  fi
}

# 合并子模块分布式编译结果
function merge_packages() {
  if [ -z "$1" ] ;then
    export DIST_PREFIX=dist_packages
  else
    export DIST_PREFIX=$1
  fi
  export -f _merge_packages
  if [ "$NO_SAVE_TEMP_DIR" != "true" ]; then
    find $TEMP_DIR -maxdepth 1 -type f -name "${DIST_PREFIX}_*.tar.gz" | xargs -n 1 -I {} bash -c "set -e ; _merge_packages ${DIST_PREFIX} {}"
  else
    find . -maxdepth 1 -type d -name "${DIST_PREFIX}_*" | xargs -n 1 -I {} bash -c "set -e ; _merge_packages ${DIST_PREFIX} {}"
  fi
  if [ "$DIST_PREFIX" != "dist" ]; then
    mkdir -p dist
    cp -r -n -u ./${DIST_PREFIX}/* ./dist/
    # mv ./${DIST_PREFIX} ./dist
  fi
  node .cicd/refreshMicroConfig.js
  # tar -zcf $TEMP_DIR/${DIST_PREFIX}.tar.gz ./dist
}

# 企业微信机器人通知
function wx_robot_notice () {

  export NOTICE_MSG=${1:-执行成功}
  if  [[ -z "$WX_ROBOT_TOKEN" ]] ; then
    echo "必须设置 WX_ROBOT_TOKEN 环境变量!"
    exit 1
  fi

  # export WX_ROBOT_TOKEN=${WX_ROBOT_TOKEN}

echo '
 {    "msgtype":
     "markdown" , "markdown": {   "content": "流水线 <font color=\"warning\">['${CI_PROJECT_NAME}']('${CI_JOB_URL}')</font>, '${NOTICE_MSG}'\n
     >修改内容: [<font color=\"comment\">'${CI_COMMIT_MESSAGE}'</font>](https://code.choerodon.com.cn/'${CI_PROJECT_NAMESPACE}'/'${CI_PROJECT_NAME}'/-/commit/'${CI_COMMIT_SHA}') \n
     >提交人: '${CI_COMMIT_AUTHOR}'     \n
     >流水线日志: https://code.choerodon.com.cn/'${CI_PROJECT_NAMESPACE}'/'${CI_PROJECT_NAME}'/-/commit/'${CI_COMMIT_SHA}'
     "  }    }
' | curl "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${WX_ROBOT_TOKEN}" -H 'Content-Type:: application/json' -d @-

}

function docker_and_charts_build () {
  kaniko_build --skip-tls-verify=true . docker/Dockerfile
  skopeo_copy
  saveImageMetadata
  chart_build
}

function docker_build(){
  # tar -zxf $TEMP_DIR/dist.tar.gz
  docker login -u ${DOCKER_USER} -p ${DOCKER_PWD} ${DOCKER_REGISTRY}
  docker build -f docker/Dockerfile --pull -t ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG} ${1:-"."}
  docker push ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
  echo "${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}"
}

function docker_and_charts_build2 () {
  docker_build
  chart_build
}
