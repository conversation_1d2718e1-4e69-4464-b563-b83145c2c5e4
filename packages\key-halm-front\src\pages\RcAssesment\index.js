/**
 * 故障字典
 * @date 2020/08/07
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2019,Hand
 */
import React from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { Table, Button, DataSet, Select } from 'choerodon-ui/pro';

import { Bind } from 'lodash-decorators';

import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { enableRender } from 'utils/renderer';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { tableDS } from './Stores/RcAssesmentDS';

@formatterCollections({
  code: ['alm.common', 'alm.component', 'amtc.rcAssesment'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
@connect(() => ({
  tenantId: getCurrentOrganizationId(),
}))
class RcSystems extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  /**
   * 跳转到详情页面
   */
  @Bind()
  handleGotoDetail(evalItemId, editFlag) {
    const { dispatch } = this.props;
    dispatch(
      routerRedux.push({
        pathname: `/rc/rc-assesment/detail/${evalItemId}`,
        state: {
          editFlag,
        },
      })
    );
  }

  /**
   * 新建故障字典
   */
  @Bind()
  handleAddRcSystem() {
    const { dispatch } = this.props;
    dispatch(
      routerRedux.push({
        pathname: `/rc/rc-assesment/create`,
      })
    );
  }

  @Bind()
  handleTypeChange() {
    this.props.listDS.queryDataSet.current.init('objectLov');
  }

  @Bind()
  handleImport() {
    const promptCode = 'amtc.rcAssesment.view';
    openTab({
      key: `/ammt/act/data-import/AORI.MAINTAIN_ACT_IMPORT`,
      title: intl.get(`${promptCode}.title.rcAssessment.import`).d('故障字典导入'),
      search: queryString.stringify({
        action: intl.get(`${promptCode}.title.rcAssessment.import`).d('故障字典导入'),
      }),
    });
  }

  get columns() {
    return [
      {
        name: 'evalItemName',
        renderer: ({ value, record }) => (
          <a onClick={() => this.handleGotoDetail(record.get('evalItemId'), false)}>{value}</a>
        ),
      },
      {
        name: 'evalItemCode',
      },
      {
        name: 'faultSetName',
      },
      {
        name: 'description',
      },
      {
        name: 'enabledFlag',
        align: 'left',
        width: 150,
        renderer: ({ value }) => enableRender(value),
      },
      {
        header: intl.get('hzero.common.button.action').d('操作'),
        width: 150,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              <a onClick={() => this.handleGotoDetail(record.get('evalItemId'), true)}>
                {intl.get('hzero.common.button.edit').d('编辑')}
              </a>
            </span>
          );
        },
      },
    ];
  }

  render() {
    const promptCode = 'amtc.rcAssesment.view';
    return (
      <>
        <Header title={intl.get(`${promptCode}.title.rcAssessment`).d('故障字典')}>
          <Button icon="add" color="primary" onClick={this.handleAddRcSystem}>
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
          <Button icon="vertical_align_top" onClick={this.handleImport} key="import">
            {intl.get('hzero.common.button.import').d('导入')}
          </Button>
        </Header>
        <Content>
          <Table
            key="RcAssesmentList"
            customizedCode="AORI.RC_ASSESMENT.LIST"
            dataSet={this.props.listDS}
            columns={this.columns}
            queryFields={{
              objectType: (
                <Select
                  optionsFilter={i => ['ASSET_SET', 'ASSET'].includes(i.get('value'))}
                  onChange={this.handleTypeChange}
                />
              ),
            }}
          />
        </Content>
      </>
    );
  }
}

export default RcSystems;
