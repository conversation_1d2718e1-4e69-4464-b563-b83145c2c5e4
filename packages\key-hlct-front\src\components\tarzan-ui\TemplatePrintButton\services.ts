/*
 * @Author: 20379 <EMAIL>
 * @Date: 2024-01-24 09:15:59
 * @LastEditors: 20379 <EMAIL>
 * @LastEditTime: 2024-01-29 15:38:44
 * @FilePath: /key-focus-front/packages/key-hmes-front/src/components/tarzan-ui/TemplatePrintButtonNew/services.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description: 模板打印按钮-services
 * @Author: <<EMAIL>>
 * @Date: 2023-07-31 12:56:55
 * @LastEditTime: 2023-08-08 18:42:19
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';


const tenantId = getCurrentOrganizationId();

/**
 * 获取打印模板信息
 * @param printButtonCode string
 * @returns
 */
export function FetchPrintTemplate(printButtonCode: string) {
  return {
    url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-print-functions/print/template/list/ui?printButtonCode=${printButtonCode}`,
    method: 'GET',
  };
}

/**
 * 获取H0打印模板
 * @returns
 */
export function FetchTemplate() {
  return {
    url: `${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html`,
    method: 'GET',
  };
}

/**
 * 获取UReport打印模板
 * @returns
 */
export async function fetchReportTemplate(params) {
  return request(`${BASIC.HRPT_COMMON}/v1/${tenantId}/reports/export/${params.templateUuid}/PRINT?docId=${params.docId}`, {
    method: 'POST',
    body: params,
    responseType: 'blob',
  });
}

/**
 * 记录打印次数
 * @returns
 */
export function RecordPrintRecord() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-object-print-record/record`,
    method: 'POST',
  };
}

export function printPDFBlob(url, params) {
  return request(url, {
    method: 'POST',
    body: params,
    responseType: 'blob',
  });
}

export function printDirect(url, params) {
  return request(url, {
    method: 'POST',
    body: params,
  });
}
