/**
 * 组织关系model
 * @date: 2021-1-25
 * @author: yang.ni <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */

import { getResponse } from '@utils/utils';
import {
  allTreeData,
  addTreeNodes,
  deleteTreeNodes,
  copyTreeNodes,
  cutTreeNodes,
  reorderTreeNodes,
} from '../../services/org/relationMaintainService';

let _localStorage;

if (localStorage) {
  _localStorage = window.localStorage;
}

export default {
  namespace: 'relationMaintain',
  state: {
    // 新组织树相关变量
    halfContainerHeight: 0, // 树容器高度
    treeData: [], // 树结构展示数据
    loading: false, // 手动loading状态
    expandedKeys: [], // 展开项列表
    addOriginDetailIcon: {}, // 点击tree icon 后保存的该条数据
    addOriginDetailNode: {}, // 点击tree node 后保存的该条数据
    dragItem: {}, // 拖动的tree node 数据
    autoExpandParent: true, // 是否自动展开父节点
    searchValue: '', // 模糊搜索值
    orgNodeCopy: {}, // 复制的组织节点信息 新关系复用
    dragGridAsideShow: false, // 分栏开关
    dragGridAsideType: undefined, // 分栏开关
    chooseModalSwitch: false, // modal开关
    focusType: undefined, // 分栏组件类别(详情/新建)
    columns: 1, // 分栏组件分栏数量
    enableFlagVisible: 'Y', // 是否显示禁用组织

    // 临时数据要删除
    originData: [], // 原始数据 数据翻倍时用 (需删除)
  },
  effects: {
    // 获取完整树节点关系
    *allTreeData(_, { call, put }) {
      const enableFlagVisible = _localStorage.getItem('enableFlagVisible') === 'N' ? 'N' : 'Y';
      const _descriptionSequence =
        JSON.parse(_localStorage.getItem('descriptionSequence') || '{}').descriptionSequence || [];
      yield put({
        type: 'updateState',
        payload: {
          enableFlagVisible,
        },
      });

      _descriptionSequence.sort((a, b) => {
        return a.value - b.value;
      });
      const descriptionSequence = [];
      _descriptionSequence.forEach(item => {
        if (item.visible) {
          descriptionSequence.push(item.name);
        }
      });

      const res = yield call(allTreeData, {
        descriptionSequence,
      });
      const list = getResponse(res);
      if (list) {
        // 返回查询的树信息
        return list.rows;
      }
    },

    // 向目标节点添加子节点
    *addTreeNodes({ payload }, { call }) {
      const res = yield call(addTreeNodes, payload);
      const list = getResponse(res);
      return list;
    },

    // 删除目标节点
    *deleteTreeNodes({ payload }, { call }) {
      const res = yield call(deleteTreeNodes, payload);
      const list = getResponse(res);
      return list;
    },

    // 复制节点至目标节点
    *copyTreeNodes({ payload }, { call }) {
      const res = yield call(copyTreeNodes, payload);
      const list = getResponse(res);
      return list;
    },

    // 剪切节点至目标节点
    *cutTreeNodes({ payload }, { call }) {
      const res = yield call(cutTreeNodes, payload);
      const list = getResponse(res);
      return list;
    },

    // 同级排序
    *reorderTreeNodes({ payload }, { call }) {
      const res = yield call(reorderTreeNodes, payload);
      const list = getResponse(res);
      return list;
    },
  },

  reducers: {
    updateState(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
};
