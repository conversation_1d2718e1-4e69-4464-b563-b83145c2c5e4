stages:
  - 安装依赖
  - 构建-packages
  - 发布

合并-packages:
  image: registry.choerodon.com.cn/hzero-fe-public/cibase:pnpm
  stage: 发布
  services:
    - name: registry.cn-shanghai.aliyuncs.com/c7n/cibase:0.11.4
      alias: kaniko
  script:
    - export NEED_TRANSPILE=false
    - source ./.cicd/.hzero_devops.sh
    - merge_packages
    - docker_and_charts_build
  only:
    refs:
      - master
    #  - develop
      - uat
      - pre

安装依赖:
  image: registry.cn-shanghai.aliyuncs.com/c7n/cibase:1.0.0-nodejs-v14.19.0
  stage: 安装依赖
  script:
    - export IS_INSTALL_STAGE=true
    - source ./.cicd/.hzero_devops.sh
    - set_build_cache
  only:
    refs:
      - master
    #  - develop
      - uat
      - pre

packages:
  image: registry.cn-shanghai.aliyuncs.com/c7n/cibase:1.0.0-nodejs-v14.19.0
  stage: 构建-packages
  parallel: 2
  script:
    - export NEED_TRANSPILE=false
    - source ./.cicd/.hzero_devops.sh
    - (set_build_cache && build_packages)
  only:
    refs:
      - master
    #  - develop
      - uat
      - pre
