/* eslint-disable */
// @ts-nocheck

// 获取3d丙图的最高扇区的高度
export function getHeight3D(series, height) {
  series.sort((a, b) => {
    return b.pieData.value - a.pieData.value;
  });
  return (height * 10) / series[0].pieData.value;
}

// 生成扇形的曲面参数方程
export function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  k = typeof k !== 'undefined' ? k : 1 / 3;

  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  const hoverRate = isHovered ? 1.05 : 1;

  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x: (u, v) => {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: (u, v) => {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: (u, v) => {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}

function fomatFloat(num, n) {
  var f = parseFloat(num);
  if (isNaN(f)) {
    return false;
  }
  f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
  var s = f.toString();
  var rs = s.indexOf('.');
  if (rs < 0) {
    rs = s.length;
    s += '.';
  }
  while (s.length <= rs + n) {
    s += '0';
  }
  return s;
}

export function getPie3D(pieData, internalDiameterRatio) {
  const series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  const legendData = [];
  const k = 1 - internalDiameterRatio;

  pieData.sort((a, b) => b.value - a.value);

  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;
    const seriesItem = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
    };

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle = {};
      if (typeof pieData[i].itemStyle.color != 'undefined') {
        itemStyle.color = pieData[i].itemStyle.color;
      }
      if (typeof pieData[i].itemStyle.opacity != 'undefined') {
        itemStyle.opacity = pieData[i].itemStyle.opacity;
      }
      seriesItem.itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    );
    startValue = endValue;
    legendData.push(series[i].name);
  }

  const boxHeight = getHeight3D(series, 20);

  const option = {
    legend: {
      data: legendData,
      orient: 'horizontal',
      bottom: 10,
      itemGap: 20,
      textStyle: {
        color: '#fff',
      },
      show: true,
      icon: 'rect',
      formatter: function (name) {
        return `${name}`;
      },
    },
    tooltip: {
      formatter: (params) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          const bfb = (
            ((params.seriesIndex !== undefined &&
              option.series[params.seriesIndex] &&
              option.series[params.seriesIndex].pieData &&
              option.series[params.seriesIndex].pieData.endRatio -
                option.series[params.seriesIndex].pieData.startRatio) ||
              0) * 100
          ).toFixed(2);
          return `${params.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>${bfb}%`;
        }
      },
    },
    xAxis3D: {
      min: -1.3,
      max: 1.3,
    },
    yAxis3D: {
      min: -1.3,
      max: 1.3,
    },
    zAxis3D: {
      min: -1.3,
      max: 1.3,
    },
    grid3D: {
      show: false,
      boxHeight: boxHeight,
      left: 0,
      top: '-5%',
      center: ['50%', '45%'],
      viewControl: {
        alpha: 25,
        beta: 40,
        distance: 200,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        panSensitivity: 0,
        autoRotate: false,
      },
    },
    series: series,
  };
  return option;
} 