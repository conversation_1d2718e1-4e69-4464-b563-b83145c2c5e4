import React, { Component } from 'react';
import { observer } from 'mobx-react';
import { Form } from 'choerodon-ui/pro';
import { Collapse, Tabs } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import intl from 'utils/intl';
import RCBMap from 'alm/components/RCBMap';
import { DefaultMaintainer } from 'alm/components/DefaultMaintainer';

import AssetHistoryTab from './AssetHistoryTab';
import MalfunctionTab from './MalfunctionTab';
import PlanTab from './PlanTab';
import BomTab from './BomTab';
import MeterPointTab from './MeterPointTab';

const prompt = 'aatn.equipmentAsset';
@observer
class InfoExhibit extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tabAK: 'basic',
    };
  }

  componentDidUpdate(preProps) {
    // 切换设备id：tab页签定位到基础
    if (preProps?.assetId && preProps?.assetId !== this?.props?.assetId) {
      this.changeTabs('basic');
    }
  }

  /**
   * 获取事件或者字段的列表
   * @param {string} key - 事件or字段
   */
  @Bind()
  changeTabs(key) {
    this.setState({
      tabAK: key,
    });
  }

  @Bind()
  getGeoMapProps() {
    const { assetId, tenantId, baseDetailDS, isNew, editFlag } = this.props;
    const headerData = baseDetailDS.current.toData();
    let geoMapProps = {};
    if (headerData.mapSource === 'MAP') {
      // 资产地图
      geoMapProps = {
        key: assetId,
        moduleName: 'Asset',
        moduleId: assetId,
        tenantId,
        draggingFlag: !!(isNew || editFlag),
        doubleClickZoom: !!(isNew || editFlag),
      };
    } else if (headerData.mapSource === 'LOCATION') {
      // 位置地图
      geoMapProps = {
        key: headerData.assetLocationId,
        moduleName: 'Location',
        moduleId: headerData.assetLocationId,
        hideEditMarkerBtn: true,
        tenantId,
        draggingFlag: false, // 位置地图时禁止拖拽
        doubleClickZoom: false, // 位置地图时禁止双击缩放
      };
    }
    return geoMapProps;
  }

  // 资产状态
  @Bind()
  handleChangeAssetStatus(record, names) {
    const { baseDetailDS } = this.props;
    baseDetailDS.current.set('multiStatusDTO', record);
    baseDetailDS.current.set('statusName', names);
  }

  render() {
    const {
      tenantId,
      dispatch,
      isNew,
      history,
      editFlag,
      baseDetailDS, // 设备/资产基本数据DS
      assetId, // 当前设备/资产ID
      // defaultGisType, // 默认地图类型
      malTreeList, // 故障评估树形结构数据
      loading,
      transactionTypes,
      acceptanceTypes,
      treeNodesProps,
      onEditPlanOrActLine, // 计划与标准表格编辑操作
      onMetersDetail,
      onSearchMal,
      onSearchTransactionTypes,
      onSearchAcceptanceTypes,
      formRender,
      collapseKeys,
    } = this.props;
    const headerData = baseDetailDS && baseDetailDS.current ? baseDetailDS.current.toData() : {}; // 头部数据 查看界面使用

    const { tabAK } = this.state;
    const assetHistoryTabProps = {
      tenantId,
      dispatch,
      assetId,
      transactionTypes,
      acceptanceTypes,
      onSearchTransactionTypes,
      onSearchAcceptanceTypes,
      eventLoading: loading.event,
      fieldLoading: loading.field,
      acceptanceLoading: loading.acceptance,
      woLoading: loading.wo,
      srLoading: loading.sr,
    };
    const planProps = {
      history,
      assetId,
      onEditPlanOrActLine,
    };
    const bomTabProps = {
      ...treeNodesProps,
      headerData,
    };
    // 仪表点
    const meterPointTabProps = {
      tenantId,
      assetId,
      onMetersDetail,
    };

    const malfunctionTabProps = {
      dispatch,
      assetId,
      tenantId,
      malTreeList,
      onSearchMal,
      malLoading: loading.malLoading,
      faultWoListLoading: loading.faultWoListLoading,
    };

    const defaultMaintainerProps = {
      editFlag,
      moduleId: assetId,
      moduleName: 'ASSET',
    };

    return (
      <React.Fragment>
        {/* tab 面板 */}
        <Tabs defaultActiveKey="basic" activeKey={tabAK} onChange={this.changeTabs}>
          <Tabs.TabPane key="basic" tab={intl.get(`alm.common.view.tab.basicTab`).d('基本')}>
            {formRender.length > 0 && (
              <Collapse bordered={false} defaultActiveKey={collapseKeys}>
                {formRender.map(area => (
                  <Collapse.Panel header={area.areaConfName} key={`collapse_${area.areaConfId}`}>
                    <Form dataSet={baseDetailDS} labelWidth={130} columns={3}>
                      {isNew || editFlag
                        ? area.formDisplayRender.map(comp => comp)
                        : area.outputDisplayRender.map(comp => comp)}
                    </Form>
                    {/* 百度地图的显示：地图来源在哪个区域 就显示在那个区域的表单下方 */}
                    {area.fieldCodeArr.includes('mapSource') &&
                    ['MAP', 'LOCATION'].includes(headerData.mapSource) ? (
                      <div style={{ height: 140, overflow: 'auto', marginTop: 10 }}>
                        {/* 默认只使用百度地图 */}
                        <RCBMap {...this.getGeoMapProps()} />
                      </div>
                    ) : (
                      ''
                    )}
                  </Collapse.Panel>
                ))}
              </Collapse>
            )}
          </Tabs.TabPane>
          {isNew || editFlag ? null : (
            <Tabs.TabPane key="record" tab={intl.get(`${prompt}.tab.assetHistory`).d('资产履历')}>
              <AssetHistoryTab {...assetHistoryTabProps} />
            </Tabs.TabPane>
          )}
          {isNew || editFlag ? null : (
            <Tabs.TabPane
              key="malfunction"
              tab={intl.get(`${prompt}.tab.failureAnalysis`).d('故障分析')}
            >
              <MalfunctionTab {...malfunctionTabProps} />
            </Tabs.TabPane>
          )}
          {isNew || editFlag ? null : (
            <Tabs.TabPane key="bom" tab={intl.get(`${prompt}.model.equipmentAsset.bom`).d('BOM')}>
              <BomTab {...bomTabProps} />
            </Tabs.TabPane>
          )}
          {isNew || editFlag ? null : (
            <Tabs.TabPane
              key="plan"
              tab={intl.get(`${prompt}.tab.planAndStandards`).d('计划与标准')}
            >
              <PlanTab {...planProps} />
            </Tabs.TabPane>
          )}
          {isNew || editFlag ? null : (
            <Tabs.TabPane key="meters" tab={intl.get(`${prompt}.tab.meterPoint`).d('仪表点')}>
              <MeterPointTab {...meterPointTabProps} />
            </Tabs.TabPane>
          )}
          {isNew ? null : (
            <Tabs.TabPane
              tab={intl.get(`alm.component.tab.defaultMaintainer`).d('默认职务人员')}
              key="defaultMaintainer"
            >
              <DefaultMaintainer {...defaultMaintainerProps} />
            </Tabs.TabPane>
          )}
        </Tabs>
      </React.Fragment>
    );
  }
}
export default InfoExhibit;
