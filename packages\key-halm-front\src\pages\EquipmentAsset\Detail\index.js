/**
 * Detail - 设备资产明细
 * @date: 2020-10-09
 * @author: DCY <<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { Component } from 'react';
import { observer } from 'mobx-react';
import { dateRender } from 'utils/renderer';
import { DataSet, Button, Modal } from 'choerodon-ui/pro';
import { Skeleton, Tooltip, Spin, Tag } from 'choerodon-ui';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import queryString from 'querystring';
import { Bind } from 'lodash-decorators';
import { isUndefined, isEmpty, endsWith, isString, omit } from 'lodash';
import classNames from 'classnames';
import { Header } from 'components/Page';
import FileUpload from 'alm/components/FileUpload';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';

import { setDSFields, setMultiFieldsValue } from 'alm/utils/dynamicFieldRender';
import { noPromptRequired } from 'alm/utils/response';
import ChangeLogsModal from 'alm/components/ChangeLogsModal';
import { useImgUpload } from 'alm/hooks/useImgUpload';
import CloseImg from 'alm/assets/img/close.png';
import InfoExhibit from './InfoExhibit';
import FullTextSearch from './FullTextSearch/index';
import styles from './index.less';
import { baseDetailDS } from '../Stores/DetailDS';

import { getAssetDetail, getCusPage, saveAsset } from '../api';

const prompt = 'aatn.equipmentAsset';
const modelPrompt = 'aatn.equipmentAsset.model.equipmentAsset';
const bucketName = 'halm-asset-pic';

@connect(({ equipmentAsset, loading }) => ({
  equipmentAsset,
  loading: {
    treeLoading: loading.effects['equipmentAsset/getNodeChild'],
    malLoading: loading.effects['equipmentAsset/fetchMalTreeList'],
  },
  tenantId: getCurrentOrganizationId(),
}))
@formatterCollections({
  code: ['alm.common', 'alm.component', 'aatn.equipmentAsset', 'hzero.c7nUI'],
})
@observer
class Detail extends Component {
  constructor(props) {
    super(props);
    const {
      location: { state },
      match: {
        params: { assetId },
      },
    } = props;

    this.leftRef = React.createRef();
    // 左侧和右侧间隙
    this.leftAndRightMiddleRef = React.createRef();

    this.baseDetailDS = new DataSet(baseDetailDS());

    this.state = {
      saveDeatilLoading: false,
      queryDetailLoading: false,
      showSearchFlag: false,
      isNew: isUndefined(assetId),
      editFlag: state && state.isEdit ? state.isEdit : false,
      defaultGisType: 'BaiduMap',
      isShow: false,
      attributeFiles: [],
      fileUploadLogList: [],
      formRender: {
        editRenders: null, // 编辑/新建
        viewRenders: null, // 查看
      },
      collapseKeys: [], // 基本tab的折叠面板key
    };
  }

  componentWillMount() {
    this.handleSearchFreework();
  }

  componentDidMount() {
    this.init();
  }

  componentDidUpdate(preProps) {
    this.refreshEditState(preProps);
    // 点击左侧列表导航跳转详情重新获取界面详情数据
    if (preProps.match.params.assetId !== this.props.match.params.assetId) {
      this.init();
    }
  }

  /**
   * 更新编辑状态
   */
  @Bind()
  refreshEditState(preProps) {
    const isEdit = preProps?.location?.state?.isEdit ?? undefined;
    const newIsEdit = this?.props?.location?.state?.isEdit ?? undefined;
    if (isEdit !== newIsEdit) {
      this.setState({ editFlag: newIsEdit });
    }
  }

  @Bind()
  async init() {
    const { match, location } = this.props;
    const { search } = location;
    let { originData = {} } = queryString.parse(search.substring(1));
    if (!isEmpty(originData)) {
      originData = JSON.parse(originData);
    }
    const { assetId } = match.params;
    if (!isUndefined(assetId)) {
      this.initWhenNotCreate(assetId);
    } else if (!isUndefined(originData?.copiedAssetId)) {
      this.initWhenCreateByCopy(originData.copiedAssetId);
    } else {
      // 设备资产新建时，路由分为1.带资产分类等信息创建；2.不带任何参数新建 [经搜索发现 第二种情况是工作台那边 凡哥说要去了 不考虑这种情况 2023.4.14]
      // 处理动态字段组织有可能是平台组织/相对方其中一个类型  需要判断一下
      originData = this.handleDynamicOrg(originData);
      this.initWhenCreate(originData);
    }
  }

  /**
   * 处理盘盈或其他来源的字段xxxOrgId是 'xx@PLATFORM'类型
   * @param {*} data originData
   * @returns new originData
   */
  handleDynamicOrg(data) {
    const newData = {};
    Object.keys(data).forEach(key => {
      if (endsWith(key, 'OrgId') && isString(data[key])) {
        const org = key.split('OrgId')[0];
        const id = data[key].split('@')[0];
        const type = data[key].split('@')[1];
        newData[key] = id;
        newData[`${org}OrgType`] = type;
      } else {
        newData[key] = data[key];
      }
    });
    return newData;
  }

  /**
   * 复制创建，直接查询要复制的资产信息塞到表单里
   */
  @Bind
  async initWhenCreateByCopy(assetId) {
    this.setState({ queryDetailLoading: true });
    getAssetDetail(assetId)
      .then(async assetDetail => {
        if (assetDetail && !assetDetail.failed) {
          const pageConfig = await getCusPage(assetDetail.assetSetNum, assetDetail.assetNum);
          if (pageConfig && !pageConfig.failed) {
            const newData = omit(assetDetail, ['assetNum', 'assetId', 'visualLabel']);
            const fieldsValue = this.handleFormRender(pageConfig, false);
            this.baseDetailDS.data = [{ ...newData, ...fieldsValue }];
          }
          this.setState({ queryDetailLoading: false });
        }
      })
      .catch(err => {
        this.setState({ queryDetailLoading: false });
        if (noPromptRequired.includes(err?.code)) {
          this.handleFormRender([], false);
          this.baseDetailDS.data = [];
        }
      });
  }

  @Bind()
  async initWhenCreate(originData) {
    const pageConfig = await getCusPage(originData.assetSet.assetSetNum);
    if (pageConfig && !pageConfig.failed) {
      // 处理位置、资产分类的显示（新建弹窗中选了这两个字段 新建页面需复现）: 放进动态字段的默认值字段中作默认值处理
      pageConfig.forEach(area => {
        (area.attrFields || []).forEach(field => {
          if (field.fieldCode === 'assetSetId' && originData.assetSet) {
            field.defaultValue = originData.assetSet[field.valueField]; // eslint-disable-line
            field.defaultValueMeaning = originData.assetSet[field.displayField]; // eslint-disable-line
          } else if (field.fieldCode === 'assetLocationId' && originData.assetLocationLov) {
            field.defaultValue = originData.assetLocationLov[field.valueField]; // eslint-disable-line
            field.defaultValueMeaning = originData.assetLocationLov[field.displayField]; // eslint-disable-line
          }
        });
      });
      const fieldsValue = this.handleFormRender(pageConfig, true);
      this.baseDetailDS.data = [
        { ...fieldsValue, ...originData, codeRule: originData?.assetSet?.codeRule },
      ];

      // 处理资产图片
      this.handleFile();
    }
  }

  // 处理基础tab下的表单渲染
  @Bind()
  handleFormRender(pageConfig, createFlag) {
    let allDFields = []; // 所有动态字段的信息
    pageConfig.forEach(i => {
      allDFields = [...allDFields, ...(i.attrFields || [])];
    });
    // 获取dsFields 初始化ds
    const { dsFields } = setDSFields(
      allDFields,
      undefined,
      'attrFieldName',
      undefined,
      undefined,
      'equipmentAsset',
      undefined,
      undefined,
      undefined,
      {},
      { skipDiaplay: true, skipResult: true }
    );
    dsFields.push({
      name: 'codeRule',
      type: 'string',
      bind: 'assetSetIdLov.codeRule',
    });
    this.baseDetailDS = new DataSet(baseDetailDS(dsFields));
    this.baseDetailDS.allFields = allDFields.map(i => i.fieldCode);
    this.baseDetailDS.pageConfig = pageConfig;
    this.baseDetailDS.gotoSourceDetail = this.handleGotoSourceDetail; // 实际上新建不需要 但有也不影响
    // 获取表单渲染 与 结果（新建时需要）
    const formRender = [];
    const fieldsValue = [];
    pageConfig.forEach(i => {
      const attrFields = i.attrFields || [];
      const { formDisplayRender, outputDisplayRender, resultData } = setDSFields(
        attrFields,
        undefined,
        'attrFieldName',
        undefined,
        createFlag ? 'defaultValue' : 'value',
        'equipmentAsset',
        undefined,
        undefined,
        undefined,
        {},
        { onlyCuxFieldsResult: !createFlag }, // 新建可能有默认值； 而非新建时 自定义字段的值也在该接口 通过onlyCuxFieldsResult只生成动态字段的值
        this.baseDetailDS
      );
      fieldsValue.push(resultData);
      formRender.push({
        areaConfName: i.areaConfName,
        areaConfId: i.areaConfId,
        fieldCodeArr: attrFields.map(attr => attr.fieldCode),
        formDisplayRender,
        outputDisplayRender,
      });
    });
    this.setState({
      formRender,
      collapseKeys: formRender.map(i => `collapse_${i.areaConfId}`),
    });
    return fieldsValue.reduce((acc, curr) => ({ ...acc, ...curr }), {});
  }

  // 非新建时的初始化
  @Bind()
  async initWhenNotCreate(assetId) {
    this.setState({ queryDetailLoading: true });
    getAssetDetail(assetId)
      .then(async assetDetail => {
        if (assetDetail && !assetDetail.failed) {
          const pageConfig = await getCusPage(assetDetail.assetSetNum, assetDetail.assetNum);
          if (pageConfig && !pageConfig.failed) {
            const fieldsValue = this.handleFormRender(pageConfig, false); // 自定义字段的值会在这儿

            // 考虑到权限问题 系统字段并没有像之前的附加字段（自定义字段）一样 在字段的value valueMeaning中获取值 而是直接通过明细接口拿
            // 不过自定义字段还是从page-cus的value valueMeaning中获取（因为涉及到翻译meaning等问题 后端说加到明细接口中会比较麻烦）
            this.baseDetailDS.data = [{ ...assetDetail, ...fieldsValue }];

            // 将左侧搜索栏选中数据设为当前数据
            if (this.leftRef && this.leftRef.current) {
              this.leftRef.current.handleSetClickedData(assetDetail);
            }
            // 获取属性行(资产-->资产分类-->属性组-->属性行)
            const { fileUploadLogList } = this.state;
            const file = [
              {
                moduleName: 'aafm-asset-set',
                moduleIdList: [assetDetail.assetSetId],
              },
            ];
            const moduleArr = fileUploadLogList.concat(file);
            this.setState({
              fileUploadLogList: moduleArr,
            });

            // 处理资产图片
            this.handleFile();
          }
          this.setState({ queryDetailLoading: false });
        }
      })
      .catch(err => {
        this.setState({ queryDetailLoading: false });
        if (noPromptRequired.includes(err?.code)) {
          // 没权限的话 重置一遍页面 为了避免没有查看权限时 新建/编辑保存后页面仍显示
          this.handleFormRender([], false);
          this.baseDetailDS.data = [];
          this.props.imgUpload.setUUID('clear');
        }
      });
  }

  // 文件处理
  async handleFile() {
    const uuid = this.baseDetailDS.current.get('uniquePictureUuid');
    this.props.imgUpload.setUUID(uuid);
  }

  // 获取默认规则明细页数据
  @Bind()
  handleSearchFreework() {
    const { tenantId, dispatch } = this.props;
    const { isShow } = this.state;
    if (!isUndefined(tenantId)) {
      dispatch({
        type: 'equipmentAsset/fetchDetailInfo',
        payload: {
          tenantId,
        },
      }).then(res => {
        if (res && res.assetManagerRule === 'ASSIGN_ASSET_MANAGER_MANUALLY') {
          this.setState({
            isShow: !isShow,
          });
        }
      });
    }
  }

  /**
   * 跳转到明细页
   */
  @Bind()
  handleGotoDetail(record) {
    this.props.dispatch(
      routerRedux.push({
        pathname: `/aafm/equipment-asset/detail/${record.assetId}`,
      })
    );
  }

  // 查询故障评估项树形结构
  @Bind()
  handleSearchMal() {
    const { dispatch, tenantId, match } = this.props;
    const { assetId } = match.params;
    // 查询故障信息
    dispatch({
      type: 'equipmentAsset/fetchMalTreeList',
      payload: {
        tenantId,
        assetId,
      },
    });
  }

  /**
   * 编辑
   */
  @Bind()
  handleEdit() {
    const { editFlag } = this.state;
    if (editFlag) {
      this.baseDetailDS.reset();
      this.props.imgUpload.reset();
    }
    this.setState({ editFlag: !editFlag });
  }

  /**
   * 数据保存
   */
  @Bind()
  async handleEquipmentAssetSave() {
    const { dispatch, match } = this.props;
    const { assetId } = match.params;
    const { editFlag } = this.state;

    if (await this.baseDetailDS.current.validate(true)) {
      // 先上传图片，拿到删除旧图片的回调函数
      const [newUUID, afterSubmit] = await this.props.imgUpload.startUpload();
      if (newUUID) {
        this.baseDetailDS.current.set('uniquePictureUuid', newUUID);
      } else {
        return;
      }

      this.setState({ saveDeatilLoading: true });
      const detail = this.baseDetailDS.current.toData();
      // 处理所属组织、使用组织 后端返回的xxxOrgId值只是id 而如果是前端选择的 值是id@type
      if (
        detail.usingOrgId &&
        isString(detail.usingOrgId) &&
        detail.usingOrgId.indexOf('@') !== -1
      ) {
        const [id, type] = detail.usingOrgId.split('@');
        detail.usingOrgId = id;
        detail.usingOrgType = type;
      }
      if (
        detail.owningOrgId &&
        isString(detail.owningOrgId) &&
        detail.owningOrgId.indexOf('@') !== -1
      ) {
        const [id, type] = detail.owningOrgId.split('@');
        detail.owningOrgId = id;
        detail.owningOrgType = type;
      }
      // 处理多选类型字段的值
      setMultiFieldsValue(this.baseDetailDS.pageConfig, detail, 'attrFieldName');
      detail.attrField = this.baseDetailDS.pageConfig
        .reduce((acc, cur) => acc.concat(cur.attrFields || []), [])
        .filter(i => i.sourceCode === 'CUSTOM');
      const res = await saveAsset(detail);
      if (res && res.failed) {
        this.setState({ saveDeatilLoading: false });
        afterSubmit(false);
        notification.error({ message: res.message });
      } else if (res && !res.failed) {
        afterSubmit(true);
        if (isUndefined(assetId)) {
          dispatch(
            routerRedux.push({
              pathname: `/aafm/equipment-asset/detail/${res.assetId}`,
            })
          );
        } else {
          this.setState({ editFlag: !editFlag, saveDeatilLoading: false });
          this.init();
        }
      }
    } else {
      notification.error({
        message: intl.get('alm.common.view.message.validate').d('请检查数据!'),
      });
    }
  }

  /**
   * 获取多选框中需要显示的事件类型短名称
   */
  @Bind()
  handleSearchTransactionTypes() {
    const { tenantId, dispatch } = this.props;
    dispatch({
      type: 'equipmentAsset/fetchTransactionTypesList',
      payload: {
        tenantId,
        enabledFlag: 1,
      },
    });
  }

  /**
   * 获取多选框中需要显示的验收单事件类型短名称
   */
  @Bind()
  handleSearchAcceptanceTypes() {
    const { tenantId, dispatch } = this.props;
    dispatch({
      type: 'equipmentAsset/fetchAcceptanceTypesList',
      payload: {
        tenantId,
        enabledFlag: 1,
      },
    });
  }

  // 计划与标准Tab页表格编辑操作跳转详情界面
  @Bind()
  handleEditLine(record, flag) {
    const { dispatch } = this.props;
    if (flag === 'item') {
      dispatch(
        routerRedux.push({
          pathname: `/amtc/pms/msi/detail/${record.maintainItemId}/edit`,
        })
      );
    } else {
      dispatch(
        routerRedux.push({
          pathname: `/amtc/act/detail/${record.actId}`,
          state: {
            isEdit: true,
          },
        })
      );
    }
  }

  /**
   * 关联仪表页面跳转
   * @param {string} id - 设备资产id
   */
  @Bind()
  handleGotoMetersDetail(id) {
    const { dispatch } = this.props;
    dispatch(
      routerRedux.push({
        pathname: `/amtr/meters/detail/${id}`,
      })
    );
  }

  /**
   * 申请转固
   */
  @Bind()
  handleFixedAsset() {
    const { dispatch } = this.props;
    dispatch(
      routerRedux.push({
        pathname: `/afam/fixed-assets/create`,
        search: queryString.stringify({ key: 'asset', ...this.baseDetailDS.current.toData() }),
      })
    );
  }

  /**
   * 搜索区域隐藏显示
   */
  @Bind()
  setShowSearchFlag() {
    const { showSearchFlag } = this.state;
    const reShowSearchFlag = !showSearchFlag;
    this.setState({ showSearchFlag: reShowSearchFlag }, () => {
      if (this.leftRef && this.leftRef.current && reShowSearchFlag) {
        this.leftRef.current.handleSetHeight();
      }
    });
  }

  /**
   * 当内容区滚动时
   */
  @Bind
  handleScroll(e) {
    const top = e.target.scrollTop;
    const { current: leftAndRightMiddleDiv } = this.leftAndRightMiddleRef;

    if (leftAndRightMiddleDiv) {
      leftAndRightMiddleDiv.style.top = `${top}px`;
    }
  }

  @Bind
  handleChildScroll(e) {
    e.stopPropagation();
  }

  @Bind()
  handleGotoSourceDetail(type) {
    const { dispatch } = this.props;
    const headerData =
      this.baseDetailDS && this.baseDetailDS.current ? this.baseDetailDS.current.toData() : {}; // 头部数据 查看界面使用
    const { aosReceivingReportId, sourcePoNum, poHeaderId, sourceProjectId } = headerData;
    let linkUrl = '';
    switch (type) {
      case 'Acceptance':
        linkUrl = `/arcv/acceptance/detail/${aosReceivingReportId}`;
        dispatch(
          routerRedux.push({
            pathname: linkUrl,
          })
        );
        break;
      case 'PurchaseOrder':
        linkUrl = `/aori/purchase-order/detail/${poHeaderId}/${sourcePoNum}`;
        dispatch(
          routerRedux.push({
            pathname: linkUrl,
          })
        );
        break;
      case 'Project':
        linkUrl = `/appm/pro-basic-info/detail/${sourceProjectId}`;
        dispatch(
          routerRedux.push({
            pathname: linkUrl,
            query: {
              editFlag: false,
            },
          })
        );
        break;
      default:
        break;
    }
  }

  // 查看变更记录
  @Bind()
  handleShowChangeLogs() {
    const headerData =
      this.baseDetailDS && this.baseDetailDS.current ? this.baseDetailDS.current.toData() : {}; // 头部数据 查看界面使用
    const { assetNum } = headerData;
    const modalProps = { assetName: 'Asset', assetNum, onToDetail: this.handleLinkToDetail };
    Modal.open({
      key: 'changeLogs',
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      drawer: true,
      style: {
        width: 600,
      },
      title: this.renderLogModalTitle(),
      footer: null,
      children: <ChangeLogsModal {...modalProps} />,
    });
  }

  @Bind()
  renderLogModalTitle() {
    return (
      <React.Fragment>
        <div className={styles['change-logs-modal-title']} onClick={this.handleCloseModal}>
          <span className={styles['title-desc']}>
            {intl.get(`${prompt}.button.changeLogs`).d('变更记录')}
          </span>
          <a>
            <img src={CloseImg} alt="" className={styles['title-img']} />
          </a>
        </div>
        <div className={styles['detail-record']} onClick={this.handleDetailLogs}>
          <a>{intl.get(`${prompt}.modal.detailRecord`).d('详细记录')}</a>
        </div>
      </React.Fragment>
    );
  }

  /**
   * 跳转到指定单据详情页
   */
  @Bind()
  handleLinkToDetail(url) {
    this.props.history.push({
      pathname: url,
    });
    this.handleCloseModal();
  }

  @Bind()
  handleCloseModal() {
    Modal.destroyAll();
  }

  @Bind()
  handleDetailLogs() {
    const headerData =
      this.baseDetailDS && this.baseDetailDS.current ? this.baseDetailDS.current.toData() : {};
    const { assetNum } = headerData;
    this.props.history.push({ pathname: '/aafm/change-logs', state: { assetNum } });
    this.handleCloseModal();
  }

  render() {
    const {
      saveDeatilLoading,
      editFlag,
      showSearchFlag,
      defaultGisType,
      attributeFiles,
      fileUploadLogList,
      isShow,
      formRender,
      collapseKeys,
      isNew,
      queryDetailLoading,
    } = this.state;
    const headerData =
      !isNew && this.baseDetailDS && this.baseDetailDS.current
        ? this.baseDetailDS.current.toData()
        : {}; // 头部数据 查看界面使用
    const {
      assetDesc,
      assetNum,
      assetLocationName,
      assetSetName,
      maintSiteName,
      startDate,
      fixedAssetFlag,
      assetStatusList,
    } = headerData;

    const headerFields = [
      {
        label: intl.get(`${modelPrompt}.assetLocation`).d('资产位置'),
        value: assetLocationName,
      },
      {
        label: intl.get(`${modelPrompt}.assetClass`).d('资产分类'),
        value: assetSetName,
      },
      {
        label: intl.get(`${modelPrompt}.startDate`).d('启用日期'),
        value: dateRender(startDate),
      },
      {
        label: intl.get(`${modelPrompt}.maintSite`).d('服务区域'),
        value: maintSiteName,
      },
    ];

    // 左侧搜索栏参数
    const fullTextSearchProps = {
      isNew,
      showSearchFlag,
      onSetShowSearchFlag: this.setShowSearchFlag,
      onGotoDetail: this.handleGotoDetail,
    };
    const {
      loading,
      tenantId,
      dispatch,
      history,
      equipmentAsset,
      imgUpload,
      match: { params },
    } = this.props;
    const { assetId } = params;
    const {
      transactionTypes = [],
      acceptanceTypes = [],
      viewType,
      treeData,
      loadedKeys,
      expandedKeys,
      malTreeList,
    } = equipmentAsset;
    const treeNodesProps = {
      tenantId,
      treeData,
      dispatch,
      viewType,
      nodeType: 'asset',
      loadedKeys,
      expandedKeys,
      nodeId: assetId,
      loading: loading.treeLoading,
    };
    const infoProps = {
      tenantId,
      editFlag,
      dispatch,
      history,
      loading,
      bucketName,
      baseDetailDS: this.baseDetailDS,
      isNew,
      defaultGisType,
      isShow,
      malTreeList,
      assetId,
      transactionTypes,
      acceptanceTypes,
      treeNodesProps,
      formRender,
      collapseKeys,
      onEditPlanOrActLine: this.handleEditLine,
      onSearchTransactionTypes: this.handleSearchTransactionTypes,
      onSearchAcceptanceTypes: this.handleSearchAcceptanceTypes,
      onSearchMal: this.handleSearchMal,
      onMetersDetail: this.handleGotoMetersDetail,
    };

    // 保存按钮: 默认权限类型为all且新建或者编辑状态下显示
    const displayFlagBtn = isNew || editFlag ? { display: 'block' } : { display: 'none' };
    // 编辑按钮: 默认权限类型为all且非新建或者非编辑状态下显示
    const displayEditFlag = isNew || editFlag ? { display: 'none' } : { display: 'block' };
    // 取消编辑按钮
    const displayCloseBtn = isNew || !editFlag ? { display: 'none' } : { display: 'block' };
    // 申请转固按钮: 非编辑状态下且未转固显示
    const displayFAFlag =
      isNew || editFlag || !isUndefined(fixedAssetFlag)
        ? { display: 'none' }
        : { display: 'block' };
    // 附件管理
    const fileUploadAttribute = assetId
      ? {
          title: [
            {
              name: intl.get(`${prompt}.model.equipmentAsset.sourceType`).d('来源类型'),
              code: 'sourceType',
              width: 150,
              index: 1, // 列插入的位置
            },
            {
              name: intl.get(`${prompt}.model.equipmentAsset.sourceNumber`).d('来源单据号/名称'),
              code: 'sourceNumber',
              width: 150,
              index: 2, // 列插入的位置
              isUrl: true,
            },
          ],
          data: [
            {
              name: intl.get(`${prompt}.model.equipmentAsset.sourceType`).d('来源类型'),
              code: 'sourceType', // code必须与title中code保持一致
              value: intl
                .get(`${prompt}.model.equipmentAsset.equipAssociatedAccessories`)
                .d('设备关联附件'),
            },
            {
              name: intl.get(`${prompt}.model.equipmentAsset.sourceNumber`).d('来源单据号/名称'),
              code: 'sourceNumber',
              value: assetNum,
              pathname: `/aafm/equipment-asset/detail/${assetId}`,
            },
          ],
        }
      : {};
    const changeLogsProps = {
      dispatch,
      moduleName: 'Asset',
      moduleNum: this.baseDetailDS.current?.get('assetNum'),
    };

    return (
      <React.Fragment>
        {/* 整体 */}
        <div className={classNames(styles.container, styles['equipment-asset-detail'])}>
          {/* 头部 */}
          <div>
            <Header
              title={intl.get(`${prompt}.title.equipmentAsset`).d('设备/资产')}
              backPath="/aafm/equipment-asset/list"
            >
              <Button
                icon="save"
                color="primary"
                style={displayFlagBtn}
                loading={saveDeatilLoading}
                onClick={this.handleEquipmentAssetSave}
              >
                {intl.get('hzero.common.button.save').d('保存')}
              </Button>
              <Button icon="edit" color="primary" style={displayEditFlag} onClick={this.handleEdit}>
                {intl.get('hzero.common.button.edit').d('编辑')}
              </Button>
              <Button icon="close" style={displayCloseBtn} onClick={this.handleEdit}>
                {intl.get('hzero.common.button.back').d('返回')}
              </Button>
              <FileUpload
                moduleName="aafm-equipment-asset"
                moduleId={assetId}
                attributeFiles={attributeFiles}
                attribute={fileUploadAttribute}
                fileUploadLogList={fileUploadLogList}
                dispatch={this.props.dispatch} // 用于做页面跳转
                style={editFlag ? { display: 'none' } : { display: 'block' }}
              />
              <Button icon="calculator" style={displayFAFlag} onClick={this.handleFixedAsset}>
                {intl.get(`${prompt}.button.applyToFixedAssets`).d('申请转固')}
              </Button>
              {!isNew && <ChangeLogsModal {...changeLogsProps} />}
            </Header>

            {/* </Header> */}
          </div>

          {/* 内容上方留白 */}
          <div className={styles['content-margin-top']} />

          {/* 内容 */}
          <div className={styles.content} onScroll={this.handleScroll}>
            {/* 左侧 新建不调用左侧列表查询接口 */}
            {isNew || editFlag ? null : (
              <div
                className={showSearchFlag ? styles['left-is-show'] : styles['left-is-hidden']}
                onScroll={this.handleChildScroll}
              >
                <FullTextSearch ref={this.leftRef} {...fullTextSearchProps} />
              </div>
            )}

            {/* 左侧和右侧之间的间隙 */}
            <div
              className={
                isNew || editFlag
                  ? styles['new-left-and-right-middle-none']
                  : classNames(
                      styles['left-and-right-middle-width'],
                      showSearchFlag
                        ? styles['nav-list-show-margin-left']
                        : styles['nav-list-hidden-margin-left']
                    )
              }
            >
              <div ref={this.leftAndRightMiddleRef} />
            </div>

            {/* 右侧 */}
            <div
              className={
                isNew || editFlag
                  ? styles['new-right-all-width']
                  : showSearchFlag
                  ? styles['right-layout-when-left-show']
                  : styles['right-layout-when-left-hidden']
              }
            >
              <Spin spinning={queryDetailLoading}>
                {!isNew && (
                  <React.Fragment>
                    {/* 右侧上 */}
                    <div className={styles['right-top']}>
                      <Skeleton loading={queryDetailLoading}>
                        {/* 图片 */}
                        <div className={styles['right-top-img']}>
                          {editFlag ? imgUpload.cuxEditRenderer : imgUpload.viewRenderer}
                        </div>
                        <div className={styles['right-top-text']}>
                          <div className={styles['title-line']}>
                            <Tooltip title={assetDesc}>{assetDesc}</Tooltip>
                            {(assetStatusList || []).map(i => {
                              return (
                                <Tooltip title={i.assetStatusName}>
                                  <Tag
                                    style={{
                                      color: i.fontColor || '#000',
                                      border: 0,
                                      marginLeft: 8,
                                      marginRight: 0,
                                    }}
                                    color={i.backgroundColor || '#fff'}
                                  >
                                    {i.assetStatusName}
                                  </Tag>
                                </Tooltip>
                              );
                            })}
                          </div>
                          <div className={styles['num-line']}> {assetNum}</div>
                          <div className={styles.divider} />
                          <div className={styles['others-line']}>
                            {headerFields.map(field => (
                              <div className={styles.field}>
                                <span className={styles['field-label']}>{field.label}</span>
                                <span className={styles['field-value']}>：{field.value}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </Skeleton>
                    </div>
                    {/* 右侧中 */}
                    <div className={styles['right-top-margin-bottom']} />
                  </React.Fragment>
                )}
                {/* 右侧下 */}
                <div className={styles['right-bot']}>
                  <InfoExhibit {...infoProps} />
                </div>
              </Spin>
            </div>
          </div>
        </div>
      </React.Fragment>
    );
  }
}

const DetailFunction = props => {
  const imgUpload = useImgUpload({ bucketName });
  return <Detail {...props} imgUpload={imgUpload} />;
};
export default DetailFunction;
