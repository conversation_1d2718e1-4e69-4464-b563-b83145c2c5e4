/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2021-11-05 15:03:55
 * @LastEditTime: 2021-11-05 15:04:41
 * @LastEditors: <<EMAIL>>
 */
/**
 * 通用-多语言
 * <AUTHOR>
 * @date 2021-03-01
 * @version: 0.0.1
 * @copyright: Copyright (c) 2021, Hand
 */

import intl from 'utils/intl';

const getCommonLangs = (key?: string) => {
  const LANGS = {
    // hzero.common
    PREVIEW: intl.get('hzero.common.preview').d('预览'),
    OPTION: intl.get('hzero.common.table.column.option').d('操作'),
    CREATE: intl.get('hzero.common.button.create').d('新建'),
    DELETE: intl.get('hzero.common.button.delete').d('删除'),
    REMOVE: intl.get('hzero.common.button.remove').d('移除'),
    SEARCH: intl.get('hzero.common.button.search').d('查询'),
    RESET: intl.get('hzero.common.button.reset').d('重置'),
    CANCEL: intl.get('hzero.common.button.cancel').d('取消'),
    CANCEL_EDIT: intl.get('hzero.common.button.cancelEdit').d('取消编辑'),
    IMPORT: intl.get('hzero.common.button.import').d('导入'),
    EXPORT: intl.get('hzero.common.button.export').d('导出'),
    CLEAR: intl.get('hzero.common.button.clear').d('清空'),
    CLEAN: intl.get('hzero.common.button.clean').d('清除'),
    EDIT: intl.get('hzero.common.button.edit').d('编辑'),
    SAVE: intl.get('hzero.common.button.save').d('保存'),
    SURE: intl.get('hzero.common.button.sure').d('确定'),
    CLOSE: intl.get('hzero.common.button.close').d('关闭'),
    BACK: intl.get('hzero.common.button.back').d('返回'),
    COLLECTED: intl.get('hzero.common.button.collected').d('收起查询'),
    VIEWMORE: intl.get('hzero.common.button.viewMore').d('更多查询'),
    ENABLE: intl.get('hzero.common.button.enable').d('启用'),
    DISABLE: intl.get('hzero.common.button.disable').d('禁用'),
    ADD: intl.get('hzero.common.button.add').d('新增'),
    DETAIL: intl.get('hzero.common.button.detail').d('详情'),
    FINISH: intl.get('hzero.common.button.finish').d('完成'),
    NEXT: intl.get('hzero.common.button.next').d('下一步'),
    ADD_CHILDREN: intl.get('hzero.common.button.addChildren').d('新增下级'),
    EXPAND: intl.get('hzero.common.button.expand').d('展开'),
    COLLAPSE: intl.get('hzero.common.button.up').d('收起'),
    EXPAND_ALL: intl.get('hzero.common.button.expandAll').d('全部展开'),
    COLLAPSE_ALL: intl.get('hzero.common.button.collapseAll').d('全部收起'),
    REFRESH: intl.get('hzero.common.button.refresh').d('刷新'),
    VIEW: intl.get('hzero.common.button.view').d('查看'),
    SUCCESS: intl.get('hzero.common.notification.success').d('操作成功！'),
    FAILED: intl.get('hzero.common.notification.failed').d('操作失败！'),
    DOWNLOAD: intl.get('hzero.common.button.download').d('下载'),
    ATTACHMENT: intl.get('hzero.common.upload.modal.title').d('附件'),
    NO: intl.get('hzero.common.status.no').d('否'),
    YES: intl.get('hzero.common.status.yes').d('是'),
    SUBMIT: intl.get('hzero.common.button.submit').d('提交'),
    REMARK: intl.get('hzero.common.remark').d('备注'),
    // hzero.c7nUI
    ITEMS_PER_PAGE: intl.get('hzero.c7nUI.Pagination.items_per_page').d('条/页'),
    SELECT_ALL: intl.get('hzero.c7nUI.Select.selectAll').d('全选'),
    FILTER_PLACEHOLDER: intl.get('hzero.c7nUI.Select.filterPlaceholder').d('输入文字以进行过滤'),
    EMPTY_TEXT: intl.get(`hzero.c7nUI.Table.emptyText`).d('暂无数据'),
    NO_SELECT: intl.get(`alm.common.view.message.noSelect`).d('请选择一条数据！'),
  }
  return key ? LANGS[key] : LANGS;
};

export default getCommonLangs;
