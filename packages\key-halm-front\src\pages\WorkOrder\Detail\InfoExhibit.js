import React, { Component } from 'react';
import intl from 'utils/intl';
import {
  Form,
  TextField,
  NumberField,
  Output,
  TextArea,
  Lov,
  Select,
  DateTimePicker,
  Switch,
} from 'choerodon-ui/pro';
import { Collapse, Tabs, Icon } from 'choerodon-ui';
import { Bind } from 'lodash-decorators';
import { isUndefined } from 'lodash';
import { yesOrNoRender } from 'utils/renderer';
import OrgPartnerLov from 'alm/components/OrgPartnerLov';
import {
  handleSetValueEmployee,
  setCostObjectByAssetOrLoc,
} from 'alm/components/CommonComponent/utils';
import BMap from 'alm/components/BasicBMap';
import EmployeesLov from 'alm/components/EmployeesLov';
import { getCurrentOrganizationId } from 'utils/utils';
import moment from 'moment';
import { DEFAULT_DATETIME_FORMAT } from 'utils/constants';
import request from 'utils/request';
import { HALM_MTC, HALM_MDM, HALM_ATN, HALM_ORI } from 'alm/utils/config';
import { observer } from 'mobx-react';
import AssetLov from 'alm/components/AssetLov';
import { tableProps as priorityTableProps } from 'alm/components/PriorityLov/tableProps';

import ActLov from './ActLov';
import LocLov from './LocLov';
import WoopList from './WoopList';
import ServiceSettlement from './ServiceSettlement';
import Material from './Material';
import SourceTable from './SourceTable';
import WoMalfunctionList from './WoMalfunction/WoMalfunctionList';
import WoChecklistList from './WoChecklists';
import WoOutsourcing from './WoOutsourcing/WoOutsourcing';
import WoLaborsList from './WoLabors/WoLaborsList';
import WoCostList from './Cost';

import { queryWoButtousService } from '../api';
import styles from './infoExhibitStyle.module.less';

const modelPrompt = 'amtc.workOrder.model.workOrder';
const viewPrompt = 'amtc.workOrder.view.message';
const organizationId = getCurrentOrganizationId();

// 获取标准作业明细数据
const getActDetailUrl = `${HALM_MTC}/v1/${organizationId}/act`;
// 获取工作类型明细数据
const getWorkOrderTypeUrl = `${HALM_MTC}/v1/${organizationId}/workorder-type`;

// 获取位置明细
const queryLocationDetailUrl = `${HALM_MDM}/v1/${organizationId}/asset-locations`;
// 获取设备明细
const queryAssetDetailUrl = `${HALM_ATN}/v1/${organizationId}/asset-info`;

@observer
class Detail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      orgIdDisableFlag: true, // 是否禁用 客户/需求组织
      upgradeRuleTimeField: '', // 工单升级规则code (取自工单类型) - 用于对页面进行控制
      serviceAgreemtHelpVisible: false, // 服务协议提示信息显示
      activeKey: 'basicTab', // 当前激活 tab 面板的 key
    };

    this.malRef = React.createRef(); // 故障ref
    this.materialRef = React.createRef();
    this.costRef = React.createRef();
    this.woopListRef = React.createRef();
    this.checklistRef = React.createRef();
    this.outsourcingRef = React.createRef();
    this.serviceSettleRef = React.createRef();
    this.woLaborsRef = React.createRef();
    this.costObjOfAsset = null; // 存储设备带出的成本对象ID，用于处理设备和位置带出成本对象的优先级
  }

  // 切换tab的key
  // repeatFlag： 不重复调用工单详情按钮接口：任务状态变更时会调用刷新方法，这里会有重复调用的情况
  @Bind()
  async handleTabsChange(key, repeatFlag) {
    const { woId, woNum, onSearchWoopList, onSetBtnCfg } = this.props;
    const { activeKey } = this.state;
    if (woNum && !repeatFlag) {
      // 不能用await中断，影响未结检查项显示
      queryWoButtousService(woNum).then(res => {
        onSetBtnCfg(res);
      });
    }
    this.setState({
      activeKey: key || activeKey,
    });
    setTimeout(() => {
      switch (key || activeKey) {
        case 'taskTab':
          onSearchWoopList();
          break;
        case 'materialTab':
          this.materialRef.current.handleRefresh();
          break;
        case 'malfunctionTab':
          this.malRef.current.handleRefresh();
          break;
        case 'checklistTab':
          this.checklistRef.current.handleSearchWoChecklist();
          this.checklistRef.current.handleSearchWoopChecklist();
          this.checklistRef.current.handleSearchWoAbChecklist();
          this.checklistRef.current.handleSearchUnFinishedChecklist();
          break;
        case 'outsourcingTab':
          this.outsourcingRef.current.handleRefresh();
          break;
        case 'laborsTab':
          this.woLaborsRef.current.handleRefresh();
          break;
        case 'serviceSettleTab':
          this.serviceSettleRef.current.getTablesData();
          this.serviceSettleRef.current.getServiceTypeData();
          break;
        case 'costTab':
          this.costRef.current.getCostDetail(woId);
          break;
        default:
          break;
      }
    }, 0);
  }

  /**
   * 清除 计划员组/计划员 和 负责人组/负责人
   */
  handleResetEmployee() {
    const { basicDataDs, onHandleRecord } = this.props;
    basicDataDs.current.set('plannerGroupId', null);
    basicDataDs.current.set('plannerGroupName', null);
    basicDataDs.current.set('plannerId', null);
    basicDataDs.current.set('plannerName', null);
    basicDataDs.current.set('ownerGroupId', null);
    basicDataDs.current.set('ownerGroupName', null);
    basicDataDs.current.set('ownerId', null);
    basicDataDs.current.set('ownerName', null);
    onHandleRecord({});
  }

  /**
   * 根据标准作业ID带出更多信息
   */
  @Bind()
  async handleMoreDataWithAct(actId) {
    const { tenantId, basicDataDs, onSetState } = this.props;
    onSetState({ queryDeatilLoading: true });

    request(`${getActDetailUrl}/${actId}`, {
      method: 'GET',
      query: {
        tenantId,
        actId,
      },
    }).then(res => {
      if (res && !res.failed) {
        basicDataDs.current.set('actId', actId);
        basicDataDs.current.set('actName', res.actName);
        this.handleAct(actId, res);
        onSetState({ queryDeatilLoading: false });
      }
    });
  }

  /**
   * 标准作业活动改变，带出其他数据; 清空时 value=undefiend record={}
   * @param {*} value 当前值
   * @param {*} record lov选中的行
   */
  @Bind()
  handleAct(value, record) {
    const { searchItem, basicDataDs, onHandleRecord } = this.props;

    basicDataDs.current.set('actId', value);
    basicDataDs.current.set('actName', record?.actName);
    basicDataDs.current.set('woName', record?.actName);
    basicDataDs.current.set('reportPriorityId', record?.woPriorityId);
    basicDataDs.current.set('reportPriorityName', record?.woPriorityName);
    basicDataDs.current.set('durationUom', record?.durationUomCode);
    basicDataDs.current.set('durationScheduled', record?.actOpStandardHourTotal);
    basicDataDs.current.set('ownerConfirmFlag', record?.ownerConfirmFlag);
    basicDataDs.current.set('waitingWoopownerFlag', record?.waitingOwnerFlag);
    basicDataDs.current.set('description', record?.description);
    basicDataDs.current.set('woBasicTypeOfAct', record?.woBasicType); // 作为工单类型lov查询参数
    basicDataDs.current.set('jobSpecifiedCode', record?.jobSpecifiedCode);

    // 特殊处理的字段
    const isClear = Object.keys(record).length === 0;
    // 清除操作
    if (isClear) {
      // 必须要把组件对应的字段清空掉 才能正确飘红（必输校验红色提醒）
      basicDataDs.current.set('woTypeLov', null);

      // SR带出的服务申请不能修改清空
      if (searchItem?.sourceParamType !== 'SR') {
        basicDataDs.current.set('maintSiteLov', null);
      }

      this.handleResetEmployee();

      this.handleWoTypeChange(); // 清除工单类型字段关联的字段
    } else {
      // 必须给lov赋值 才能避免：先选服务区域 清除后字段飘红 选了标准作业红 仍然飘红问题
      basicDataDs.current.set('woTypeLov', {
        woTypeName: record.woTypeName,
        woTypeId: record.woTypeId,
      });

      // SR带出的服务申请不能修改清空
      if (searchItem?.sourceParamType !== 'SR') {
        basicDataDs.current.set('maintSiteLov', {
          maintSiteName: record.maintSiteName,
          maintSiteId: record.maintSiteId,
        });
      }

      // 负责人、计划员逻辑：
      // 1.如果标准作业上的 工作职责指定方式（jobSpecifiedCode） 是 来源于活动 （FROM_ACT）则取当前标准作业上的数据
      // 2.如果不是 FROM_ACT 则依据当前标准作业的 服务区域 上的配置而定
      if (record.jobSpecifiedCode === 'FROM_ACT') {
        onHandleRecord(record);
      }

      if (record.woTypeId) {
        this.getWoType(record.woTypeId, res => {
          this.handleWoTypeChange({
            ...res,
            defaultPriorityId: record.woPriorityId,
            defaultPriorityName: record.woPriorityName,
          });
        });
      } else {
        this.handleWoTypeChange({
          enableOrgFlag: 0,
          defaultPriorityId: record.woPriorityId,
          defaultPriorityName: record.woPriorityName,
        });
      }
    }
  }

  /**
   * 工单类型改变 - 清空时 lovRecord值为null
   * @param {*} lovRecord lov选中的行
   */
  @Bind()
  async handleWoTypeChange(lovRecord) {
    const { tsData, basicDataDs, onsetItemPropertyByWoType, searchItem } = this.props;
    const detail = basicDataDs.current.toJSONData() || {};
    const { assetId, assetLocationId } = detail;
    // 根据工单类型(工单工作对象控制) 控制字段 必输 禁用， 及 orgFlag 的值
    onsetItemPropertyByWoType(lovRecord || {});

    if (lovRecord) {
      basicDataDs.current.set('billingInfoFlag', lovRecord.billingInfoFlag);
      basicDataDs.current.set('priorityId', lovRecord.defaultPriorityId); // 计划优先级
      basicDataDs.current.set('priorityName', lovRecord.defaultPriorityName);
      basicDataDs.current.set('costObjectLimitType', lovRecord.costObjectLimitType); // 成本对象限制
      basicDataDs.current.set('costObjectType', lovRecord.defaultCostObjectType); // 成本对象类型
      basicDataDs.current.set('woBasicType', lovRecord.woBasicType); // 工单基础类型
      // 工作对象为NOT_ALLOW（需扫码输入）时，清空设备、位置
      if (lovRecord?.workObjCtrlCode === 'NOT_ALLOW') {
        basicDataDs.current.set('assetId', null);
        basicDataDs.current.set('assetName', null);
        basicDataDs.current.set('assetLocationName', null);
        basicDataDs.current.set('assetLocationId', null);
      } else if (searchItem.sourceParamType === 'SR' && !assetLocationId && !assetId) {
        // 工作对象不为NOT_ALLOW（需扫码输入）时，且设备、位置为空时，获取暂存的设备位置
        basicDataDs.current.set('assetId', tsData?.assetId);
        basicDataDs.current.set('assetName', tsData?.assetName);
        basicDataDs.current.set('descAndLabel', tsData?.descAndLabel);
        basicDataDs.current.set('assetLocationId', tsData?.assetLocationId);
        basicDataDs.current.set('assetLocationName', tsData?.assetLocationName);
      }

      if (lovRecord.costObjectLimitType && lovRecord.costObjectLimitType !== 'NOT_DISPLAY') {
        await setCostObjectByAssetOrLoc(basicDataDs.current);
      }

      // 从项目任务创建的工单当工单类型的计划状态不为工单可直接执行，将任务的计划开始时间带到工单的计划开始时间上，否则不带出
      if (searchItem.sourceParamType === 'WBS') {
        basicDataDs.current.set(
          'scheduledStartDate',
          lovRecord.scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY'
            ? searchItem.startDate || null
            : null
        );
      }
      // 选择服务区域后再选择工单类型触发默认职责逻辑
      this.handleQueryDefaultStaff();
    } else {
      // 清空工单类型时 清空相关数据
      basicDataDs.current.set('orgId', null); // 客户/需求组织
      basicDataDs.current.set('orgName', null);
      basicDataDs.current.init('contactLov'); // 需求方联系人
      basicDataDs.current.set('phone', null);
      basicDataDs.current.set('billingInfoFlag', 0);
      basicDataDs.current.set('priorityId', null); // 计划优先级
      basicDataDs.current.set('priorityName', null);
      basicDataDs.current.set('scheduledStartDate', null);
      basicDataDs.current.set('costObjectType', null); // 成本对象类型
      basicDataDs.current.set('costObjectLimitType', null); // 成本对象限制
      basicDataDs.current.set('costObject', null); // 成本对象限制
    }
  }

  /**
   * 成本对象类型变化
   */
  @Bind
  handleChangeCostObjType() {
    const { basicDataDs } = this.props;
    setCostObjectByAssetOrLoc(basicDataDs.current);
  }

  /**
   * 计算并设置日期 - 会被计划开始日期、计划工期、工期单位三个值改变时触发
   * 逻辑如下：
   * 1. 有工期单位及开始时间：
   * （1）如果是新建， 会改变计划完成时间、目标开始日期、目标完成日期，
   * 目标开始日期设置成计划开始时间的值，两完成时间设置成计算出来的完成时间（通过开始时间+计划工期+工期单位计算）
   * （2）如果是编辑，仅设置计划完成时间
   *
   * @param {*} durationUom 工期单位
   * @param {*} currentDate 开始时间
   * @param {*} durationScheduled 计划工期（可能为0）
   */
  @Bind()
  handleSetFinishDate(durationUom, currentDate, durationScheduled) {
    const { basicDataDs, editFlag } = this.props;
    let finishDate;
    if ((durationScheduled || durationScheduled === 0) && durationUom && currentDate) {
      const addType = durationUom === 'DAY' ? 'd' : durationUom === 'HOUR' ? 'h' : 'm';
      if (durationUom !== 'DAY') {
        finishDate = moment(currentDate).add(durationScheduled, addType);
      } else {
        const dayStr = String(durationScheduled);
        if (dayStr && dayStr.includes('.')) {
          const fullDay = Number(dayStr.split('.')[0]); // 完整天数
          const min = Number(`0.${dayStr.split('.')[1]}`); // 小数点后的天数 0.xx天
          finishDate = moment(currentDate)
            .add(fullDay, 'days')
            .add(min * 24 * 60, 'minutes');
        } else {
          finishDate = moment(currentDate).add(durationScheduled, 'days');
        }
      }

      if (editFlag) {
        basicDataDs.current.set('scheduledFinishDate', finishDate); // 计划完成时间
      } else {
        basicDataDs.current.set('scheduledFinishDate', finishDate);
        basicDataDs.current.set('targetStartDate', currentDate); // 目标开始
        basicDataDs.current.set('targetFinishDate', finishDate); // 目标完成
      }
    }
  }

  // 计划工期 字段改变
  @Bind()
  handleDurationScheduledChange(durationScheduled = 0) {
    const { basicDataDs } = this.props;
    const currentDate = basicDataDs.current.get('scheduledStartDate') || ''; // 计划开始时间
    const durationUom = basicDataDs.current.get('durationUom'); // 工期单位
    this.handleSetFinishDate(durationUom, currentDate, durationScheduled);
  }

  // 工期单位 字段改变
  @Bind()
  handleDurationUomChange(durationUom) {
    const { basicDataDs } = this.props;
    const currentDate = basicDataDs.current.get('scheduledStartDate') || ''; // 计划开始时间
    const durationScheduled = basicDataDs.current.get('durationScheduled'); // 计划工期
    this.handleSetFinishDate(durationUom, currentDate, durationScheduled);
  }

  // 计划开始时间 字段改变
  @Bind()
  handleScheduledStartDateChange(date) {
    const { basicDataDs } = this.props;
    const currentDate = date || ''; // 计划开始时间
    const durationUom = basicDataDs.current.get('durationUom'); // 工期单位
    const durationScheduled = basicDataDs.current.get('durationScheduled'); // 计划工期
    this.handleSetFinishDate(durationUom, currentDate, durationScheduled);
  }

  // 计划完成时间 字段改变逻辑：
  // （1）如果是新建，将目标完成时间设置成计划完成时间的值，同时改变 计划工期
  // （2）如果是编辑 只改变计划工期
  @Bind()
  handleScheduledFinishDateChange(val) {
    const { basicDataDs, editFlag } = this.props;
    const start = basicDataDs.current.get('scheduledStartDate');
    const durationUom = basicDataDs.current.get('durationUom');
    let durationScheduled = 0;
    if (val) {
      const d1 = Date.parse(val); // 返回该日期与 1970 年 1 月 1 日午夜之间相差的毫秒数
      const d2 = Date.parse(start);
      const time = Math.abs(d1 - d2);
      switch (durationUom) {
        case 'DAY':
          durationScheduled = (time / (1000 * 3600 * 24)).toFixed(2); // 四舍五入保留两位
          break;
        case 'HOUR':
          durationScheduled = (time / (1000 * 3600)).toFixed(2); // 四舍五入保留两位
          break;
        case 'MINUTE':
          durationScheduled = (time / (1000 * 60)).toFixed(2); // 四舍五入保留两位
          break;
        default:
          break;
      }
    }
    if (editFlag) {
      basicDataDs.current.set('durationScheduled', durationScheduled);
    } else {
      basicDataDs.current.set('targetFinishDate', val);
      basicDataDs.current.set('durationScheduled', durationScheduled);
    }
  }

  // 获取工单类型明细数据
  @Bind()
  getWoType(woTypeId, fu) {
    const { tenantId } = this.props;
    if (!isUndefined(woTypeId)) {
      request(`${getWorkOrderTypeUrl}/${woTypeId}`, {
        method: 'GET',
        query: {
          tenantId,
          woTypeId,
        },
      }).then(res => {
        if (res && !res.failed) {
          fu(res);
        }
      });
    }
  }

  /**
   * 通过接单明确负责人按钮改变 - 打开时，清空负责人字段
   * @param {*} value 当前值
   */
  @Bind()
  onWaitWoopownerChange(value) {
    const { basicDataDs } = this.props;
    if (value) {
      basicDataDs.current.set('ownerId', null);
      basicDataDs.current.set('ownerName', null);
    }
  }

  /**
   * 资产/设备 值改变
   * @param {*} lovRecord lov选中的行
   */
  @Bind()
  onAssetChange(lovRecord) {
    const { basicDataDs } = this.props;
    const nowAssetLocationId = basicDataDs.current.get('assetLocationId');
    const existFlag =
      !isUndefined(nowAssetLocationId) && nowAssetLocationId !== null && nowAssetLocationId !== '';
    if (Object.keys(lovRecord).length > 0) {
      basicDataDs.current.set('assetId', lovRecord.assetId);
      basicDataDs.current.set('assetNum', lovRecord.assetNum);
      basicDataDs.current.set(
        'descAndLabel',
        lovRecord.descAndLabel || `${lovRecord.assetDesc}-${lovRecord.assetNum}`
      );
    } else {
      basicDataDs.current.set('assetId', undefined);
      basicDataDs.current.set('assetNum', undefined);
      basicDataDs.current.set('descAndLabel', undefined);
    }

    // 当前位置没有值时 取当前选中资产的位置
    if (!existFlag) {
      basicDataDs.current.set('assetLocationId', lovRecord.assetLocationId);
      basicDataDs.current.set('assetLocationName', lovRecord.assetLocationName);
      // 如果地图来源有值且为 位置： 设置经纬度的值
      const mapSourceCode = basicDataDs.current.get('mapSourceCode');
      if (mapSourceCode && mapSourceCode === 'LOCATION') {
        this.handleSetLngAndLat('LOCATION');
      }
    }

    // 如果地图来源有值且为 资产： 设置经纬度的值
    const mapSourceCode = basicDataDs.current.get('mapSourceCode');
    if (mapSourceCode && mapSourceCode === 'ASSET') {
      const { lng, lat } = lovRecord || {};
      basicDataDs.current.set('longitude', lng);
      basicDataDs.current.set('latitude', lat);
    }
    // 设备变更后若服务区域发生变更修改工作中心相关信息
    if (lovRecord.assetId && basicDataDs.current.get('maintSiteId') !== lovRecord.maintSiteId) {
      basicDataDs.current.set('maintSiteId', lovRecord.maintSiteId);
      basicDataDs.current.set('maintSiteName', lovRecord.maintSiteName);
    }

    // 设备与成本对象联动
    this.handleChangeCostObjOfLocOrAsset(lovRecord);
    // 资产设备变更时重新查询默认职责
    this.handleQueryDefaultStaff();
  }

  /**
   * 位置改变 清空设备
   */
  @Bind()
  handleLocationChange(lovRecord) {
    const { basicDataDs } = this.props;
    basicDataDs.current.set('assetId', null);
    basicDataDs.current.set('descAndLabel', null);
    basicDataDs.current.set('assetLocationName', lovRecord?.locationName);
    basicDataDs.current.set('assetLocationId', lovRecord?.assetLocationId);

    // 如果地图来源有值且为 位置： 设置经纬度的值
    const mapSourceCode = basicDataDs.current.get('mapSourceCode');
    if (mapSourceCode && mapSourceCode === 'LOCATION') {
      const { lng, lat } = lovRecord || {};
      basicDataDs.current.set('longitude', lng);
      basicDataDs.current.set('latitude', lat);
    }
    if (lovRecord && lovRecord.maintSiteId !== basicDataDs.current.get('maintSiteId')) {
      basicDataDs.current.set('maintSiteId', lovRecord.maintSiteId);
      basicDataDs.current.set('maintSiteName', lovRecord.maintSiteName);
    }
    // 位置与成本对象联动
    this.handleChangeCostObjOfLocOrAsset(lovRecord);
    // 位置变更时重新查询默认职责
    this.handleQueryDefaultStaff();
  }

  @Bind
  handleChangeCostObjOfLocOrAsset(lovRecord) {
    const { basicDataDs } = this.props;
    const costObjectType = basicDataDs.current.get('costObjectType');
    if (costObjectType === 'COST_CENTER') {
      if (!lovRecord || !lovRecord.costCenterId) {
        basicDataDs.current.init('costObjectLov');
        return;
      }
      basicDataDs.current.set('costObjectLov', {
        codeId: lovRecord?.costCenterId,
        name: lovRecord?.costCenterMeaning,
      });
    } else if (costObjectType === 'INTERNAL_ORDER') {
      if (!lovRecord || !lovRecord.internalOrderId) {
        basicDataDs.current.init('costObjectLov');
        return;
      }
      basicDataDs.current.set('costObjectLov', {
        codeId: lovRecord?.internalOrderId,
        name: lovRecord?.internalOrderMeaning,
      });
    } else if (costObjectType === 'WBS_ELEMENT') {
      if (!lovRecord || !lovRecord.wbsElementId) {
        basicDataDs.current.init('costObjectLov');
        return;
      }
      basicDataDs.current.set('costObjectLov', {
        codeId: lovRecord?.wbsElementId,
        name: lovRecord?.wbsElementMeaning,
      });
    }
  }

  /**
   * 服务协议改变
   */
  @Bind()
  handleServiceAgreemtChange(value) {
    if (value) {
      this.setState({ serviceAgreemtHelpVisible: true });
    } else {
      this.setState({ serviceAgreemtHelpVisible: false });
    }
  }

  /**
   * 计划员组改变 设置计划员的值
   * @param {*} value 选中值
   */
  @Bind()
  handleChangePlannerGroup(e) {
    const { basicDataDs, onHandleStaffCallBack } = this.props;

    basicDataDs.current.set('plannerGroupId', e.workCenterId);
    basicDataDs.current.set('plannerGroupName', e.workCenterName);
    basicDataDs.current.set('plannerId', e.employeeId);
    basicDataDs.current.set('plannerName', e.employeeName);
    onHandleStaffCallBack(e, 'plannerGroup');
  }

  /**
   * 计划员组改变 设置计划员的值
   * @param {*} value 选中值
   */
  @Bind()
  handleChangePlanner(e) {
    const { basicDataDs, onHandleStaffCallBack } = this.props;
    if (e) {
      this.handleChangePlannerGroup(e);
    } else {
      // 清空时，只清空自己
      basicDataDs.current.set('plannerId', e.employeeId);
      basicDataDs.current.set('plannerName', e.employeeName);
    }
    onHandleStaffCallBack(e, 'planner');
  }

  /**
   * 负责人组改变 设置负责人的值
   * @param {*} value 选中值
   */
  @Bind()
  handleChangeOwnerGroup(e) {
    const { basicDataDs, onHandleStaffCallBack } = this.props;
    const waitingWoopownerFlag = basicDataDs.current.get('waitingWoopownerFlag');

    basicDataDs.current.set('ownerGroupId', e.workCenterId);
    basicDataDs.current.set('ownerGroupName', e.workCenterName);
    if (waitingWoopownerFlag !== 1) {
      basicDataDs.current.set('ownerId', e.employeeId);
      basicDataDs.current.set('ownerName', e.employeeName);
    }
    onHandleStaffCallBack(e, 'ownerGroup');
  }

  /**
   * 负责人改变
   * @param {*} value 选中值
   */
  @Bind()
  handleChangeOwner(e) {
    const { basicDataDs, onHandleStaffCallBack } = this.props;
    if (e) {
      basicDataDs.current.set('ownerGroupId', e.workCenterId);
      basicDataDs.current.set('ownerGroupName', e.workCenterName);
      basicDataDs.current.set('ownerId', e.employeeId);
      basicDataDs.current.set('ownerName', e.employeeName);
    } else {
      // 清空时，只清空自己
      basicDataDs.current.set('ownerId', e.employeeId);
      basicDataDs.current.set('ownerName', e.employeeName);
    }
    onHandleStaffCallBack(e, 'owner');
  }

  // 服务区域 对计划员、负责人字段进行控制
  @Bind()
  async handleMaintSiteChange(record) {
    const { basicDataDs, sourceDs, isNew, searchItem } = this.props;
    // 清空位置、设备、标准作业、签派员及负责人相关的值， 同时对计划员、负责人进行特殊处理
    basicDataDs.current.set('assetLocationName', null);
    basicDataDs.current.set('assetLocationId', null);
    basicDataDs.current.set('assetId', null);
    basicDataDs.current.set('descAndLabel', null);
    basicDataDs.current.set('actId', null);
    basicDataDs.current.set('actName', null);
    basicDataDs.current.init('costObjectLov'); // 因设备/位置变化，清空成本对象
    // 签派员及负责人相关
    this.handleResetEmployee();

    if (record) {
      // 如果存在工单类型时,选择服务区域才会生效
      this.handleQueryDefaultStaff();
    } else if (isNew && (searchItem.sourceParamType === 'WBS' || !searchItem.sourceParamType)) {
      // 从项目新建工单时或者是 从列表新建工单的时候，清除服务区域清空关联项目信息
      sourceDs.data = [];
      this.handleChangeRelatedPro();
    }
  }

  toCamelCaseVar(variable) {
    return variable.replace(/_+[a-zA-Z]/g, (str, index) =>
      index ? str.substr(-1).toUpperCase() : str
    );
  }

  /**
   * 手工指定联系人值改变
   * @param {*} val
   */
  @Bind()
  handleChangeManually(val) {
    const { basicDataDs } = this.props;
    // 修改 客户/需求组织 状态： 手工指定联系人开启时 客户/需求组织 可编辑
    this.setState({
      orgIdDisableFlag: !val,
    });
    basicDataDs.current.set('orgId', null);
    basicDataDs.current.set('orgName', null);
    basicDataDs.current.init('contactLov');
    basicDataDs.current.set('contactDesc', null);
    basicDataDs.current.set('phone', null);
  }

  /**
   * 需求方联系人值改变时 改变 客户/需求组织 及 联系电话的值
   */
  @Bind()
  handleChangeContact(record) {
    const { basicDataDs } = this.props;
    basicDataDs.current.set('orgId', record?.orgId);
    basicDataDs.current.set('orgName', record?.orgName);
    basicDataDs.current.set('phone', record?.mobile);
  }

  // 客户/需求组织 值改变
  @Bind()
  handleChangeOrg(record, type) {
    const { basicDataDs } = this.props;
    basicDataDs.current.set('orgId', type === 'PLATFORM' ? record?.unitId : record?.orgId);
    basicDataDs.current.set('orgName', type === 'PLATFORM' ? record?.unitName : record?.orgName);
    basicDataDs.current.set('orgType', type);
  }

  @Bind()
  handleChangeReportOrg() {
    const { basicDataDs } = this.props;
    basicDataDs.current.set('reporterLov', null);
  }

  // reporterLov 值改变
  @Bind()
  handleChangeReport(value) {
    const { basicDataDs } = this.props;

    basicDataDs.current.set('reportOrgId', value?.orgId);
    basicDataDs.current.set('reportOrgName', value?.orgName);
  }

  /**
   *  CascadeLov - 传入客制化参数 - 点击工作中心，查询技能组的时候，只查询启用的技能组
   * @param {*} lovCode -值集编码
   * @param {*} queryParams -值集查询参数
   */
  @Bind()
  customDefineParams(lovCode, queryParams) {
    if (lovCode && lovCode === 'AMTC.WC_RES_TREE') {
      return {
        ...queryParams,
        plannerFlag: 1,
      };
    }
    return queryParams;
  }

  /**
   * 计划优先级值改变
   * 新建状态下 如果不是通过SR创建的工单 那么计划优先级改变的时候 同时改变报告优先级的值
   * @param {string} id 优先级ID
   * @param {string} name 优先级名称
   */
  @Bind
  handlePriorityChange(i) {
    const { basicDataDs, isNew, searchItem } = this.props;
    const { sourceParamType } = searchItem;

    if (isNew && !(sourceParamType && sourceParamType === 'SR')) {
      basicDataDs.current.set('reportPriorityId', i?.priorityId);
      basicDataDs.current.set('reportPriorityName', i?.priorityName);
    }
  }

  /**
   * 地图来源字段改变：
   * 1. 值为 不显示 NO_DISPLAY 时 不显示地图 清空经纬度的值
   * 2. 值为 位置 LOCATION 时， 取位置上的经纬度
   * 2. 值为 设备 ASSET 时， 取设备上的经纬度
   */
  @Bind
  handleMapSourceCodeChange(value) {
    const { basicDataDs } = this.props;
    switch (value) {
      case 'NO_DISPLAY':
        basicDataDs.current.init('longitude');
        basicDataDs.current.init('latitude');
        break;
      case 'LOCATION':
        this.handleSetLngAndLat('LOCATION');
        break;
      case 'ASSET':
        this.handleSetLngAndLat('ASSET');
        break;
      default:
        break;
    }
  }

  @Bind
  handleSetLngAndLat(type) {
    const { basicDataDs } = this.props;
    const { assetLocationId, assetId } = basicDataDs.current.toData();
    const url =
      type === 'LOCATION'
        ? `${queryLocationDetailUrl}/${assetLocationId}`
        : `${queryAssetDetailUrl}/${assetId}`;
    request(url, {
      method: 'GET',
    }).then(res => {
      if (res && !res.failed) {
        const { lng, lat } = res;
        basicDataDs.current.set('longitude', lng);
        basicDataDs.current.set('latitude', lat);
      }
    });
  }

  @Bind
  mapSourceOptionsFilter(record) {
    return record.get('value') !== 'MAP';
  }

  switchBasicTypeToServiceType(type) {
    let serviceType = '';
    switch (type) {
      case 'FAULT_MAINTAIN_TYPE':
        serviceType = 'FAULT_MAINTAIN_WO';
        break;
      case 'FAILURE':
        serviceType = 'FAILURE_WO';
        break;
      default:
        serviceType = undefined;
        break;
    }
    return serviceType;
  }

  /**
   * 获取默认职责
   */
  @Bind()
  handleQueryDefaultStaff() {
    const { isNew, handleRecord, basicDataDs } = this.props;
    const detail = basicDataDs.current?.toData() || {};
    const { maintSiteId, woBasicType, assetId, assetLocationId, woTypeCode } = detail;
    if (isNew && maintSiteId && woBasicType && this.shouldDefaultStaff()) {
      request(`${HALM_ORI}/v1/${organizationId}/default-staff`, {
        method: 'GET',
        query: {
          assetId,
          assetLocationId,
          tenantId: organizationId,
          serviceType: this.switchBasicTypeToServiceType(woBasicType),
          maintSiteId: detail.maintSiteId,
          docTypeCode: woTypeCode,
        },
      }).then(res => {
        if (res) {
          handleSetValueEmployee({
            type: 'both',
            defaultStaff: res,
            record: basicDataDs.current,
            handleRecord,
          });
        }
      });
    }
  }

  /**
   * 是否需要做默认职责操作
   */
  shouldDefaultStaff() {
    const { searchItem } = this.props;
    const { sourceParamType } = searchItem;
    // 只有从这5种来源创建的工单需要走默认职责逻辑
    // 或者从不知名来源创建工单
    const sourceTypeArr = ['ACT', 'Relate', 'SR', 'COPYWO', 'ALARM'];
    if (sourceParamType === undefined || sourceTypeArr.includes(sourceParamType)) {
      return true;
    } else {
      return false;
    }
  }

  // 基本数据 非查看 情况下的渲染 renderFlag: 是否渲染内容
  @Bind()
  basicDataEditRender(renderFlag) {
    const {
      isNew,
      editFlag,
      woId,
      history,
      searchItem,
      sourceDs,
      basicDataDs,
      itemProperty,
      tenantId,
      orgFlag, // 是否需要指定需求组织/客户 需要时才显示几个字段
      maintSitesIds,
      billingInfoFlag,
      disableAllByStatus,
      woBasicType,
      showSourceFlag,
      scheduleRequirmentStatus,
      onRefresh,
      currentEmployee,
      btnDisplayCfg = {},
    } = this.props;
    const { staticButtonList = [] } = btnDisplayCfg;
    const {
      serviceAgreemtHelpVisible,
      orgIdDisableFlag,
      // projectRelatedFlag,
    } = this.state;

    const detail = basicDataDs && basicDataDs.current ? basicDataDs.current.toData() : {};
    const {
      sourceTypeCode,
      maintSiteId,
      actName,
      assetId,
      manuallySpecifyFlag,
      assetLocationId,
      actId,
      billingInfoFlag: detailBillingInfoFlag,
      serviceAgreementId,
      agreementName,
      partnerName,
      costObjectLimitType,
      agreementDescription,
      waitingWoopownerFlag,
      mapSourceCode,
      longitude,
      latitude,
      sourceParamType,
      serviceAgreementFlag,
    } = detail;
    // 工单来源有值时,禁用: 报告人所在组织 报告人 报告时间 报告的优先级
    const disableBySourceTypeCode = !!sourceTypeCode;

    const bMapProps = {
      initialZoom: 15,
      initialLon: longitude,
      initialLat: latitude,
      mapHeight: 150,
      allowedScrollWheelZoom: true, // 新建/编辑时可以放大缩小 查看时不可
    };
    if (!renderFlag) {
      return null;
    }

    const sourceTableProps = {
      sitePageCode: 'WO', // 所在页面
      searchItem,
      isNew,
      editFlag,
      woId,
      history,
      sourceDs,
      basicDataDs,
      createRelateProjectFlag: staticButtonList?.includes('createRelateProject'),
      projectRelatedFlag: staticButtonList?.includes('relateProject'),
      srRelatedFlag: staticButtonList?.includes('relateWoAndSR'),
      woRelatedFlag: staticButtonList?.includes('relateWoAndSR'),
      onRefresh,
      assetId,
      maintSiteId,
      assetLocationId,
      employeeId: currentEmployee.employeeId,
      onChangRelatedPro: this.handleChangeRelatedPro,
    };

    // 服务申请创建(不包含新建)的工单需要禁用部分字段：设备、位置、报告人
    const sourceList = sourceDs?.toData() || [];
    const disabledCreateBySr =
      sourceList?.findIndex(i => i.sourceType === 'SR' && !i.canRemoveFlag) > -1;

    return (
      <React.Fragment>
        <Collapse bordered={false} defaultActiveKey={['OA']}>
          <Collapse.Panel key="OA" header={intl.get(`${viewPrompt}.panel.basic`).d('基本信息')}>
            <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
              <Lov
                name="maintSiteLov"
                disabled={
                  itemProperty.maintSiteId.disabled ||
                  disableAllByStatus ||
                  (['WBS', 'SR'].includes(searchItem.sourceParamType) && searchItem.maintSiteId)
                }
                onChange={record => this.handleMaintSiteChange(record)}
              />
              <Lov
                name="woTypeLov"
                disabled={itemProperty.woTypeId.disabled || disableAllByStatus}
                onChange={lovRecord => {
                  this.handleWoTypeChange(lovRecord);

                  let timeField =
                    lovRecord && lovRecord.upgradeRuleTimeField
                      ? lovRecord.upgradeRuleTimeField
                      : '';
                  timeField = this.toCamelCaseVar(timeField.toLowerCase());
                  this.setState({ upgradeRuleTimeField: timeField }); // 工单升级规则code
                }}
              />
              <TextField name="woName" disabled={disableAllByStatus} />
              {sourceParamType !== 'COPYWO' && (
                <ActLov
                  name="actName"
                  disabled={itemProperty.actId.disabled || disableAllByStatus}
                  queryParams={{
                    tenantId,
                    // 如果当前工单有服务区域，取当前工单的，如果没有，取当前用户对应员工所属组织下的服务区域
                    maintSiteIdList: maintSiteId || maintSitesIds,
                    effectiveEndDateFrom: moment().format(DEFAULT_DATETIME_FORMAT),
                  }}
                  // textValue 为undefined的时候 不能正常显示 需要为''才可
                  // 涉及改变 assetId 值的地方如下, 皆对assetName进行了处理：
                  // 1. 标准作业值改变 handleAct
                  // 2. 服务区域值改变 handleMaintSiteChange
                  textValue={actName}
                  onChange={(value, lovRecord) => this.handleAct(value, lovRecord)}
                  assetId={assetId}
                  assetLocationId={assetLocationId}
                />
              )}
              <TextArea colSpan={3} name="description" newLine disabled={disableAllByStatus} />
            </Form>
          </Collapse.Panel>
        </Collapse>
        {woBasicType && (
          <Collapse bordered={false} defaultActiveKey={['OB', 'OC', 'OD', 'RP']}>
            {woBasicType !== 'CHECKING_TYPE' && (
              <Collapse.Panel
                key="OB"
                header={intl.get(`${viewPrompt}.panel.workObj`).d('工作对象')}
              >
                <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
                  {orgFlag && (
                    <OrgPartnerLov
                      name="orgName"
                      handleOk={this.handleChangeOrg}
                      disabled={orgIdDisableFlag}
                    />
                  )}
                  {orgFlag && manuallySpecifyFlag === 0 && (
                    <Lov
                      name="contactLov"
                      disabled={disableAllByStatus}
                      onChange={lovRecord => this.handleChangeContact(lovRecord)}
                    />
                  )}
                  {orgFlag && (
                    <Switch
                      name="manuallySpecifyFlag"
                      onChange={this.handleChangeManually}
                      disabled={disableAllByStatus}
                    />
                  )}

                  {orgFlag && manuallySpecifyFlag === 1 && (
                    <TextField name="contactDesc" newLine disabled={disableAllByStatus} />
                  )}
                  {orgFlag && <TextField name="phone" disabled={disableAllByStatus} />}
                  <AssetLov
                    isLimited
                    newLine
                    name="descAndLabel"
                    disabled={
                      itemProperty.assetId.disabled ||
                      disableAllByStatus ||
                      (disabledCreateBySr && !isNew) ||
                      (searchItem.sourceParamType === 'SR' &&
                        (searchItem.assetId || searchItem.assetLocationId))
                    }
                    queryParams={{
                      tenantId,
                      assetLocationId,
                      maintSiteId,
                      maintainFlag: 1,
                    }}
                    onOk={this.onAssetChange}
                    actId={actId}
                  />
                  <LocLov
                    name="assetLocationName"
                    disabled={
                      itemProperty.assetLocationId.disabled ||
                      disableAllByStatus ||
                      (disabledCreateBySr && !isNew) ||
                      (searchItem.sourceParamType === 'SR' &&
                        (searchItem.assetId || searchItem.assetLocationId))
                    }
                    queryParams={{
                      organizationId: tenantId,
                      directMaintainFlag: 1,
                      maintSiteId,
                    }}
                    onChange={this.handleLocationChange}
                    actId={actId}
                  />
                  <TextField name="locationDesc" disabled={disableAllByStatus} />
                  {costObjectLimitType !== 'NOT_DISPLAY' && (
                    <Select
                      name="costObjectType"
                      disabled={itemProperty?.costObjectType?.disabled}
                      optionsFilter={record => {
                        // 仅保留成本中心、内部订单、WBS要素
                        return (
                          record.get('value') === 'COST_CENTER' ||
                          record.get('value') === 'INTERNAL_ORDER' ||
                          record.get('value') === 'WBS_ELEMENT'
                        );
                      }}
                      onChange={this.handleChangeCostObjType}
                    />
                  )}
                  {costObjectLimitType !== 'NOT_DISPLAY' && (
                    <Lov name="costObjectLov" disabled={itemProperty?.costObjectLov?.disabled} />
                  )}
                  <Select
                    name="mapSourceCode"
                    disabled={disableAllByStatus}
                    optionsFilter={this.mapSourceOptionsFilter}
                    onChange={this.handleMapSourceCodeChange}
                  />
                  {(billingInfoFlag || detailBillingInfoFlag) && (
                    <Lov
                      name="serviceAgreementLov"
                      onChange={this.handleServiceAgreemtChange}
                      disabled={serviceAgreementFlag}
                      help={
                        serviceAgreemtHelpVisible || serviceAgreementId ? (
                          <div className={styles['service-agreemt-tooltip']}>
                            <div className={styles['tooltip-popup-arrow']} />
                            <div className={styles['tooltip-popup-inner']}>
                              <div>
                                {`${intl
                                  .get(`${modelPrompt}.serviceAgreementName`)
                                  .d('服务协议名称')}：${agreementName || null}`}
                              </div>
                              <div>
                                {`${intl.get(`${modelPrompt}.partnerName`).d('客户信息')}：${
                                  partnerName || null
                                }`}
                              </div>
                              <div>
                                {`${intl.get(`${modelPrompt}.agreementDesc`).d('协议描述')}：${
                                  agreementDescription || null
                                }`}
                              </div>
                            </div>
                          </div>
                        ) : null
                      }
                    />
                  )}
                </Form>
                {mapSourceCode && mapSourceCode !== 'NO_DISPLAY' && <BMap {...bMapProps} />}
              </Collapse.Panel>
            )}
            <Collapse.Panel
              key="OC"
              header={intl.get(`${viewPrompt}.panel.workDuty`).d('工作职责')}
            >
              <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
                <EmployeesLov
                  name="plannerGroupName"
                  disabled={disableAllByStatus}
                  record={basicDataDs.current}
                  queryParams={{
                    maintSiteId,
                  }}
                  onOk={this.handleChangePlannerGroup}
                />
                <EmployeesLov
                  name="plannerName"
                  disabled={disableAllByStatus}
                  record={basicDataDs.current}
                  queryParams={{
                    maintSiteId,
                  }}
                  onOk={this.handleChangePlanner}
                />
                <EmployeesLov
                  newLine
                  name="ownerGroupName"
                  disabled={disableAllByStatus}
                  record={basicDataDs.current}
                  queryParams={{
                    maintSiteId,
                  }}
                  onOk={this.handleChangeOwnerGroup}
                />
                <EmployeesLov
                  name="ownerName"
                  disabled={waitingWoopownerFlag === 1 || disableAllByStatus}
                  record={basicDataDs.current}
                  queryParams={{
                    maintSiteId,
                  }}
                  onOk={this.handleChangeOwner}
                />
                <Switch
                  name="waitingWoopownerFlag"
                  disabled={itemProperty.waitingWoopownerFlag.disabled || disableAllByStatus}
                  onChange={value => this.onWaitWoopownerChange(value)}
                />
                <Lov
                  name="priorityLov"
                  newLine
                  disabled={disableAllByStatus}
                  tableProps={priorityTableProps}
                  onChange={this.handlePriorityChange}
                />
                {/* 工单可直接执行时隐藏 计划后下达工单显示 */}
                {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
                  <NumberField
                    name="durationScheduled"
                    disabled={disableAllByStatus}
                    onChange={this.handleDurationScheduledChange}
                  />
                )}
                <Select
                  name="durationUom"
                  onChange={this.handleDurationUomChange}
                  disabled={disableAllByStatus}
                />
                {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
                  <DateTimePicker
                    name="scheduledStartDate"
                    disabled={disableAllByStatus}
                    onChange={this.handleScheduledStartDateChange}
                  />
                )}
                {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
                  <DateTimePicker
                    name="scheduledFinishDate"
                    disabled={disableAllByStatus}
                    onChange={this.handleScheduledFinishDateChange}
                  />
                )}
                {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
                  <DateTimePicker
                    newLine
                    name="targetStartDate"
                    disabled={itemProperty.targetStartDate.disabled || disableAllByStatus}
                  />
                )}
                {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
                  <DateTimePicker
                    name="targetFinishDate"
                    disabled={itemProperty.targetFinishDate.disabled || disableAllByStatus}
                  />
                )}
              </Form>
            </Collapse.Panel>
            <Collapse.Panel
              key="RP"
              header={intl.get(`${viewPrompt}.panel.relatedDoc`).d('关联单据')}
            >
              <SourceTable {...sourceTableProps} />
            </Collapse.Panel>
            {showSourceFlag && (
              <Collapse.Panel
                key="OD"
                header={intl.get(`${viewPrompt}.panel.workSource`).d('工作来源信息')}
              >
                <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
                  <Lov
                    name="reporterOrgLov" // 报告人所在组织
                    disabled={
                      disableBySourceTypeCode ||
                      disableAllByStatus ||
                      (disabledCreateBySr && !isNew)
                    }
                    onChange={this.handleChangeReportOrg}
                  />
                  <Lov
                    name="reporterLov"
                    disabled={
                      disableBySourceTypeCode ||
                      disableAllByStatus ||
                      (disabledCreateBySr && !isNew)
                    }
                    onChange={value => this.handleChangeReport(value)}
                  />
                  <DateTimePicker
                    name="reportDate"
                    disabled={
                      disableBySourceTypeCode ||
                      itemProperty.reportDate.disabled ||
                      disableAllByStatus ||
                      (disabledCreateBySr && !isNew)
                    }
                  />
                  <Lov
                    name="reportPriorityLov"
                    newLine
                    tableProps={priorityTableProps}
                    disabled={
                      disableBySourceTypeCode ||
                      itemProperty.reportPriorityId.disabled ||
                      disableAllByStatus
                    }
                  />
                </Form>
              </Collapse.Panel>
            )}
          </Collapse>
        )}
      </React.Fragment>
    );
  }

  // 基本数据 查看 情况下的渲染 renderFlag: 是否渲染内容
  @Bind
  basicDataViewRender(renderFlag) {
    const {
      isNew,
      editFlag,
      basicDataDs,
      orgFlag, // 是否需要指定需求组织/客户 需要时才显示几个字段
      billingInfoFlag,
      woBasicType,
      showSourceFlag,
      scheduleRequirmentStatus,
      history,
      woId,
      sourceDs,
      onRefresh,
      viewFlag, // 页面只读，不增删改
      disableOptionFlag, // 页面不可做任何操作，
      currentEmployee,
      btnDisplayCfg = {},
    } = this.props;
    const { staticButtonList = [] } = btnDisplayCfg;
    const basicData = basicDataDs && basicDataDs.current ? basicDataDs.current.toData() : {}; // 头部数据 查看界面使用

    const {
      plannerName,
      plannerGroupName,
      ownerName,
      ownerGroupName,
      scheduledStartDate,
      scheduledFinishDate,
      durationScheduled,
      durationUomMeaning,
      actualFinishDate,
      actualStartDate,
      targetStartDate,
      targetFinishDate,
      manuallySpecifyFlag,
      mapSourceCode,
      longitude,
      latitude,
      assetId,
      assetLocationId,
      maintSiteId,
      costObjectLimitType,
      // 以下字段计划调度侧弹显示工单时根据字段有值则展示，否则隐藏
      descAndLabel,
      assetLocationName,
      locationDesc,
      serviceAgreementLov,
      phone,
      contactDesc,
      contactName,
      orgName,
      checkerId,
    } = basicData;

    const bMapProps = {
      initialZoom: 15,
      initialLon: longitude,
      initialLat: latitude,
      mapHeight: 150,
      allowedScrollWheelZoom: false, // 新建/编辑时可以放大缩小 查看时不可
    };

    const searchItem = {
      sourceParamType: 'WO',
    };

    const sourceTableProps = {
      sitePageCode: 'WO', // 所在页面
      isNew,
      editFlag,
      searchItem,
      createRelateProjectFlag: staticButtonList?.includes('createRelateProject'),
      projectRelatedFlag: staticButtonList?.includes('relateProject'),
      woId,
      history,
      sourceDs,
      basicDataDs,
      srRelatedFlag: staticButtonList?.includes('relateWoAndSR'),
      woRelatedFlag: staticButtonList?.includes('relateWoAndSR'),
      onRefresh,
      assetId,
      maintSiteId,
      assetLocationId,
      employeeId: currentEmployee.employeeId,
      viewFlag,
      disableOptionFlag,
      onChangRelatedPro: this.handleChangeRelatedPro,
    };

    if (!renderFlag) {
      return null;
    }
    // 职责面板：通过设置viewFlag为true计划调度侧弹查看工单-工作对象、工作职责面板合并为一；否则工作单分开显示；
    const renderWorkDuty = () => {
      return (
        <>
          <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
            <Output
              name="waitingWoopownerFlag"
              renderer={({ text }) => yesOrNoRender(Number(text))}
            />
            <Output name="priorityLov" />
            {checkerId && <Output name="checkerName" />}
          </Form>
          <div className={styles['view-of-work-duty']}>
            <div className={styles['first-div']}>
              <div className={styles['first-div-left']}>
                <div>
                  <Icon style={{ fontSize: 20, color: '#3889FF' }} type="people_outline" />
                  &nbsp;{intl.get(`${modelPrompt}.plannerGroup`).d('计划员组')}&nbsp;
                  <span style={{ fontWeight: 'bold' }}>{plannerGroupName}</span>
                </div>
                <div>
                  <Icon style={{ fontSize: 20, color: '#3889FF' }} type="person_outline" />
                  &nbsp;{intl.get(`${modelPrompt}.planner`).d('计划员')}&nbsp;
                  <span style={{ fontWeight: 'bold' }}>{plannerName}</span>
                </div>
              </div>
              <div className={styles['first-div-middle']}>
                <div />
              </div>
              <div className={styles['first-div-right']}>
                <div>
                  <Icon style={{ fontSize: 20, color: '#3889FF' }} type="people_outline" />
                  &nbsp;{intl.get(`${modelPrompt}.ownerGroup`).d('负责人组')}&nbsp;
                  <span style={{ fontWeight: 'bold' }}>{ownerGroupName}</span>
                </div>
                <div>
                  <Icon style={{ fontSize: 20, color: '#3889FF' }} type="person_outline" />
                  &nbsp;{intl.get(`${modelPrompt}.owner`).d('负责人')}&nbsp;
                  <span style={{ fontWeight: 'bold' }}>{ownerName}</span>
                </div>
              </div>
            </div>
            {/* 工单可直接执行时隐藏 计划后下达工单显示 */}
            {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
              <div className={styles['second-div']}>
                <div className={styles['second-div-content']}>
                  <div className={styles['second-div-content-time']}>
                    <div className={styles['second-div-content-time-top']}>
                      {scheduledStartDate}
                    </div>
                    <div className={styles['second-div-content-time-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.scheduledStartDate`).d('计划开始时间')}
                    </div>
                  </div>
                  <div
                    className={`${styles['second-div-content-middle']} ${styles['second-div-content-middle1']}`}
                  >
                    <div />
                  </div>
                  <div className={styles['second-div-content-total']}>
                    <div className={styles['second-div-content-total-top']}>
                      {durationScheduled}&nbsp;{durationUomMeaning}
                    </div>
                    <div className={styles['second-div-content-total-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.durationScheduled`).d('计划时长')}
                    </div>
                  </div>
                  <div
                    className={`${styles['second-div-content-middle']} ${styles['second-div-content-middle2']}`}
                  >
                    <div />
                  </div>
                  <div className={styles['second-div-content-time']}>
                    <div className={styles['second-div-content-time-top']}>
                      {scheduledFinishDate}
                    </div>
                    <div className={styles['second-div-content-time-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.scheduledFinishDate`).d('计划完成时间')}
                    </div>
                  </div>
                </div>
              </div>
            )}
            {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
              <div className={styles['third-div']}>
                <div className={styles['third-div-left']}>
                  <div className={styles['third-div-common-time']}>
                    <div className={styles['third-div-common-time-top']}>{targetStartDate}</div>
                    <div className={styles['third-div-common-time-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.targetStartDate`).d('目标开始时间')}
                    </div>
                  </div>
                  <div className={styles['third-div-common-middle']}>
                    <div />
                  </div>
                  <div className={styles['third-div-common-time']}>
                    <div className={styles['third-div-common-time-top']}>{targetFinishDate}</div>
                    <div className={styles['third-div-common-time-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.targetFinishDate`).d('目标完成时间')}
                    </div>
                  </div>
                </div>
                <div className={styles['third-div-middle']} />
                <div className={styles['third-div-right']}>
                  <div className={styles['third-div-common-time']}>
                    <div className={styles['third-div-common-time-top']}>{actualStartDate}</div>
                    <div className={styles['third-div-common-time-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.actualStartDate`).d('实际开始时间')}
                    </div>
                  </div>
                  <div className={styles['third-div-common-middle']}>
                    <div />
                  </div>
                  <div className={styles['third-div-common-time']}>
                    <div className={styles['third-div-common-time-top']}>{actualFinishDate}</div>
                    <div className={styles['third-div-common-time-bottom']}>
                      <Icon type="schedule" style={{ fontSize: 20, color: '#3889FF' }} />
                      &nbsp;{intl.get(`${modelPrompt}.actualFinishDate`).d('实际完成时间')}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </>
      );
    };

    return (
      <React.Fragment>
        {woBasicType !== 'CHECKING_TYPE' && (
          <Collapse bordered={false} defaultActiveKey={['OA']}>
            <Collapse.Panel key="OA" header={intl.get(`${viewPrompt}.panel.workObj`).d('工作对象')}>
              {/* 计划调度中心-工作对象只在字段有值的时候才显示字段 */}
              {viewFlag && !disableOptionFlag ? (
                <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
                  {orgFlag && !!orgName && <Output name="orgName" />}
                  {orgFlag && manuallySpecifyFlag === 0 && !!contactName && (
                    <Output name="contactName" />
                  )}
                  {orgFlag && !!manuallySpecifyFlag && (
                    <Output
                      name="manuallySpecifyFlag"
                      renderer={({ text }) => yesOrNoRender(Number(text))}
                    />
                  )}

                  {orgFlag && manuallySpecifyFlag === 1 && !!contactDesc && (
                    <Output name="contactDesc" newLine />
                  )}
                  {orgFlag && !!phone && <Output name="phone" />}
                  {!!descAndLabel && <Output name="descAndLabel" />}
                  {!!assetLocationName && <Output name="assetLocationName" />}
                  {!!locationDesc && <Output name="locationDesc" />}
                  {costObjectLimitType !== 'NOT_DISPLAY' && <Output name="costObjectType" />}
                  {costObjectLimitType !== 'NOT_DISPLAY' && <Output name="costObjectLov" />}
                  {!!mapSourceCode && <Output name="mapSourceCode" />}
                  {billingInfoFlag && !!serviceAgreementLov && (
                    <Output name="serviceAgreementLov" />
                  )}
                </Form>
              ) : (
                <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
                  {orgFlag && <Output name="orgName" />}
                  {orgFlag && manuallySpecifyFlag === 0 && <Output name="contactName" />}
                  {orgFlag && (
                    <Output
                      name="manuallySpecifyFlag"
                      renderer={({ text }) => yesOrNoRender(Number(text))}
                    />
                  )}

                  {orgFlag && manuallySpecifyFlag === 1 && <Output name="contactDesc" newLine />}
                  {orgFlag && <Output name="phone" />}
                  <Output newLine name="descAndLabel" />
                  <Output name="assetLocationName" />
                  <Output name="locationDesc" />
                  {costObjectLimitType !== 'NOT_DISPLAY' && <Output name="costObjectType" />}
                  {costObjectLimitType !== 'NOT_DISPLAY' && <Output name="costObjectLov" />}
                  <Output name="mapSourceCode" />
                  {billingInfoFlag && <Output name="serviceAgreementLov" />}
                </Form>
              )}
              {/* 当非检查类型时：计划调度中心侧弹显示时，工作对象与工作职责合并显示 */}
              {viewFlag && !disableOptionFlag && renderWorkDuty()}
              {mapSourceCode && mapSourceCode !== 'NO_DISPLAY' && <BMap {...bMapProps} />}
            </Collapse.Panel>
          </Collapse>
        )}
        {/* 只用于计划调度中心侧弹工单为检查类型时工作对象需要显示工作职责相关的字段 */}
        {woBasicType === 'CHECKING_TYPE' && viewFlag && !disableOptionFlag && (
          <Collapse bordered={false} defaultActiveKey={['OA']}>
            <Collapse.Panel key="OA" header={intl.get(`${viewPrompt}.panel.workObj`).d('工作对象')}>
              {renderWorkDuty()}
            </Collapse.Panel>
          </Collapse>
        )}
        <Collapse bordered={false} defaultActiveKey={['OB', 'OC', 'RP']}>
          {/* 工作单、审批表单 单独显示工作职责 */}
          {(!viewFlag || (viewFlag && disableOptionFlag)) && (
            <Collapse.Panel
              key="OB"
              header={intl.get(`${viewPrompt}.panel.workDuty`).d('工作职责')}
            >
              {renderWorkDuty()}
            </Collapse.Panel>
          )}
          <Collapse.Panel
            key="OC"
            header={intl.get(`${viewPrompt}.panel.workSource`).d('工作来源信息')}
          >
            <Form dataSet={basicDataDs} labelWidth={120} columns={3} className="inclusion-row">
              {showSourceFlag && <Output name="reportOrgName" />}
              {showSourceFlag && <Output name="reporterName" />}
              {showSourceFlag && <Output name="reportDate" />}
              {showSourceFlag && <Output name="reportPriorityLov" />}
              <Output name="actName" />
            </Form>
          </Collapse.Panel>
          <Collapse.Panel
            key="RP"
            header={intl.get(`${viewPrompt}.panel.relatedDoc`).d('关联单据')}
          >
            <SourceTable {...sourceTableProps} />
          </Collapse.Panel>
        </Collapse>
      </React.Fragment>
    );
  }

  // 处理关联项目信息
  @Bind()
  handleChangeRelatedPro(obj = {}) {
    const { basicDataDs } = this.props;
    basicDataDs.current.set('projectId', obj.sourceId);
    basicDataDs.current.set('taskCode', obj.taskCode);
    basicDataDs.current.set('editProjectFlag', true);
  }

  render() {
    const { activeKey } = this.state;
    const {
      woId,
      isNew,
      tenantId,
      editFlag,
      isOpenPerson,
      outsourceFlag,
      basicDataDs,
      woopListDs,
      woopListLoading,
      history,
      woopListPagenation,
      onWoopListPageChange,
      onSearchWoopList,
      onRefresh,
      onChangeWoStatus,
      onRefreshBasicAndCurrentTab,
      isOpenCheck,
      showFailureFlag,
      costFlag,
      billingInfoFlag,
      isOpenMaterial,
      currentEmployee,
      onIgnoreChecklist,
      onSetState,
      onOpenWrdModal,
      viewFlag = false, // 标识页面只读，不可进行增删改，目前计划调度中心会设置为true，其余默认为false
      disableOptionFlag = false, // 页面禁止任何点击查看跳转、显示弹窗的操作，只显示顶层界面查看，作为审批表单时会设置为true，其余默认为false
      btnDisplayCfg = {},
    } = this.props;
    const { staticButtonList = [] } = btnDisplayCfg;

    const basicData = basicDataDs && basicDataDs.current ? basicDataDs.current.toData() : {}; // 头部数据 查看界面使用

    const {
      woStatus,
      woNum,
      woName,
      serviceAgreementId,
      maintSiteId,
      durationUom,
      woBasicType,
      assetId,
      currencyName,
      descAndLabel,
      durationScheduled,
      woChecklistFlag, // 是否有检查项
      woMaterialFlag, // 是否有物料
      woOutFlag, // 是否有委外
      woLaborFlag, // 是否有人员
      woServiceFlag, // 是否有结算信息
      woMalfunctionFlag, // 是否有故障信息
      ifShowSignFlag, // 是否显示需签到字段
    } = basicData;

    const isCanceled = woStatus === 'CANCELED';
    // 三种结束状态：取消 关闭 无法执行
    const isEnd = ['CANCELED', 'CLOSED', 'UNABLE'].includes(woStatus);
    const woopListProps = {
      woId,
      woBasicType,
      durationUom,
      maintSiteId,
      durationScheduled,
      onTabsChange: this.handleTabsChange,
      onShowChecklist: () => this.checklistRef?.current.woChecklistRef?.showChecklist?.(),
      onSearchWoopList,
      onRefresh,
      onRefreshBasicAndCurrentTab,
      woopListPagenation,
      onWoopListPageChange,
      onIgnoreChecklist,
      onChangeWoStatus,
      basicDataDs,
      onOpenWrdModal,
      ds: woopListDs,
      loading: woopListLoading,
      showCreateBtn: !viewFlag && staticButtonList?.includes('addTask'),
      viewFlag, // viewFlag不可增删改，控制样式，但还允许表格或者其他的点击查看操作
      disableOptionFlag, // 禁止任何点击操作，只可查看顶层界面
      ifShowSignFlag,
      staticButtonList,
    };

    const serviceSettleProps = {
      woId,
      isNew,
      editFlag,
      woStatus,
      serviceAgreementId,
      viewFlag, // viewFlag不显示新增
      staticButtonList,
    };

    // 检查项Props
    const woChecklistListProps = {
      isNew,
      woId,
      editFlag,
      disableBtn: !staticButtonList?.includes('alarmTabCreatWoAndSr'),
      history,
      activeKey,
      onSetState,
      parentId: woId,
      status: woStatus,
      parentNum: woNum,
      parentName: woName,
      isEndFlag: viewFlag || isEnd || ['APPROVING', 'WAITCOMPCHECK'].includes(woStatus), // viewFlag不显示新增
      hiddenBtn: viewFlag || !staticButtonList?.includes('alarmTabCreatWoAndSr'), // 是否需要隐藏按钮； viewFlag仅查看隐藏创建工单，创建服务申请按钮
      viewFlag, // 控制仅查看时不显示’显示未结检查项‘按钮
      disableOptionFlag, // 审批表单时控制不显示表格操作列
      detail: basicData,
      staticButtonList,
    };

    const woMalfunctionListProps = {
      isNew,
      woopList: woopListDs,
      editFlag,
      woId,
      woStatus,
      woName,
      isEndFlag: viewFlag || !staticButtonList?.includes('generalTab'), // viewFlag仅查看不显示新增按钮
      woAssetId: assetId,
      woDescAndLabel: descAndLabel,
      viewFlag, // 仅查看控制样式
      disableOptionFlag, // 审批表单时控制不显示表格操作列
      employeeId: currentEmployee.employeeId,
    };

    // 物料 Props
    const materialProps = {
      woId,
      history,
      tenantId,
      woStatus,
      isCanceled,
      addInvestItemFlag: viewFlag ? false : staticButtonList?.includes('generalTab'), // viewFlag,仅查看，不允许新增
      editInvestItemFlag: viewFlag ? false : staticButtonList?.includes('materialServiceInAndOut'), // viewFlag,仅查看，不允许编辑
      delItemFlag: staticButtonList?.includes('materialServiceDelete'),
      editReturnItemFlag: viewFlag ? false : staticButtonList?.includes('materialServiceInAndOut'), // viewFlag,仅查看，不允许编辑
      generateApplyFlag: viewFlag ? false : staticButtonList?.includes('materialApply'), // viewFlag 仅查看不显示生成申请按钮
      woopListDs,
      maintSiteId,
      currentEmployee,
      woDetail: basicData,
    };
    const woLaborsListProps = {
      isNew,
      editFlag,
      parentId: woId,
      parentType: 'WO',
      isEndFlag: viewFlag || isEnd || woStatus === 'APPROVING', // viewFlag仅查看，不显示新增按钮
      woopList: woopListDs,
      maintSiteId, // 服务区域
      woStatus,
      currencyName,
      disableOptionFlag, // 审批表单时控制不显示表格操作列
      staticButtonList,
    };
    const costProps = {
      woId,
    };

    return (
      <div className={styles['work-order-infoExhibit']}>
        {isNew ? (
          this.basicDataEditRender(true)
        ) : (
          <Tabs activeKey={activeKey} onChange={this.handleTabsChange} defaultActiveKey="basicTab">
            <Tabs.TabPane tab={intl.get(`${viewPrompt}.tab.basic`).d('基本')} key="basicTab">
              {/* 解决编辑/查看 状态转换时 地图组件不会卸载再重新挂载的问题： */}
              {/* {editFlag ? this.basicDataEditRender() : this.basicDataViewRender()} */}
              {this.basicDataEditRender(editFlag)}
              {this.basicDataViewRender(!editFlag)}
            </Tabs.TabPane>
            <Tabs.TabPane tab={intl.get(`${viewPrompt}.tab.task`).d('任务')} key="taskTab">
              <WoopList {...woopListProps} ref={this.woopListRef} />
            </Tabs.TabPane>
            {isNew || (!showFailureFlag && !woMalfunctionFlag) ? null : (
              <Tabs.TabPane
                tab={intl.get(`${viewPrompt}.tab.malfunction`).d('故障')}
                key="malfunctionTab"
              >
                <WoMalfunctionList {...woMalfunctionListProps} ref={this.malRef} />
              </Tabs.TabPane>
            )}
            {isNew || (!isOpenCheck && !woChecklistFlag) ? null : (
              <Tabs.TabPane
                tab={intl.get(`${viewPrompt}.tab.checklist`).d('检查项')}
                key="checklistTab"
              >
                <WoChecklistList {...woChecklistListProps} ref={this.checklistRef} />
              </Tabs.TabPane>
            )}
            {isNew || (!isOpenMaterial && !woMaterialFlag) ? null : (
              <Tabs.TabPane
                tab={intl.get(`${viewPrompt}.tab.material`).d('物料')}
                key="materialTab"
              >
                <Material {...materialProps} ref={this.materialRef} />
              </Tabs.TabPane>
            )}
            {isNew || (!outsourceFlag && !woOutFlag) ? null : (
              <Tabs.TabPane
                tab={intl.get(`${viewPrompt}.tab.outsourcing`).d('费用')}
                key="outsourcingTab"
              >
                <WoOutsourcing
                  {...woLaborsListProps}
                  displayBtns={staticButtonList?.includes('generalTab')}
                  ref={this.outsourcingRef}
                />
              </Tabs.TabPane>
            )}
            {isNew || (!isOpenPerson && !woLaborFlag) ? null : (
              <Tabs.TabPane tab={intl.get(`${viewPrompt}.tab.labor`).d('人员')} key="laborsTab">
                <WoLaborsList {...woLaborsListProps} ref={this.woLaborsRef} />
              </Tabs.TabPane>
            )}
            {serviceAgreementId && (billingInfoFlag || (!billingInfoFlag && woServiceFlag)) ? (
              <Tabs.TabPane
                tab={intl.get(`${viewPrompt}.tab.serviceSettlement`).d('服务结算')}
                key="serviceSettleTab"
              >
                <ServiceSettlement {...serviceSettleProps} ref={this.serviceSettleRef} />
              </Tabs.TabPane>
            ) : null}
            {isNew || !costFlag ? null : (
              <Tabs.TabPane tab={intl.get(`${viewPrompt}.tab.cost`).d('成本')} key="costTab">
                <WoCostList {...costProps} ref={this.costRef} />
              </Tabs.TabPane>
            )}
          </Tabs>
        )}
      </div>
    );
  }
}

export default Detail;
