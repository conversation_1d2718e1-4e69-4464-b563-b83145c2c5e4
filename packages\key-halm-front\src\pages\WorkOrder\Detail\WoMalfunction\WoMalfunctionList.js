/*
 * @Description: 故障：委外、工单共用
 */

import React, { Component } from 'react';
import { Spin, Row, Col, Icon, Popconfirm, Tooltip } from 'choerodon-ui';
import { Modal } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Bind } from 'lodash-decorators';
import { isNull, isUndefined } from 'lodash';
import { HALM_MTC } from 'alm/utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';

import WoMalfunctionModal from './WoMalfunctionModal';
import FileUpload from './FileUpload';

import styles from './index.less';

const modelPrompt = 'amtc.woMalfunction.model.woMalfunction';
const viewPrompt = 'amtc.woMalfunction.view.message';

const organizationId = getCurrentOrganizationId();
// 查询故障列表数据
const queryWoMalfunctionListUrl = `${HALM_MTC}/v1/${organizationId}/wo-malfunctions`;
// 查询评估项
const queryDefualtRcAssesmentUrl = `${HALM_MTC}/v1/${organizationId}/eval-item`;

class WoMalfunctionList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      currentData: {},
      list: [],
    };
    this.editModalRef = React.createRef();
  }

  /**
   * 获取故障列表
   */
  @Bind()
  handleRefresh() {
    const { woId } = this.props;
    this.setState({ loading: true });
    request(queryWoMalfunctionListUrl, {
      method: 'GET',
      query: {
        tenantId: organizationId,
        woId,
      },
    }).then(res => {
      if (res && res.content) {
        const data = res.content;
        let filData = data.filter(i => i.evalItemId !== null);
        filData = filData.map(i => {
          const meaningArr = [];
          i.basicTypeCodeList.forEach(code => {
            if (
              !isUndefined(i[`${code.toLowerCase()}CodeMeaning`]) &&
              !isNull(i[`${code.toLowerCase()}CodeMeaning`])
            ) {
              meaningArr.push(i[`${code.toLowerCase()}CodeMeaning`]);
            }
          });
          return { ...i, detail: meaningArr.length > 0 ? meaningArr.join('/') : '' };
        });
        // 先重置 以确保故障信息刷新的时候 所有附件都刷新
        this.setState({
          list: [],
        });
        this.setState({
          list: filData,
        });
      }
      this.setState({ loading: false });
    });
  }

  /**
   * 删除
   */
  @Bind()
  handleDeleteLine(record) {
    request(queryWoMalfunctionListUrl, {
      method: 'DELETE',
      body: record,
    }).then(res => {
      if (res && res.failed) {
        notification.warning({ message: res.message });
      } else {
        notification.success();
        // 刷新列表
        this.handleRefresh();
      }
    });
  }

  /**
   * 新增
   */
  @Bind()
  async handleCreateWoMalfunction() {
    const { woopList, woId, woAssetId, woDescAndLabel } = this.props;
    const currentData = {
      woId,
      assetId: woAssetId,
      assetMeaning: woDescAndLabel,
      fileModuleId: Date.now(),
    };
    await this.setState({
      currentData,
    });
    if (woopList.records.length === 1) {
      currentData.srId = woopList.records[0].get('woopId');
      currentData.woopMeaning = woopList.records[0].get('woopShortName');
      // 查询评估项
      if (woopList.records[0].get('assetId')) {
        request(queryDefualtRcAssesmentUrl, {
          method: 'GET',
          query: {
            tenantId: organizationId,
            assetId: woopList.records[0].get('assetId'),
          },
        }).then(res => {
          if (res && res.failed) {
            notification.warning({ message: res.message });
          } else if (res && res.content && res.content.length === 1) {
            currentData.evalItemId = res.content[0].evalItemId;
            currentData.evalItemMeaning = res.content[0].evalItemName;
            // this.handleWoMalfunctionModal();
          }
          this.handleWoMalfunctionModal();
        });
      } else {
        this.handleWoMalfunctionModal();
      }
    } else {
      this.handleWoMalfunctionModal();
    }
  }

  /**
   * 编辑
   */
  @Bind()
  async handleEditLine(record) {
    const { woId } = this.props;
    await this.setState({
      // 旧数据修改时没有fileModuleId，则生成一个保存
      currentData: { ...record, woId, fileModuleId: record?.fileModuleId || Date.now() },
    });
    this.handleWoMalfunctionModal();
  }

  /**
   * 打开故障modal
   */
  @Bind()
  handleWoMalfunctionModal() {
    const { currentData } = this.state;
    const { woId } = this.props;
    const modalProps = {
      woId,
      currentData,
    };
    Modal.open({
      key: 'woMalfunction',
      maskClosable: true, // 点击蒙层是否允许关闭
      keyboardClosable: true, // 按 esc 键是否允许关闭
      destroyOnClose: true, // 关闭时是否销毁
      closable: true,
      drawer: true,
      style: {
        width: 900,
      },
      cancelText: intl.get('hzero.common.button.cancel').d('取消'),
      okText: intl.get('hzero.common.button.save').d('保存'),
      children: <WoMalfunctionModal {...modalProps} ref={this.editModalRef} />,
      onOk: async () => {
        if (this.editModalRef && this.editModalRef.current) {
          const result = await this.editModalRef.current.woMalfunctionModalDs.validate();
          if (result) {
            this.handleWoMalfunctionModalOk();
          } else {
            return false;
          }
        }
      },
    });
  }

  @Bind()
  handleWoMalfunctionModalOk() {
    const data = this.editModalRef.current.woMalfunctionModalDs.toData();
    request(queryWoMalfunctionListUrl, {
      method: 'POST',
      body: data,
    }).then(res => {
      if (res && res.failed) {
        notification.warning({ message: res.message });
      } else {
        notification.success();
        // 刷新列表
        this.handleRefresh();
      }
    });
  }

  @Bind
  renderItem(data, index) {
    // viewFlag: 计划调度仅查看，不显示别的按钮
    const { isEndFlag, viewFlag, employeeId } = this.props;
    const {
      evalItemMeaning,
      malfunctionTime,
      description,
      remark,
      basicTypeCodeList,
      fileModuleId,
    } = data;
    const labelStyle = { color: '#4F5972' };
    const textStyle = { fontWeight: 'bold', color: '#333333' };
    return (
      <div className={styles['task-item']} key={`mal-item-${index}`}>
        <div className={styles['task-item-content-wrapper']}>
          <div
            className={styles['task-item-content']}
            style={viewFlag ? { borderRight: 'none' } : null}
          >
            <div className={styles['task-item-content-middle']}>
              <Row>
                <Col span={12}>
                  <Row className={styles['task-item-content-middle-first']}>
                    <span style={labelStyle}>
                      {intl.get(`${modelPrompt}.rcAssesment`).d('故障字典')}：
                    </span>
                    <span style={textStyle}>
                      <Tooltip title={evalItemMeaning}>{evalItemMeaning}</Tooltip>
                    </span>
                  </Row>
                  <Row className={styles['task-item-content-middle-line']}>
                    <span style={labelStyle}>
                      {intl.get(`${modelPrompt}.malfunctionTime`).d('故障时间')}：
                    </span>
                    <span style={textStyle}>
                      <Tooltip title={malfunctionTime}>{malfunctionTime}</Tooltip>
                    </span>
                  </Row>
                  <Row className={styles['task-item-content-middle-line']}>
                    <span style={labelStyle}>
                      {intl.get(`${modelPrompt}.description`).d('解决方法描述')}：
                    </span>
                    <span style={textStyle}>
                      <Tooltip title={description}>{description}</Tooltip>
                    </span>
                  </Row>
                  <Row className={styles['task-item-content-middle-line']}>
                    <span style={labelStyle}>
                      {intl.get(`${modelPrompt}.remark`).d('记录备注')}：
                    </span>
                    <span style={textStyle}>
                      <Tooltip title={remark}>{remark}</Tooltip>
                    </span>
                  </Row>
                </Col>
                <Col span={12}>
                  {basicTypeCodeList.map(code => {
                    let i = null;
                    if (data[`${code.toLowerCase()}HierarchyName`]) {
                      i = (
                        <Row className={styles['task-item-content-middle-line']}>
                          <span style={labelStyle}>
                            {intl
                              .get(`${modelPrompt}.riskCodeMeaning`)
                              .d(data[`${code.toLowerCase()}HierarchyName`])}
                            ：
                          </span>
                          <span style={textStyle}>
                            <Tooltip title={data[`${code.toLowerCase()}CodeMeaning`]}>
                              {data[`${code.toLowerCase()}CodeMeaning`]}
                            </Tooltip>
                          </span>
                        </Row>
                      );
                    }
                    return i;
                  })}
                </Col>
              </Row>
            </div>
          </div>
          {!viewFlag && (
            <div className={styles['task-item-operate']}>
              <div className={styles['task-item-operate-top']}>
                {isEndFlag ? null : (
                  <React.Fragment>
                    <Icon type="edit-o" style={{ verticalAlign: 'text-top' }} />
                    &nbsp;
                    <span onClick={() => this.handleEditLine(data, true)}>
                      {intl.get(`hzero.common.button.edit`).d('编辑')}
                    </span>
                  </React.Fragment>
                )}
              </div>
              <div className={styles['task-item-operate-bottom']}>
                {isEndFlag ? null : (
                  <React.Fragment>
                    <Popconfirm
                      title="是否删除该记录?"
                      placement="topRight"
                      onConfirm={() => this.handleDeleteLine(data)}
                      okText="是"
                      cancelText="否"
                    >
                      <Icon type="delete" />
                      &nbsp;<span>{intl.get(`hzero.common.button.delete`).d('删除')}</span>
                    </Popconfirm>
                  </React.Fragment>
                )}
              </div>
            </div>
          )}
        </div>
        <div className={styles['task-item-file']}>
          <FileUpload
            employeeId={employeeId}
            fileUploadLogList={[
              {
                moduleName: 'amtc-malfunction',
                moduleId: fileModuleId,
                attachmentUUID: `halm-amtc-malfunction-${fileModuleId}`,
                bucketName: 'amtc-malfunction',
              },
            ]}
            uploadParams={{
              moduleId: fileModuleId,
              moduleName: 'amtc-malfunction',
              bucketName: 'amtc-malfunction',
              attachmentUuid: `halm-amtc-malfunction-${fileModuleId}`,
              collectionCode: 'MALFUNCTION', // 只在alm文件日志分区使用，文件服务未使用
              description: 'PC端故障附件',
            }}
            showDelete={!isEndFlag}
          />
        </div>
      </div>
    );
  }

  render() {
    const { loading, list } = this.state;
    const { isEndFlag, woDescAndLabel, disableOptionFlag } = this.props;

    const bgColor = !isEndFlag ? '#F3F8FF' : '#F8F8F8';
    const bdColor = !isEndFlag ? '#3889FF' : '#D5DAE0';
    return (
      <React.Fragment>
        {/* disableOptionFlag：审批表单仅查看顶层界面，禁止任何操作 */}
        {!disableOptionFlag && (
          <div
            style={{
              backgroundColor: bgColor,
              border: `1px dashed ${bdColor}`,
              borderRadius: 8,
              lineHeight: '40px',
              textAlign: 'center',
              marginBottom: 16,
            }}
            onClick={e => {
              if (!isEndFlag) {
                this.handleCreateWoMalfunction();
                e.stopPropagation();
              }
            }}
          >
            <Icon type="add" style={{ fontSize: '12px', color: bdColor }}>
              &nbsp;{intl.get(`${viewPrompt}.button.create`).d('新增 故障')}
            </Icon>
          </div>
        )}
        <Spin spinning={loading}>
          {list && list.length > 0 && <div className={styles['task-title']}>{woDescAndLabel}</div>}
          {list.map((i, index) => this.renderItem(i, index))}
        </Spin>
      </React.Fragment>
    );
  }
}
export default WoMalfunctionList;
