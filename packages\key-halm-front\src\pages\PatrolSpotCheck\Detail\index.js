/**
 * 点巡检单
 * @date 2021/10/08
 * <AUTHOR>
 * @copyright Copyright (c) 2021,Hand
 */
import React, { Component } from 'react';
import { Button, DataSet, Modal } from 'choerodon-ui/pro';
import { isUndefined } from 'lodash';
import { Bind } from 'lodash-decorators';
import { Header } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { buttonsRender } from 'alm/utils/renderer';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import PatrolLayout from 'alm/components/PatrolLayout';
import { handleOpenWrdModal } from 'alm/pages/PatrolSpotCheck/utils/assignUtils';
import CheckFileUploadButton from 'alm/pages/WorkOrder/Detail/FileUploadButton/check';

import getLangs from '../Langs';
import { detailDs } from '../Stores/detailDs';
import { receiptCheckDs, objSearchDs, receiptObjDs } from '../Stores/detailLineDs';

import {
  saveApi,
  ignoreChecklistService,
  updateWoStatusService,
  updateWoopStatusService,
  queryWoopDetailService,
  queryPSCButtonsService,
  queryPSCLineButtonsService,
} from '../api';

import HeaderInfo from './HeaderInfo';
import LeftSide from './LeftSide';
import MainContent from './MainContent';

@formatterCollections({
  code: [
    'alm.common',
    'alm.component',
    'aori.patrolSpotCheck',
    'alm.checklistEditModal',
    'alm.abnormalCkList',
  ],
})
@observer
export default class PatrolSpotCheckDetail extends Component {
  constructor(props) {
    super(props);
    const {
      match: { params },
      location: { state },
    } = props;
    const { id } = params;

    this.state = {
      isNew: isUndefined(id),
      isEdit: state && state.isEdit ? state.isEdit : false,
      queryDeatilLoading: false,
      saveLoading: false,
      saveAndSubLoading: false,
      disableTimeByStatus: false, // 计划开始时间等相关四个控件在 工作完成、取消、无法执行 三种状态下为不可编辑
      btnsLoadingCompleteFlag: false, // 页面按钮加载完成flag
      woopDetail: {}, // 任务详情
    };

    this.detailDs = new DataSet(detailDs(isUndefined(id)));
    this.receiptCheckDs = new DataSet(receiptCheckDs());
    this.objSearchDs = new DataSet(objSearchDs());
    this.receiptObjDs = new DataSet(receiptObjDs());
    this.checkDs = new DataSet(receiptCheckDs());

    this.mainRef = React.createRef();
  }

  componentDidMount() {
    const { history } = this.props;
    const { isNew } = this.state;

    if (!isNew) {
      this.init();
    }

    // 清除外部传入的isEdit，保持右键刷新到详情状态
    history.replace({ ...history.location, state: { ...history.location?.state, isEdit: false } });
  }

  componentDidUpdate(preProps) {
    this.handleCompUpdate(preProps);
  }

  @Bind
  init() {
    const {
      match: { params },
    } = this.props;
    const { id } = params;
    this.detailDs.setQueryParameter('id', id);
    this.handleQueryDetail();
  }

  @Bind
  handleQueryDetail() {
    this.setState({
      queryDeatilLoading: true,
      btnsLoadingCompleteFlag: false,
    });
    return this.detailDs.query().then(() => {
      const {
        woId,
        woStatus,
        woType,
        spotCheckObjType,
        spotCheckLineIds,
      } = this.detailDs.current.toData();
      const isWo = spotCheckObjType === 'CHECKROUTE';
      const queryButoons = id =>
        isWo ? queryPSCButtonsService(id) : queryPSCLineButtonsService(id);
      this.detailDs.current.set('woTypeLov', woType);
      Promise.all([
        queryButoons(isWo ? woId : spotCheckLineIds[0]),
        isWo ? null : queryWoopDetailService(spotCheckLineIds[0]),
      ]).then(result => {
        this.statusBtns = buttonsRender(
          'PSC',
          result[0]?.buttonMap || {},
          this.changeStatusBtnClick
        );

        this.setState({
          woopDetail: isWo ? {} : result[1],
          queryDeatilLoading: false,
          btnsLoadingCompleteFlag: true,
          disableTimeByStatus: ['COMPLETED', 'CANCELED', 'UNABLE'].includes(woStatus),
        });
      });
    });
  }

  @Bind
  handleCompUpdate(preProps) {
    const {
      match: {
        params: { id },
      },
      location: { state },
    } = preProps;
    if (!this.state.isNew && id !== this.props.match.params.id) {
      this.init();
      this.setState({
        isEdit: state && state.isEdit ? state.isEdit : false,
      });
    }
  }

  /**
   * 编辑
   */
  @Bind()
  handleEdit() {
    const { isEdit } = this.state;
    // 如果是从编辑切换到查看状态 要重置ds 清空改动
    if (isEdit) {
      this.detailDs.reset();
      this.init();
    }
    this.setState({ isEdit: !isEdit });
  }

  @Bind()
  getDetail() {
    const data = this.detailDs.current.toData();
    const { pointId, routeId, ...newData } = data;
    if (data.pointId) {
      newData.spotCheckObjId = pointId;
    } else if (data.routeId) {
      newData.spotCheckObjId = routeId;
    }
    return newData;
  }

  @Bind()
  async saveMethod(successCallBack) {
    const result = await this.detailDs.validate();
    if (result) {
      const data = this.getDetail();
      this.setState({
        saveLoading: true,
      });
      saveApi(this.state.isNew, data).then(res => {
        this.setState({
          saveLoading: false,
        });
        if (res && !res.failed) {
          successCallBack(res);
        } else {
          notification.error({
            message: res?.message,
          });
        }
      });
    }
  }

  @Bind()
  handleSave(goToEdit) {
    this.saveMethod(res => {
      this.setState({
        isEdit: false,
      });
      this.saveAndSubmitCallback(goToEdit, res.woId);
    });
  }

  @Bind()
  saveAndSubmitCallback(goToEdit = false, woId) {
    const { isNew } = this.state;
    if (isNew) {
      this.props.history.push({
        pathname: `/aori/patrol-spot-check/detail/${woId}`,
        state: { isEdit: goToEdit },
      });
    } else {
      this.handleQueryDetail();
    }
  }

  @Bind()
  handleNext() {
    this.handleSave(true);
  }

  // 新建 编辑时 都可以保存并提交 提交完之后再跳转到明细界面
  @Bind()
  handleSaveAndSubmit() {
    this.saveMethod(res => {
      this.handleSubmit(res);
    });
  }

  @Bind()
  handleSubmit(data) {
    const { woId, _token, objectVersionNumber } = data;
    this.setState({
      saveAndSubLoading: true,
    });
    updateWoStatusService({ url: 'submit', woId, _token, objectVersionNumber }).then(res => {
      this.setState({
        saveAndSubLoading: false,
        isEdit: false,
      });
      if (res && res.failed) {
        notification.error({
          message: res?.message,
        });
      }
      const _woId = res && res.failed ? woId : res.woId; // 提交失败的话 用保存时返回的id
      this.saveAndSubmitCallback(false, _woId);
    });
  }

  /**
   * 状态修改按钮点击事件
   */
  @Bind()
  changeStatusBtnClick(url, params) {
    const woDetail = this.detailDs.current?.toData() || {};
    const { spotCheckObjType } = woDetail;
    const isWo = spotCheckObjType === 'CHECKROUTE';
    switch (url) {
      case 'assign':
        // 派工弹窗显示
        handleOpenWrdModal({
          woDetail,
          type: 'WO',
          wrdSuccCallback: this.handleWhenStatusOpSucc,
        });
        break;
      case 'cancel':
        Modal.confirm({
          title: isWo ? getLangs('CANCEL_WO') : getLangs('CANCEL_WOOP'),
        }).then(button => {
          if (button === 'ok') {
            this.changeWoStatus(url, params);
          }
        });
        break;
      default:
        this.changeWoStatus(url, params);
        break;
    }
  }

  // 修改工单状态
  @Bind()
  changeWoStatus(url, params) {
    const { woopDetail } = this.state;
    const {
      woopId,
      woopStatus,
      _token: woopToken,
      objectVersionNumber: woopObjectVersionNumber,
    } = woopDetail;
    const { woId, _token, spotCheckObjType, objectVersionNumber } = this.detailDs.current.toData();
    const isWo = spotCheckObjType === 'CHECKROUTE';
    // 特殊：拟定状态的点位，提交和取消时走工单的状态变更
    const updateStatus = _params =>
      isWo || (!isWo && woopStatus === 'DRAFT' && ['submit', 'cancel'].includes(url))
        ? updateWoStatusService({
            url,
            woId,
            _token,
            objectVersionNumber,
            ..._params,
          })
        : updateWoopStatusService({
            url,
            woopId,
            _token: woopToken,
            objectVersionNumber: woopObjectVersionNumber,
            ..._params,
          });

    this.setState({
      queryDeatilLoading: true,
    });

    updateStatus(params).then(res => {
      this.setState({
        queryDeatilLoading: false,
      });
      this.handleException(res);
    });
  }

  @Bind()
  handleException(res) {
    const { woId } = this.detailDs.current.toData();
    if (res && res.failed) {
      if (
        [
          'alm.error.ori.all_wo_checklist_incomplete',
          'alm.error.ori.all_woop_checklist_incomplete',
          'alm.error.ori.woop_checklist_incomplete',
        ].includes(res.code)
      ) {
        const isWo = res.code === 'alm.error.ori.all_wo_checklist_incomplete';
        Modal.confirm({
          title: getLangs('NOTICE'),
          children: res.message,
          okText: getLangs('YES'),
          cancelText: getLangs('NO'),
          onOk: () => {
            const ignoreType = isWo ? 'WO_CHECK' : 'WOOP_CHECK';
            this.handleIgnoreChecklist({ ignoreType, woId });
          },
          onCancel: () => {
            this.handleWhenStatusOpSucc();
          },
        });
      } else if (['alm.error.ori.pre_construction_inspection'].includes(res.code)) {
        Modal.confirm({
          title: res.message,
          onOk: () => {
            this.mainRef?.current?.handleTabsChange('checklistsTab');
          },
        });
      } else {
        notification.error({ message: res.message });
      }
    } else {
      notification.success();
      this.handleWhenStatusOpSucc();
    }
  }

  //  状态按钮操作成功的回调
  @Bind()
  handleWhenStatusOpSucc() {
    this.handleQueryDetail();
    // 刷新任务（点巡检对象）数据
    this.receiptObjDs.query();
  }

  @Bind()
  handleIgnoreChecklist(params) {
    ignoreChecklistService(params).then(res => {
      if (res && res.failed) {
        notification.error({ message: res.message });
      } else {
        //  继续完工
        this.changeWoStatus('complete', params.woId);
      }
    });
  }

  get btns() {
    const { isNew, isEdit, saveLoading, saveAndSubLoading, btnsLoadingCompleteFlag } = this.state;
    const detail = this.detailDs.current?.toData() || {};
    const { woId, woopId, woStatus } = detail;
    const isNewOrEdit = isNew || isEdit;
    // 新建 或者 拟定（状态为拟定） 时显示 保存并提交
    const showSaveAndSub = isNew || (isEdit && woStatus === 'DRAFT');
    // 仅 拟定 需改派状态才显示 编辑 按钮
    const showEdit = !isNewOrEdit && ['DRAFT', 'WRD'].includes(woStatus);

    const FileUploadButtonProps = {
      type: 'CHECK_BUTTON',
      showContentFlag: true,
      woId,
      icon: 'link',
      moduleId: woopId,
      parentTypeCode: 'WO_CHECK',
      uploadButtonName: {
        code: 'attachmentManagement',
        name: '附件管理',
      },
    };

    return [
      <Button
        icon="save"
        color="primary"
        key="save"
        hidden={!isEdit}
        style={{ marginLeft: 8 }}
        onClick={this.handleSave}
        loading={saveLoading}
        waitType="throttle"
        wait={1000}
      >
        {getLangs('SAVE')}
      </Button>,
      <Button
        key="next"
        hidden={!isNew}
        color="primary"
        onClick={this.handleNext}
        loading={saveLoading}
        waitType="throttle"
        wait={1000}
      >
        {getLangs('NEXT')}
      </Button>,
      <Button
        key="saveAndsubmit"
        hidden={!showSaveAndSub}
        onClick={this.handleSaveAndSubmit}
        loading={saveAndSubLoading}
        waitType="throttle"
        wait={1000}
      >
        {getLangs('SAVE_AND_SUBMIT')}
      </Button>,
      <Button
        icon="close"
        key="close"
        onClick={this.handleEdit}
        hidden={isNew || !isEdit}
        style={{ marginLeft: 8 }}
      >
        {getLangs('CLOSE')}
      </Button>,
      <Button color="primary" onClick={this.handleEdit} key="edit" hidden={!showEdit}>
        {getLangs('EDIT')}
      </Button>,
      !isEdit && btnsLoadingCompleteFlag ? [...this.statusBtns] : null,
      <CheckFileUploadButton {...FileUploadButtonProps} />,
    ];
  }

  render() {
    const {
      match: {
        params: { id },
      },
      history,
    } = this.props;
    const { isNew, isEdit, woopDetail, queryDeatilLoading, disableTimeByStatus } = this.state;
    const { woopStatus, woopStatusMeaning } = woopDetail;
    const detail = this.detailDs.current?.toData() || {};
    const { woStatus, woStatusMeaning, spotCheckObjType } = detail;
    const isWo = spotCheckObjType === 'CHECKROUTE';
    const HeaderInfoProps = {
      detail: {
        ...detail,
        status: isWo ? woStatus : woopStatus,
        statusMeaning: isWo ? woStatusMeaning : woopStatusMeaning,
      },
    };
    const leftSideProps = {
      detail,
      queryDeatilLoading,
    };
    const mainProps = {
      isNew,
      isEdit,
      detail,
      woopStatus,
      disableTimeByStatus,
      history,
      currentId: id,
      detailDs: this.detailDs,
      receiptCheckDs: this.receiptCheckDs,
      objSearchDs: this.objSearchDs,
      receiptObjDs: this.receiptObjDs,
      checkDs: this.checkDs,
      onQueryDetail: this.handleQueryDetail,
      onChangeWoStatus: this.changeWoStatus,
    };
    const layoutProps = {
      isNew,
      editFlag: isEdit,
      queryDeatilLoading,
      header: (
        <Header title={getLangs('TITLE')} backPath="/aori/patrol-spot-check/list">
          {this.btns}
        </Header>
      ),
      leftTitle: getLangs('PANEL_BASE_INFO'),
      leftContent: <LeftSide {...leftSideProps} />,
      rightTop: <HeaderInfo {...HeaderInfoProps} />,
      rightContent: <MainContent {...mainProps} ref={this.mainRef} key={id} />,
    };

    return <PatrolLayout {...layoutProps} />;
  }
}
