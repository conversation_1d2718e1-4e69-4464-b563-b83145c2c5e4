/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-15 14:17:05
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-01 14:28:42
 * @FilePath: \institute-front\packages\key-hmes-front\src\routes\workshop\InspectionMonitorBoard\index.module.less
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
:global(html) {
  /* 1rem = 10px on a 1920px screen */
  font-size: calc(100vw / 192);
}

.boardContainer {
  background-color: #0d1224;
  background-image: url(../../assets/IncomingInspectionDashboard/bac.png);
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;

  // 全屏时的等比缩放
  &:fullscreen,
  &:-webkit-full-screen,
  &:-moz-full-screen,
  &:-ms-fullscreen {
    // 使用transform scale实现等比缩放
    transform-origin: center center;

    // 设置固定的设计稿尺寸
    width: 1920px;
    height: 1080px;

    // 居中显示
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -960px; // width / 2
    margin-top: -540px;  // height / 2

    // 防止内容溢出
    overflow: hidden;
    z-index: 9999;

    // 根据屏幕尺寸应用不同的缩放比例
    @media screen and (aspect-ratio: 16/9) {
      transform: scale(calc(100vw / 1920px));
    }

    @media screen and (min-aspect-ratio: 16/9) {
      transform: scale(calc(100vh / 1080px));
    }

    @media screen and (max-aspect-ratio: 16/9) {
      transform: scale(calc(100vw / 1920px));
    }
  }

  // 简化版本 - 使用zoom属性（更兼容）
  &.simple-fullscreen {
    // 使用zoom属性进行缩放
    zoom: calc(100vw / 1920px);

    // 居中显示
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    // 设置固定尺寸
    width: 1920px;
    height: 1080px;

    // 防止溢出
    overflow: hidden;
    z-index: 9999;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  position: absolute;
  height: 6rem;
  width: 100%;
  padding: 0 3rem;
  box-sizing: border-box;
  top: 0;
  left: 0;
}

.headerLeft,
.headerRight {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.headerRight {
  justify-content: flex-end;
  margin-right: 12rem;
}

.datePickerWrapper {
  background-image: url(../../assets/IncomingInspectionDashboard/time_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  width: 18rem;
  height: 3rem;
  position: relative;
  overflow: hidden;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 86% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-shadow: none !important;
      outline: none !important;
    }

    .c7n-pro-calendar-picker {
      background-color: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 100% !important;
      color: #c3e1e9 !important;
      font-size: 1.4rem !important;
      padding: 0 1.5rem 0 4.6rem !important;
      display: flex !important;
      align-items: center !important;

      input {
        background: transparent !important;
        border: none !important;
        color: #b9d4ff !important;
        font-size: 1.4rem !important;
        width: 100% !important;
        text-align: left !important;
        box-shadow: none !important;
        outline: none !important;
        &::placeholder {
          color: #b9d4ff !important;
          opacity: 1;
        }
        &:focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
        }
      }
    }

    .c7n-pro-calendar-picker-inner {
      background: transparent !important;
      width: 100% !important;
      height: 100% !important;
    }

    .c7n-pro-calendar-picker-suffix .icon {
      color: #b9d4ff !important;
    }

    .icon-date_range {
      color: #c3e1e9 !important;
    }
  }
}

// 全局样式，确保自定义时间选择器正确显示
:global(.custom-date-picker) {
  .c7n-pro-calendar-picker-wrapper {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
  }

  .c7n-pro-calendar-cell-inner {
    width: 100% !important;
    color: #fff !important;
    height: 100% !important;
    font-size: 12px;
  }

  .c7n-pro-calendar-picker {
    background: transparent !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
  }
}

// 确保弹窗样式正确应用 - 使用更高优先级的选择器
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup),
:global(.c7n-pro-popup.custom-datepicker-popup),
:global(.c7n-pro-calendar-picker-popup[class*='custom-datepicker-popup']) {
  background-color: #0c1a3e !important;
  border: 1px solid #1a3a8b !important;
  border-radius: 4px !important;
  padding: 1rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
  min-width: 360px !important;
  width: 360px !important;
  max-width: none !important;
  min-height: 420px !important;
  height: auto !important;
  overflow: visible !important;
  z-index: 9999 !important;

  .c7n-pro-calendar-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #fff !important;
    border-bottom: 1px solid #1a3a8b !important;
    background: transparent !important;
    padding: 12px 16px !important;
    align-items: center !important;
    justify-content: space-between !important;
    min-height: 48px !important;

    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #ffffff !important;
      background: rgba(26, 58, 139, 0.3) !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 6px 12px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      cursor: pointer !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: rgba(26, 58, 139, 0.5) !important;
        border-color: #4a90e2 !important;
        color: #fff !important;
      }
    }

    button {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #b9d4ff !important;
      background: rgba(26, 58, 139, 0.3) !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 8px 12px !important;
      cursor: pointer !important;
      transition: all 0.3s ease !important;
      font-size: 16px !important;
      min-width: 36px !important;
      height: 36px !important;
      align-items: center !important;
      justify-content: center !important;

      &:hover {
        color: #fff !important;
        background-color: #1a3a8b !important;
        border-color: #4a90e2 !important;
        transform: scale(1.05) !important;
      }
    }
  }

  .c7n-pro-calendar-body {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: transparent !important;
    width: 100% !important;
    min-height: 240px !important;
    height: auto !important;
    overflow: visible !important;
    padding: 8px !important;

    table {
      display: table !important;
      visibility: visible !important;
      opacity: 1 !important;
      width: 100% !important;
      table-layout: fixed !important;
      border-collapse: separate !important;
      border-spacing: 2px !important;
      height: auto !important;
      min-height: 200px !important;

      thead {
        display: table-header-group !important;
        visibility: visible !important;
        opacity: 1 !important;

        th {
          display: table-cell !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #b9d4ff !important;
          background: transparent !important;
          border: none !important;
          padding: 12px 8px !important;
          text-align: center !important;
          font-weight: bold !important;
          font-size: 14px !important;
          width: 14.28% !important;
          height: 40px !important;
        }
      }

      tbody {
        display: table-row-group !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        overflow: visible !important;
      }

      tbody tr {
        display: table-row !important;
        visibility: visible !important;
        opacity: 1 !important;
      }

      // 使用具体的类名选择器
      tbody td.c7n-pro-calendar-cell {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
        border: none !important;
        padding: 2px !important;
        text-align: center !important;
        width: 14.28% !important;
        height: 34px !important;
        vertical-align: middle !important;
        position: relative !important;
        box-sizing: border-box !important;
        .c7n-pro-calendar-cell-inner {
          display: inline-block !important;
          visibility: visible !important;
          opacity: 1 !important;
          width: 100% !important;
          height: 100% !important;
          color: #fff !important;
          font-size: 12px !important;
          line-height: 30px !important;
          text-align: center !important;
        }

        // 确保单元格内容显示
        &::before,
        &::after {
          display: none !important;
        }

        // 日期内容显示 - 基础样式
        .c7n-pro-calendar-date {
          display: inline-block !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #ffffff !important;
          background: rgba(26, 58, 139, 0.6) !important;
          border: 1px solid rgba(185, 212, 255, 0.5) !important;
          border-radius: 4px !important;
          padding: 0 !important;
          cursor: pointer !important;
          transition: all 0.2s ease !important;
          box-shadow: none !important;
          width: 30px !important;
          height: 30px !important;
          line-height: 30px !important;
          text-align: center !important;
          vertical-align: middle !important;
          font-size: 12px !important;
          font-weight: normal !important;
          margin: 1px !important;
          overflow: hidden !important;
        }

        // 如果没有.c7n-pro-calendar-date元素，直接在td上显示内容
        &:not(:has(.c7n-pro-calendar-date)) {
          color: #ffffff !important;
          background: rgba(26, 58, 139, 0.6) !important;
          border: 1px solid rgba(185, 212, 255, 0.5) !important;
          border-radius: 4px !important;
          cursor: pointer !important;
          font-size: 12px !important;
          font-weight: normal !important;
        }

        &:hover {
          background-color: #2a4a9b !important;
          color: #ffffff !important;
          border-color: #4a90e2 !important;
          transform: none !important;
          box-shadow: 0 0 4px rgba(74, 144, 226, 0.3) !important;
        }
      }

      // 选中状态 - 最高优先级
      &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
        background-color: #4a90e2 !important;
        color: #ffffff !important;
        border-color: #4a90e2 !important;
        box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
        font-weight: bold !important;
        width: 30px !important;
        height: 30px !important;
        line-height: 30px !important;
      }

      // 今天日期 - 仅当不是选中状态时
      &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
        border-color: #4a90e2 !important;
        color: #ffffff !important;
        background: rgba(74, 144, 226, 0.4) !important;
        font-weight: bold !important;
        width: 30px !important;
        height: 30px !important;
        line-height: 30px !important;
      }

      // 本月日期 - 确保可见
      &.c7n-pro-calendar-in-range-cell .c7n-pro-calendar-date,
      &:not(.c7n-pro-calendar-last-month-cell):not(.c7n-pro-calendar-next-month-btn-day)
        .c7n-pro-calendar-date {
        color: #ffffff !important;
        background: rgba(26, 58, 139, 0.6) !important;
        border-color: rgba(185, 212, 255, 0.5) !important;
      }

      // 上月和下月日期 - 灰色显示
      &.c7n-pro-calendar-last-month-cell .c7n-pro-calendar-date,
      &.c7n-pro-calendar-next-month-btn-day .c7n-pro-calendar-date {
        color: #888 !important;
        background: rgba(26, 58, 139, 0.2) !important;
        border-color: rgba(185, 212, 255, 0.2) !important;
      }
    }
  }
}

.c7n-pro-calendar-footer {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  border-top: 1px solid #1a3a8b !important;
  background-color: transparent !important;
  padding: 8px 0 !important;
  text-align: right !important;

  .c7n-pro-calendar-now-btn,
  .c7n-pro-calendar-ok-btn {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #b9d4ff !important;
    background: transparent !important;
    border: 1px solid #1a3a8b !important;
    border-radius: 2px !important;
    padding: 4px 12px !important;
    margin-left: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;

    &:hover {
      color: #fff !important;
      background-color: #1a3a8b !important;
      border-color: #4a90e2 !important;
    }
  }
}

// 确保弹窗内容完整显示
.c7n-pro-calendar {
  width: 100% !important;
  min-width: 280px !important;
  background: transparent !important;
}

// 确保日历面板正确显示
.c7n-pro-calendar-panel {
  width: 100% !important;
  background: transparent !important;
}

// 修复可能的容器问题
.c7n-pro-calendar-picker-panel {
  width: 100% !important;
  background: transparent !important;
}

// 额外的全局样式确保弹窗正确显示
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup),
:global(.c7n-pro-popup.custom-datepicker-popup),
:global(.c7n-pro-calendar-picker-popup[class*='custom-datepicker-popup']) {
  .c7n-pro-calendar-picker-panel-container {
    width: 100% !important;
  }

  // 确保所有日历单元格内容都显示
  .c7n-pro-calendar-cell-inner {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100% !important;
    color: #fff !important;
    font-size: 12px !important;
    line-height: 30px !important;
    text-align: center !important;
  }

  .c7n-pro-calendar-picker-panel {
    width: 100% !important;
    min-width: 280px !important;
  }

  // 确保日历表格每行7列正确显示
  .c7n-pro-calendar-tbody {
    height: auto !important;
    overflow: visible !important;

    tr {
      display: table-row !important;
      width: 100% !important;
      height: 32px !important;

      td {
        display: table-cell !important;
        width: 14.28% !important;
        height: 32px !important;
        vertical-align: middle !important;
      }
    }
  }

  .c7n-pro-calendar-thead tr {
    display: table-row !important;
    width: 100% !important;

    th {
      display: table-cell !important;
      width: 14.28% !important;
    }
  }

  // 强制显示所有日期行
  .c7n-pro-calendar-date-panel {
    height: auto !important;
    min-height: 200px !important;
    overflow: visible !important;
  }

  // 确保日历容器有足够高度
  .c7n-pro-calendar-picker-panel-container {
    height: auto !important;
    min-height: 300px !important;
    overflow: visible !important;
  }
}

// 通用的DatePicker弹窗样式覆盖，确保在任何情况下都能正确显示
:global(.c7n-pro-popup) {
  &.custom-datepicker-popup {
    background-color: #0c1a3e !important;
    border: 1px solid #1a3a8b !important;
    border-radius: 4px !important;
    padding: 1rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
    min-width: 360px !important;
    width: 360px !important;
    max-width: none !important;
    min-height: 420px !important;
    height: auto !important;
    overflow: visible !important;
    z-index: 9999 !important;
  }
}

// 强制覆盖所有可能的DatePicker弹窗样式
:global {
  // 针对所有可能的弹窗类名
  .c7n-pro-popup,
  .c7n-pro-calendar-picker-popup,
  .c7n-pro-calendar-popup {
    &[class*='custom-datepicker'] {
      background-color: #0c1a3e !important;
      border: 1px solid #1a3a8b !important;
      border-radius: 4px !important;
      padding: 1rem !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
      min-width: 320px !important;
      width: 320px !important;
      max-width: none !important;
      min-height: 350px !important;
      height: auto !important;
      overflow: visible !important;
      z-index: 9999 !important;

      // 确保内容区域正确显示
      .c7n-pro-calendar,
      .c7n-pro-calendar-picker-panel {
        background: transparent !important;
        width: 100% !important;
        height: auto !important;
        min-height: 320px !important;
        padding: 8px !important;
      }

      // 确保日历头部样式
      .c7n-pro-calendar-header {
        padding: 12px 16px !important;
        min-height: 48px !important;
        border-bottom: 1px solid #1a3a8b !important;

        button {
          background: rgba(26, 58, 139, 0.3) !important;
          border: 1px solid rgba(185, 212, 255, 0.3) !important;
          color: #b9d4ff !important;
          border-radius: 4px !important;
          padding: 8px 12px !important;
          min-width: 36px !important;
          height: 36px !important;

          &:hover {
            background-color: #1a3a8b !important;
            border-color: #4a90e2 !important;
            color: #fff !important;
          }
        }
      }

      // 确保日历主体样式
      .c7n-pro-calendar-body {
        padding: 8px !important;
        min-height: 240px !important;

        table {
          border-spacing: 2px !important;
          min-height: 200px !important;

          thead th {
            padding: 12px 8px !important;
            height: 40px !important;
            font-size: 14px !important;
            font-weight: bold !important;
            color: #b9d4ff !important;
          }

          tbody td {
            padding: 2px !important;
            height: 34px !important;
            box-sizing: border-box !important;

            .c7n-pro-calendar-date {
              width: 30px !important;
              height: 30px !important;
              background: rgba(26, 58, 139, 0.6) !important;
              border: 1px solid rgba(185, 212, 255, 0.5) !important;
              color: #ffffff !important;
              border-radius: 4px !important;
              font-size: 12px !important;
              font-weight: normal !important;
              line-height: 30px !important;
              display: inline-block !important;
              text-align: center !important;
              margin: 1px !important;
              overflow: hidden !important;

              &:hover {
                background-color: #2a4a9b !important;
                border-color: #4a90e2 !important;
                color: #ffffff !important;
                transform: none !important;
              }
            }
          }
        }
      }
    }
  }
}

// 修复可能的容器高度限制
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup),
:global(.c7n-pro-popup.custom-datepicker-popup),
:global(.c7n-pro-calendar-picker-popup[class*='custom-datepicker-popup']) {
  .c7n-pro-calendar {
    height: auto !important;
    min-height: 280px !important;

    .c7n-pro-calendar-panel {
      height: auto !important;
      min-height: 280px !important;

      .c7n-pro-calendar-body {
        height: auto !important;
        min-height: 200px !important;

        table {
          height: auto !important;

          tbody {
            height: auto !important;

            tr {
              height: 32px !important;
              display: table-row !important;
            }
          }
        }
      }
    }
  }
}

// 额外的样式确保日历弹窗内容不被挤压
:global {
  .c7n-pro-calendar-picker-popup.custom-datepicker-popup,
  .c7n-pro-popup.custom-datepicker-popup {
    // 确保弹窗有足够的内边距
    .c7n-pro-calendar-picker-panel-container {
      padding: 0 !important;
      width: 100% !important;
      height: auto !important;
      min-height: 360px !important;
    }

    // 确保日历面板不被压缩
    .c7n-pro-calendar-picker-panel {
      width: 100% !important;
      height: auto !important;
      min-height: 360px !important;
      overflow: visible !important;
    }

    // 确保日历容器有足够空间
    .c7n-pro-calendar {
      width: 100% !important;
      height: auto !important;
      min-height: 320px !important;
      overflow: visible !important;
    }

    // 防止内容溢出隐藏
    * {
      box-sizing: border-box !important;
    }

    // 强制修复本月日期显示
    .c7n-pro-calendar-body {
      tbody {
        td {
          // 确保所有日期都可见
          .c7n-pro-calendar-date {
            color: #ffffff !important;
            background: rgba(26, 58, 139, 0.6) !important;
            border: 1px solid rgba(185, 212, 255, 0.5) !important;
          }

          // 本月日期特殊处理
          &:not(.c7n-pro-calendar-last-month-cell):not(.c7n-pro-calendar-next-month-btn-day) {
            .c7n-pro-calendar-date {
              color: #ffffff !important;
              background: rgba(26, 58, 139, 0.7) !important;
              border-color: rgba(185, 212, 255, 0.6) !important;
              font-weight: 500 !important;
            }
          }

          // 选中状态
          &.c7n-pro-calendar-selected {
            .c7n-pro-calendar-date {
              background-color: #4a90e2 !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
              font-weight: bold !important;
            }
          }

          // 今天日期 - 仅当不是选中状态时
          &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) {
            .c7n-pro-calendar-date {
              background: rgba(74, 144, 226, 0.5) !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
              font-weight: bold !important;
            }
          }

          // 悬停效果
          &:hover {
            .c7n-pro-calendar-date {
              background-color: #2a4a9b !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
            }
          }
        }
      }
    }
  }
}

// 最高优先级样式修复 - 确保日期显示正确
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  .c7n-pro-calendar-body {
    table {
      tbody {
        tr {
          td {
            .c7n-pro-calendar-date {
              // 强制覆盖所有可能的默认样式
              color: #ffffff !important;
              background-color: rgba(26, 58, 139, 0.6) !important;
              border: 1px solid rgba(185, 212, 255, 0.5) !important;
              width: 30px !important;
              height: 30px !important;
              line-height: 30px !important;
              text-align: center !important;
              display: inline-block !important;
              border-radius: 4px !important;
              font-size: 12px !important;
              margin: 1px !important;
              padding: 0 !important;
              box-sizing: border-box !important;
              overflow: hidden !important;
              vertical-align: middle !important;
            }

            // 悬停效果
            &:hover .c7n-pro-calendar-date,
            .c7n-pro-calendar-date:hover {
              background-color: #2a4a9b !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
            }

            // 选中状态 - 最高优先级，包括同时是今天的情况
            &.c7n-pro-calendar-selected .c7n-pro-calendar-date,
            &.c7n-pro-calendar-selected.c7n-pro-calendar-today .c7n-pro-calendar-date {
              background-color: #4a90e2 !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
              font-weight: bold !important;
              box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
            }

            // 今天日期 - 仅当不是选中状态时
            &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
              background-color: rgba(74, 144, 226, 0.5) !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
              font-weight: bold !important;
            }

            // 本月日期 - 确保可见
            &:not(.c7n-pro-calendar-last-month-cell):not(.c7n-pro-calendar-next-month-btn-day)
              .c7n-pro-calendar-date {
              color: #ffffff !important;
              background: rgba(26, 58, 139, 0.6) !important;
              border-color: rgba(185, 212, 255, 0.5) !important;
            }

            // 上月和下月日期 - 灰色显示
            &.c7n-pro-calendar-last-month-cell .c7n-pro-calendar-date,
            &.c7n-pro-calendar-next-month-btn-day .c7n-pro-calendar-date {
              color: #888 !important;
              background: rgba(26, 58, 139, 0.2) !important;
              border-color: rgba(185, 212, 255, 0.2) !important;
            }
          }
        }
      }
    }
  }
}

// 最终优先级覆盖 - 确保选中状态在所有情况下都正确显示
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  .c7n-pro-calendar-body {
    table {
      tbody {
        tr {
          td {
            // 选中状态 - 最终优先级，覆盖所有其他状态
            &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
              background-color: #4a90e2 !important;
              color: #ffffff !important;
              border-color: #4a90e2 !important;
              font-weight: bold !important;
              box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
            }
          }
        }
      }
    }
  }
}

.dateLabel {
  font-size: 1.4rem;
  color: #b9d4ff;
}

.titleGroup {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.title {
  background-image: url(../../assets/IncomingInspectionDashboard/header_title.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 30px;
  width: 404px;
}

.enTitle {
  font-size: 1.4rem;
  letter-spacing: 0.2rem;
  color: #fff;
  opacity: 0.7;
  padding-top: 5px;
}

.headerCenter {
  position: absolute;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 10;
}

.titleGradient {
  background: linear-gradient(#78fdf7 0%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.headerInfo {
  display: flex;
  align-items: center;
  gap: 3rem;
  z-index: 1;
  font-size: 1.6rem;
  width: 100%;
  justify-content: flex-end;
}

.timeWrapper {
  display: flex;
  flex-direction: row;
  align-items: end;
  gap: 1.5rem;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 10px 0;
}
.time {
  font-size: 2.2rem;
  color: #fff;
}
.date {
  font-size: 1.6rem;
  color: #fff;
}
.week {
  font-size: 1.6rem;
  color: #fff;
}

.mainContent {
  position: absolute;
  width: 100%;
  height: calc(100% - 9.5rem);
  top: 9.5rem;
  left: 0;
  padding: 0 2rem 2rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.topRow {
  display: flex;
  height: 56%;

  > .panel {
    flex: 1;
    min-width: 0;
  }

  > .panel:nth-child(2) {
    margin: 0 3rem 0 6rem;
  }
}

.bottomRow {
  display: grid;
  grid-template-columns: 4.5fr 4.7fr;
  gap: 4rem;
  height: 42%;
}

.panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  background: transparent;
  padding-top: 0;
}

.panelHeader {
  display: flex;
  align-items: center;
  height: 4.4rem;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-shrink: 0;
  padding: 1.3rem 0 0 6rem;
  .panelTitle {
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
    padding: 1.5rem 0;
    flex-shrink: 0;
    position: relative;
    display: flex;
    align-items: center;
    .title {
      background-image: url(../../assets/IncomingInspectionDashboard/header_title.png);
      background-size: 100% 100%;
      background-position: center;

      height: 40px;
      width: 40px;
    }
  }

  .panelExtra {
    display: flex;
    gap: 1rem;
    margin-top: 0.7rem;
  }
}

.assetButton {
  background-image: url(../../assets/IncomingInspectionDashboard/button_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-color: transparent;
  border: none;
  color: #00d1ff;
  padding: 0.3rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.4rem;
}

.button {
  background-color: transparent;
  border: 1px solid #00d1ff;
  color: #00d1ff;
  padding: 0.5rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.4rem;
  box-shadow: 0 0 5px #00d1ff;
  &.primary {
    background-color: #00d1ff;
    color: #fff;
    box-shadow: 0 0 5px #00d1ff, inset 0 0 5px rgba(255, 255, 255, 0.5);
  }
}

.panelBody {
  flex-grow: 1;
  padding: 0 1.5rem;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.chartPanelBody {
  padding: 0;
  margin-top: 4.4rem;
  position: relative;
}

.pieChartBackground {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  background-image: url(../../assets/IncomingInspectionDashboard/pie_bac.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.tableContainer {
  flex-grow: 1;
  overflow: hidden;
  margin-top: 1rem;
}

.customTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
}

.tableHeader,
.tableRow {
  display: grid;
  grid-template-columns: 1.5fr 1.5fr 1.5fr 1.5fr 1.2fr 1fr;
  text-align: center;
  padding: 1rem;
  align-items: center;
  gap: 2rem;
}
.tableHeader2,
.tableRow2 {
  display: grid;
  grid-template-columns: 0.5fr 1.5fr 1fr 1fr;
  text-align: center;
  padding: 1rem;
  align-items: center;
  gap: 2rem;
}

.tableHeader,
.tableHeader2 {
  background-image: url(../../assets/IncomingInspectionDashboard/table_title.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  font-weight: bold;
  color: #c3e1e9;
  font-size: 1.6rem;
  > span {
    white-space: nowrap;
  }
}

.tableBody,
.materialFilterModalBody .materialFilterListBody {
  &::-webkit-scrollbar {
    width: 1.2rem !important;
  }
  &::-webkit-scrollbar-track {
    background-color: #0b1a3e !important;
    border: 2px solid #6274a4 !important;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #869ecd !important;
    border-radius: 0.5rem !important;
    border: 3px solid transparent !important;
    background-clip: content-box !important;
  }
}

.tableBody {
  flex-grow: 1;
  overflow-y: auto;
  color: #b9d4ff;
  font-size: 1.6rem;
  scroll-behavior: smooth;
}

.tableRow,
.tableRow2 {
  border-bottom: 1px solid #16366b;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);

  &:nth-child(even) {
    background-color: transparent;
  }

  // 悬停效果
  &:hover {
    background-color: rgba(26, 58, 139, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.1);
  }

  // 行进入动画
  &.entering {
    opacity: 0;
    transform: translateY(20px);
    animation: rowFadeIn 0.5s ease forwards;
  }
}

.clickableLink {
  color: #00ffc5;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;

  &:hover {
    color: #00d4a3;
    text-decoration: underline;
  }
}
.activeRow {
  background-color: rgba(0, 132, 255, 0.3) !important;
}
.tableCell {
  white-space: nowrap;
  overflow: hidden;
  padding: 0 0.5rem;
  position: relative;
  max-width: 100%;
  min-width: 0;
}

.scrollingText {
  display: inline-block;
  animation: scrollText var(--scroll-duration, 8s) linear infinite alternate;
  will-change: transform;
  transform: translateX(0);
}

@keyframes scrollText {
  0% {
    transform: translateX(0);
  }
  15% {
    transform: translateX(0);
  }
  85% {
    transform: translateX(var(--scroll-amount, -100px));
  }
  100% {
    transform: translateX(var(--scroll-amount, -100px));
  }
}
.alignRight {
  text-align: right;
  padding-right: 2rem;
}

.loadingRow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  color: #b9d4ff;
  font-size: 1.2rem;
  opacity: 0.8;
  background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
  animation: loadingShimmer 2s ease-in-out infinite;

  span {
    animation: loadingPulse 1.5s ease-in-out infinite;
    position: relative;

    &::after {
      content: '...';
      animation: loadingDots 1.5s steps(4, end) infinite;
    }
  }
}

// 行淡入动画
@keyframes rowFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 加载文字脉动动画
@keyframes loadingPulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

// 加载背景闪烁动画
@keyframes loadingShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

// 加载点动画
@keyframes loadingDots {
  0%,
  20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%,
  100% {
    content: '...';
  }
}
.tableIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url(../../assets/IncomingInspectionDashboard/bottom_content_num.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 48px;
  height: 20px;
  margin: 0 8px;
}

:global(.filter-modal .c7n-pro-modal-content) {
  background: transparent !important;
  box-shadow: none !important;
}

.materialFilterModalContainer {
  color: #fff;
  display: flex;
  flex-direction: column;
  height: 52rem;
  width: 750px;
  box-sizing: border-box;
  margin-top: 5rem;
}

.materialFilterModalTitle {
  text-align: center;
  font-size: 24px;
  padding: 15px 0 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;

  .titleText {
    margin: 0 15px;
  }

  .titleDecorator {
    font-size: 20px;
    color: #4a90e2;
  }
}

.materialFilterModalBody {
  flex-grow: 1;
  padding: 0;
  overflow-y: auto;
}

.materialFilterList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.materialFilterListHeader {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 12px 30px;
  background-color: transparent;
  background-image: url(../../assets/IncomingInspectionDashboard/table_title.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-weight: bold;
  text-align: left;
  color: #c3e1e9;
  font-size: 16px;
  flex-shrink: 0;
}

.materialFilterListBody {
  flex-grow: 1;
  overflow-y: auto;
}

.materialFilterListRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 15px 30px;
  text-align: left;
  cursor: pointer;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
  font-size: 16px;
  color: #b9d4ff;

  &:nth-child(even) {
    background-color: rgba(0, 132, 255, 0.1);
  }

  &:hover {
    background-color: rgba(74, 144, 226, 0.3);
  }

  &.selected {
    background-color: #4a90e2;
    color: #fff;
  }

  &:last-child {
    border-bottom: none;
  }
}

.materialFilterModalFooter {
  padding: 20px 0 10px;
  text-align: right;
  flex-shrink: 0;
}

.modalButton {
  background-color: transparent;
  border: 1px solid #4a90e2;
  color: #4a90e2;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
  font-size: 14px;

  &:hover {
    background-color: #4a90e2;
    color: #fff;
  }

  &.primary {
    background-color: #4a90e2;
    color: #fff;
  }
}

.modalOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background-image: url(../../assets/IncomingInspectionDashboard/modal.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

// 使用您找到的具体类名修复日期单元格显示
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  // 针对具体的日期单元格类名
  td.c7n-pro-calendar-cell {
    display: table-cell !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 2px !important;
    text-align: center !important;
    width: 14.28% !important;
    height: 34px !important;
    vertical-align: middle !important;
    position: relative !important;
    box-sizing: border-box !important;

    // 基础样式 - 确保单元格有背景和边框
    color: #ffffff !important;
    background: rgba(26, 58, 139, 0.6) !important;
    border: 1px solid rgba(185, 212, 255, 0.5) !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: normal !important;
    line-height: 30px !important;

    // 新日期样式
    &.c7n-pro-calendar-new {
      color: #ffffff !important;
      background: rgba(26, 58, 139, 0.6) !important;
      border: 1px solid rgba(185, 212, 255, 0.5) !important;

      &:hover {
        background-color: #2a4a9b !important;
        border-color: #4a90e2 !important;
        color: #ffffff !important;
      }
    }

    // 选中状态 - 最高优先级
    &.c7n-pro-calendar-selected {
      background-color: #4a90e2 !important;
      color: #ffffff !important;
      border-color: #4a90e2 !important;
      font-weight: bold !important;
      box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
    }

    // 今天日期 - 仅当不是选中状态时
    &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) {
      background: rgba(74, 144, 226, 0.4) !important;
      color: #ffffff !important;
      border-color: #4a90e2 !important;
      font-weight: bold !important;
    }

    // 上月和下月日期
    &.c7n-pro-calendar-last-month-cell,
    &.c7n-pro-calendar-next-month-btn-day {
      color: #888 !important;
      background: rgba(26, 58, 139, 0.2) !important;
      border-color: rgba(185, 212, 255, 0.2) !important;
    }

    // 如果单元格内有.c7n-pro-calendar-date元素，确保它也正确显示
    .c7n-pro-calendar-date {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      width: 30px !important;
      height: 30px !important;
      line-height: 30px !important;
      text-align: center !important;
      border-radius: 4px !important;
      margin: 0 auto !important;
      color: inherit !important;
      background: inherit !important;
      border: inherit !important;
    }

    // 确保 cell-inner 元素也正确显示
    .c7n-pro-calendar-cell-inner {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      width: 100% !important;
      height: 100% !important;
      color: #fff !important;
      font-size: 12px !important;
      line-height: 30px !important;
      text-align: center !important;
    }
  }
}
