/**
 * ServiceCenter - 服务中心
 * @date: 2020/11/20
 * @author: lkj<<EMAIL>>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */
import React, { PureComponent } from 'react';
import { Tag, Menu, Icon } from 'choerodon-ui';
import { Lov, Table, DataSet, Dropdown, Modal, Button, DateTimePicker } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { isUndefined } from 'lodash';
import { connect } from 'dva';
import { Bind } from 'lodash-decorators';
import { routerRedux } from 'dva/router';
import { HALM_MTC, HALM_MDM } from 'alm/utils/config';
import IconSelect from 'alm/components/IconSelect';
import OrgPartnerLov from 'alm/components/OrgPartnerLov';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import moment from 'moment';
import queryString from 'querystring';
import withProps from 'utils/withProps';
import notification from 'utils/notification';

import { tableProps as priorityTableProps } from 'alm/components/PriorityLov/tableProps';
import { statusColors } from 'alm/utils/constants';
import { tableDS } from './Stores/ServiceCenterDS';
// import styles from './index.module.less';

const modalKey = Modal.key();
const organizationId = getCurrentOrganizationId();

const prompt = 'amtc.serviceApply';
const modelPrompt = 'amtc.serviceApply.model.serviceApply';
const viewPrompt = 'amtc.serviceApply.view.message';

const queryWoListUrl = `${HALM_MDM}/v1/${organizationId}/work-orders/lov`;
const querySubListUrl = `${HALM_MDM}/v1/${organizationId}/subcontract-requisition/lov`;
// 更新工单关联的sr数据
const updateSrUrl = `${HALM_MTC}/v1/${organizationId}/sr/update-by-source`;

const menuIconStyle = {
  width: 14,
  height: 14,
};

@formatterCollections({
  code: ['alm.common', 'alm.component', 'amtc.serviceApply'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    const priorityId = localStorage.getItem('priorityId');

    listDS.setQueryParameter('priorityId', priorityId);
    localStorage.removeItem('priorityId');
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
@connect(({ serviceApply }) => ({
  serviceApply,
  tenantId: getCurrentOrganizationId(),
}))
class ServiceCenter extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      // delButtDisabled: true, // 禁用删除按钮
      createWoButtonDisabled: true, // 禁用创建工作单按钮
      relateWoButtonDisabled: true, // 禁用关联工作单按钮
      currentStatus: null, // 当前状态
      currentMaintSiteId: null,
      currentAssetId: null,
      currentLocationId: null,
      exportParams: {}, // 导出组件的参数
    };
    props.listDS.addEventListener('load', this.initSelectStatus);
    props.listDS.addEventListener('select', this.handleSelect);
    props.listDS.addEventListener('unSelect', this.handleUnSelect);
    props.listDS.queryDataSet.addEventListener('reset', this.handleClearQuery);
  }

  componentDidMount() {
    const { dispatch, tenantId } = this.props;

    this.props.listDS.handleExportParams = this.handleExportParams;
    // 查询当前租户下的员工信息
    dispatch({
      type: 'serviceApply/getCurrentEmployee',
      payload: {
        tenantId,
      },
    });
  }

  @Bind()
  handleExportParams(data) {
    this.setState({
      exportParams: data,
    });
  }

  @Bind()
  initSelectStatus() {
    this.setState({
      // delButtDisabled: true,
      createWoButtonDisabled: true,
      relateWoButtonDisabled: true,
      currentStatus: null,
      currentMaintSiteId: null,
      currentAssetId: null,
      currentLocationId: null,
    });
  }

  /**
   * 选中行：控制删除按钮、创建工单、关联工单状态; 行是否可选
   * 选择拟定状态的行，则置灰待响应、待处理的行；选择待响应、待处理的行，则置灰拟定的行，同时，对于后续选择需要进行控制：
   * 数据的服务区域必须相同
   * 作业对象（设备、位置）必须相同：
   * 当前行没有作业对象，则不限制，有的话，则限制只能选没有作业对象的或者作业对象相同的数据
   * （选择的数据的设备/位置中可能只有一个有值，后续的数据也需要一致）
   */
  @Bind()
  handleSelect({ dataSet, record }) {
    const { currentStatus, currentMaintSiteId, currentAssetId, currentLocationId } = this.state;
    const {
      srStatus: selectedSrStatus,
      maintSiteId: selectedMaintSiteId,
      assetId: selectedAssetId,
      assetLocationId: selectedAssetLocationId,
    } = record.toData();
    // 最新状态
    const status = currentStatus || selectedSrStatus;
    if (['APPROVED', 'RESPONSED'].includes(status)) {
      // 可以选择的服务区域
      const maintSiteIdCanSelect = currentMaintSiteId || selectedMaintSiteId;
      // 可以选择的设备
      let assetIdCanSelect = null;
      // 可以选择的位置
      let locationIdCanSelect = null;
      if (!currentAssetId && !currentLocationId) {
        assetIdCanSelect = selectedAssetId;
        locationIdCanSelect = selectedAssetLocationId;
      } else {
        assetIdCanSelect = currentAssetId;
        locationIdCanSelect = currentLocationId;
      }

      dataSet.records.forEach(item => {
        const { srStatus, maintSiteId, assetId, assetLocationId, canResponseFlag } = item.toData();
        // 待响应（canResponseFlag字段需要不为是（值为1））、待处理的可以勾选
        const statusFlag = !(
          srStatus === 'RESPONSED' ||
          (srStatus === 'APPROVED' && !canResponseFlag)
        );
        const maintSiteFlag = maintSiteId && maintSiteId !== maintSiteIdCanSelect;
        let WoObjFlag = false;
        // 如果assetId assetLocationId 都没值 则数据可选 不做处理；一旦有值，则需要和现在已选的匹配才可选；
        if (assetId || assetLocationId) {
          if (assetIdCanSelect && locationIdCanSelect) {
            // 只要有一个不相同 就不能选择
            WoObjFlag = assetId !== assetIdCanSelect || assetLocationId !== locationIdCanSelect;
          } else if (assetIdCanSelect && !locationIdCanSelect) {
            // 设备必须相同 且 没有位置 才可选
            WoObjFlag = !(assetId === assetIdCanSelect && !assetLocationId);
          } else if (!assetIdCanSelect && locationIdCanSelect) {
            // 位置必须相同 且 没有设备 才可选
            WoObjFlag = !(assetLocationId === locationIdCanSelect && !assetId);
          }
        }

        if (statusFlag || maintSiteFlag || WoObjFlag) {
          item.selectable = false; // eslint-disable-line
        } else {
          item.selectable = true; // eslint-disable-line
        }
      });

      this.setState({
        // delButtDisabled: true, // 禁用删除按钮
        createWoButtonDisabled: false, // 禁用创建工作单按钮
        relateWoButtonDisabled: false, // 禁用关联工作单按钮
        currentStatus: status, // 当前状态
        currentMaintSiteId: maintSiteIdCanSelect,
        currentAssetId: assetIdCanSelect,
        currentLocationId: locationIdCanSelect,
      });
    }
    // else if (status === 'DRAFT') {
    //   if (dataSet.selected.length === 1) {
    //     dataSet.records.forEach(item => {
    //       item.selectable = item.get('srStatus') === 'DRAFT'; // eslint-disable-line
    //     });
    //     this.setState({
    //       // delButtDisabled: false, // 禁用删除按钮
    //       createWoButtonDisabled: true, // 禁用创建工作单按钮
    //       relateWoButtonDisabled: true, // 禁用关联工作单按钮
    //       currentStatus: status, // 当前状态
    //       currentMaintSiteId: null,
    //       currentAssetId: null,
    //       currentLocationId: null,
    //     });
    //   }
    // }
  }

  /**
   * 取消选中行:
   * 对于当前选择的是待处理/待响应数据 需要所有数据走一遍，看看剩下的数据中是否还有服务区域，设备/位置
   * 如果没有了 就要将state里的相关变量重置
   */
  @Bind()
  handleUnSelect({ dataSet }) {
    const len = dataSet.selected.length;
    if (len === 0) {
      dataSet.records.forEach(record => {
        // 拟定、待响应（canResponseFlag字段需要不为是（值为1））、待处理的可以勾选
        const srStatus = record.get('srStatus');
        const canResponseFlag = record.get('canResponseFlag');
        // eslint-disable-next-line
        record.selectable =
          ['RESPONSED'].includes(srStatus) || (srStatus === 'APPROVED' && !canResponseFlag);
      });
      this.initSelectStatus();
    } else {
      const { currentStatus, currentMaintSiteId, currentAssetId, currentLocationId } = this.state;
      if (['APPROVED', 'RESPONSED'].includes(currentStatus)) {
        const dataList = dataSet.selected.map(item => item.toData());
        let dataHasMaintSite = null;
        let dataHasWoObj = null;

        if (currentMaintSiteId) {
          dataHasMaintSite = dataList.find(item => item.maintSiteId === currentMaintSiteId);
        }

        if (currentAssetId && currentLocationId) {
          dataHasWoObj = dataList.find(
            item => item.assetId === currentAssetId && item.assetLocationId === currentLocationId
          );
        } else if (currentAssetId) {
          dataHasWoObj = dataList.find(item => item.assetId === currentAssetId);
        } else if (currentLocationId) {
          dataHasWoObj = dataList.find(item => item.assetLocationId === currentLocationId);
        }

        this.setState(
          {
            currentMaintSiteId: dataHasMaintSite ? currentMaintSiteId : null,
            currentAssetId: dataHasWoObj ? currentAssetId : null,
            currentLocationId: dataHasWoObj ? currentLocationId : null,
          },
          () => {
            // 更新数据可选状态
            this.updateRecordsStatu(dataSet);
          }
        );
      }
    }
  }

  @Bind()
  updateRecordsStatu(dataSet) {
    const { currentMaintSiteId, currentAssetId, currentLocationId } = this.state;

    dataSet.records.forEach(item => {
      const { srStatus, maintSiteId, assetId, assetLocationId, canResponseFlag } = item.toData();
      // 待响应（canResponseFlag字段需要不为是（值为1））、待处理的可以勾选
      const statusFlag = !(
        srStatus === 'RESPONSED' ||
        (srStatus === 'APPROVED' && !canResponseFlag)
      );

      const maintSiteFlag = maintSiteId && maintSiteId !== currentMaintSiteId;
      let WoObjFlag = false;
      // 如果assetId assetLocationId 都没值 则数据可选 不做处理；一旦有值，则需要和现在已选的匹配才可选；
      if (assetId || assetLocationId) {
        if (currentAssetId && currentLocationId) {
          // 只要有一个不相同 就不能选择
          WoObjFlag = assetId !== currentAssetId || assetLocationId !== currentLocationId;
        } else if (currentAssetId && !currentLocationId) {
          // 设备必须相同 且 没有位置 才可选
          WoObjFlag = !(assetId === currentAssetId && !assetLocationId);
        } else if (!currentAssetId && currentLocationId) {
          // 位置必须相同 且 没有设备 才可选
          WoObjFlag = !(assetLocationId === currentLocationId && !assetId);
        }
      }

      if (statusFlag || maintSiteFlag || WoObjFlag) {
        item.selectable = false; // eslint-disable-line
      } else {
        item.selectable = true; // eslint-disable-line
      }
    });
  }

  /**
   * 跳转到工单详情页
   */
  handleLinkToWorkOrderDetail(type, id, num) {
    const { dispatch } = this.props;
    let url;
    switch (type) {
      case 0:
        url = `/amtc/work-order/detail/${id}/${num}`;
        break;
      case 1:
        url = `/amtc/sub-requisition/detail/${id}`;
        break;
      default:
        break;
    }

    dispatch(
      routerRedux.push({
        pathname: url,
      })
    );
  }

  /**
   * 删除
   */
  @Bind()
  handleDelete() {
    this.props.listDS.delete(this.props.listDS.selected);
  }

  /**
   * 跳转到详情页
   */
  @Bind()
  handleToDetail(id) {
    const { dispatch } = this.props;
    const linkUrl = id ? `detail/${id}` : `create`;
    // 保留列表筛选条件
    const priorityId = this.props.listDS.getQueryParameter('priorityId');

    if (priorityId) {
      localStorage.setItem('priorityId', priorityId);
    }
    dispatch(
      routerRedux.push({
        pathname: `/amtc/service-center/${linkUrl}`,
      })
    );
  }

  /**
   * 创建工作单
   * 多个服务申请单创建工单，只需要带出：服务区域 位置 设备 报告时间
   */
  @Bind()
  handleCreateWo() {
    const len = this.props.listDS.selected.length;
    const { currentMaintSiteId, currentAssetId, currentLocationId } = this.state;
    const { dispatch } = this.props;

    let detail = this.props.listDS.selected[0].toData();
    // 找出有工作对象的sr
    let itemHasObj = {};
    const dataList = this.props.listDS.selected.map(item => item.toData());
    if (currentAssetId && currentLocationId) {
      itemHasObj = dataList.find(
        item => item.assetId === currentAssetId && item.assetLocationId === currentLocationId
      );
    } else if (currentAssetId) {
      itemHasObj = dataList.find(item => item.assetId === currentAssetId);
    } else if (currentLocationId) {
      itemHasObj = dataList.find(item => item.assetLocationId === currentLocationId);
    } else {
      itemHasObj = { ...detail };
    }
    const { descAndLabel, assetLocationName, maintSiteName } = itemHasObj;

    if (len > 1) {
      const srList = dataList.map(i => ({
        srId: i.srId,
        srNumber: i.srNumber,
        srName: i.srName,
        partCodeId: i.partCodeId,
        evalItemId: i.evalItemId,
        riskCodeId: i.riskCodeId,
        faultDate: moment(i.faultDate).format('YYYY-MM-DD HH:mm:ss'),
        description: i.description,
      }));
      detail = {
        maintSiteId: currentMaintSiteId,
        maintSiteName,
        assetId: currentAssetId,
        descAndLabel,
        assetLocationId: currentLocationId,
        assetLocationName,
        srListstr: JSON.stringify(srList),
      };
    }

    detail.sourceParamType = 'SR';
    detail.reportDate = moment(detail.reportDate).format('YYYY-MM-DD HH:mm:ss');
    dispatch(
      routerRedux.push({
        pathname: `/amtc/work-order/create`,
        search: queryString.stringify(detail),
      })
    );
  }

  /**
   * 关联工作单
   */
  @Bind()
  handleRelateWo() {
    const { currentMaintSiteId, currentAssetId, currentLocationId } = this.state;
    const { serviceApply } = this.props;
    const { currentEmployee } = serviceApply;
    const modalDs = new DataSet({
      autoQuery: true,
      primaryKey: 'woId',
      selection: 'single',
      queryFields: [
        {
          name: 'woNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.woNum`).d('工单编号'),
        },
        {
          name: 'woName',
          type: 'string',
          label: intl.get(`${modelPrompt}.woName`).d('工单概述'),
        },
      ],
      fields: [
        {
          name: 'woNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.woNum`).d('工单编号'),
        },
        {
          name: 'woName',
          type: 'string',
          label: intl.get(`${modelPrompt}.woName`).d('工单概述'),
        },
        {
          name: 'assetDesc',
          type: 'string',
          label: intl.get(`${modelPrompt}.assetDesc`).d('设备全称'),
        },
        {
          name: 'woStatus',
          type: 'string',
          label: intl.get(`${modelPrompt}.woStatus`).d('工单状态'),
          lookupCode: 'AMTC.WORKORDERSTATUS',
        },
      ],
      transport: {
        read: ({ params }) => {
          return {
            url: queryWoListUrl,
            method: 'GET',
            params: {
              ...params,
              tenantId: organizationId,
              maintSiteId: currentMaintSiteId,
              assetId: currentAssetId,
              assetLocationId: currentLocationId,
              plannerId: currentEmployee.employeeId,
            },
          };
        },
      },
    });
    const modalColumns = [
      {
        name: 'woNum',
      },
      {
        name: 'woName',
      },
      {
        name: 'assetDesc',
      },
      {
        name: 'woStatus',
      },
    ];

    Modal.open({
      key: modalKey,
      destroyOnClose: true,
      closable: true,
      style: {
        width: 700,
      },
      title: intl.get(`${prompt}.modal.wo`).d('工作单'),
      children: (
        <Table
          key="serviceCenterWo"
          customizedCode="AORI.SERVICE_CNTER.WO"
          dataSet={modalDs}
          columns={modalColumns}
          queryFieldsLimit={2}
        />
      ),
      onOk: () => {
        const { selected } = modalDs;
        if (selected.length > 0) {
          const { woId } = selected[0].toData();
          const body = this.props.listDS.selected.map(i => ({
            sourceId: i.get('srId'),
            woId: Number(woId),
          }));
          request(updateSrUrl, {
            method: 'POST',
            body,
          }).then(res => {
            if (res && !res.failed) {
              notification.success({
                placement: 'bottomRight',
              });
              this.props.listDS.query();
            }
          });
        } else {
          notification.error({
            message: intl.get(`${viewPrompt}.pleaseSelect`).d('请选择一条数据！'),
            placement: 'bottomRight',
          });
          return Promise.reject();
        }
      },
    });
  }

  /**
   * 创建委外申请
   * 多个服务申请单创建工单，只需要带出：服务区域 位置 设备 报告时间 报告人/组
   */
  @Bind()
  handleCreateSub() {
    const { currentMaintSiteId, currentAssetId, currentLocationId } = this.state;
    const { history } = this.props;
    const len = this.props.listDS.selected.length;
    const detail = this.props.listDS.selected[0].toData();

    // 找出有工作对象的sr
    let itemHasObj = {};
    const dataList = this.props.listDS.selected.map(item => item.toData());
    if (currentAssetId && currentLocationId) {
      itemHasObj = dataList.find(
        item => item.assetId === currentAssetId && item.assetLocationId === currentLocationId
      );
    } else if (currentAssetId) {
      itemHasObj = dataList.find(item => item.assetId === currentAssetId);
    } else if (currentLocationId) {
      itemHasObj = dataList.find(item => item.assetLocationId === currentLocationId);
    } else {
      itemHasObj = { ...detail };
    }
    const { maintSiteName, assetDesc, descAndLabel, assetLocationName } = itemHasObj;

    const sourceSrIds = [];
    const sourceInfoDTOList = dataList.map(i => {
      sourceSrIds.push(i.srId);
      return {
        sourceType: 'SR',
        sourceNum: i.srNumber,
        sourceName: i.srName,
      };
    });
    const newDetail = {
      woName: len === 1 ? detail.srName : null,
      maintSiteId: currentMaintSiteId,
      maintSiteName,
      assetId: currentAssetId,
      assetDesc,
      descAndLabel,
      assetLocationId: currentLocationId,
      assetLocationName,
      locationDesc: detail.locationDesc,
      reportOrgId: detail.reportOrgId,
      reportOrgName: detail.reportOrgName,
      reporterId: detail.reporterId,
      reporterName: detail.reporterName,
      reportDate: detail.reportDate,
      currencySymbol: detail.currencySymbol,
      sourceTypeCode: 'SR',
      sourceSrIds: JSON.stringify(sourceSrIds),
      sourceInfoDTOList: JSON.stringify(sourceInfoDTOList),
    };

    history.push({
      pathname: `/amtc/sub-requisition/create`,
      state: {
        sourceParamType: 'SR', // 标识为服务中心跳转过去的
      },
      query: newDetail,
    });
  }

  /**
   * 关联委外申请
   */
  @Bind()
  handleRelateSub() {
    const { currentMaintSiteId, currentAssetId, currentLocationId } = this.state;
    const { serviceApply } = this.props;
    const { currentEmployee } = serviceApply;
    const modalDs = new DataSet({
      autoQuery: true,
      primaryKey: 'woId',
      selection: 'single',
      queryFields: [
        {
          name: 'woNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.applyNum`).d('申请编号'),
        },
        {
          name: 'woName',
          type: 'string',
          label: intl.get(`${modelPrompt}.applyName`).d('申请概述'),
        },
      ],
      fields: [
        {
          name: 'woNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.applyNum`).d('申请编号'),
        },
        {
          name: 'woName',
          type: 'string',
          label: intl.get(`${modelPrompt}.applyName`).d('申请概述'),
        },
        {
          name: 'assetDesc',
          type: 'string',
          label: intl.get(`${modelPrompt}.assetDesc`).d('设备全称'),
        },
        {
          name: 'woStatus',
          type: 'string',
          label: intl.get(`${modelPrompt}.applyStatus`).d('申请状态'),
          lookupCode: 'AMTC.WORKORDERSTATUS',
        },
      ],
      transport: {
        read: ({ params }) => {
          return {
            url: querySubListUrl,
            method: 'GET',
            params: {
              ...params,
              tenantId: organizationId,
              maintSiteId: currentMaintSiteId,
              assetId: currentAssetId,
              assetLocationId: currentLocationId,
              plannerId: currentEmployee.employeeId,
              woStatusList:
                'APPROVING,APPROVED,WRD,INPRG,WSCH,PAUSE,COMPLETED,UNABLE,WAITCHECK,REJECTED,SIGNED,WAITCOMPCHECK,REWORKING',
            },
          };
        },
      },
    });
    const modalColumns = [
      {
        name: 'woNum',
      },
      {
        name: 'woName',
      },
      {
        name: 'assetDesc',
      },
      {
        name: 'woStatus',
      },
    ];

    Modal.open({
      key: modalKey,
      destroyOnClose: true,
      closable: true,
      style: {
        width: 700,
      },
      title: intl.get(`${prompt}.modal.sub`).d('委外申请单'),
      children: (
        <Table
          key="serviceCenterSub"
          customizedCode="AORI.SERVICE_CNTER.SUB"
          dataSet={modalDs}
          columns={modalColumns}
          queryFieldsLimit={2}
        />
      ),
      onOk: () => {
        const { selected } = modalDs;
        if (selected.length > 0) {
          const { woId } = selected[0].toData();
          const body = this.props.listDS.selected.map(i => ({
            sourceId: i.get('srId'),
            woId: Number(woId),
          }));
          request(updateSrUrl, {
            method: 'POST',
            body,
          }).then(res => {
            if (res && !res.failed) {
              notification.success({
                placement: 'bottomRight',
              });
              this.props.listDS.query();
            }
          });
        } else {
          notification.error({
            message: intl.get(`${viewPrompt}.pleaseSelect`).d('请选择一条数据！'),
            placement: 'bottomRight',
          });
          return Promise.reject();
        }
      },
    });
  }

  /**
   * 需求组织改变
   */
  @Bind()
  handleChangeReportOrg(record, type) {
    this.props.listDS.queryDataSet.current.set(
      'orgName',
      type === 'PLATFORM' ? record?.unitName : record?.orgName
    );
    this.props.listDS.setQueryParameter(
      'orgId',
      type === 'PLATFORM' ? record?.unitId : record?.orgId
    );
    this.props.listDS.setQueryParameter('reportOrgType', type);
  }

  /**
   * 清空额外的查询参数
   */
  @Bind
  handleClearQuery() {
    for (const key in this.props.listDS.queryParameter) {
      if (key) {
        this.props.listDS.setQueryParameter(key, null);
      }
    }
    localStorage.removeItem('priorityId');
  }

  get columns() {
    return [
      {
        name: 'srTypeIcon',
        width: 65,
        renderer: ({ record }) => (
          <IconSelect
            title={record.get('srTypeIcon')}
            iconTypeCode={record.get('woTypeIconTypeCode')}
            type={record.get('srTypeIcon')}
            style={menuIconStyle}
            color={record.get('priorityColor')}
          />
        ),
      },
      {
        name: 'srNumber',
        width: 100,
        renderer: ({ value, record }) => (
          <a onClick={() => this.handleToDetail(record.get('srId'))}>{value}</a>
        ),
      },
      {
        name: 'srName',
      },
      {
        name: 'srStatusMeaning',
        width: 120,
        renderer: ({ record }) => {
          const { srStatus, srStatusMeaning } = record.toData();
          return statusColors[srStatus] ? (
            <Tag
              style={{
                color: statusColors[srStatus].fontColor,
                border: 0,
              }}
              color={statusColors[srStatus].bgColor}
            >
              {srStatusMeaning}
            </Tag>
          ) : (
            <Tag style={{ color: '#000' }} color="#fff">
              {srStatusMeaning}
            </Tag>
          );
        },
      },
      {
        name: 'orgName',
        width: 120,
      },
      {
        name: 'descAndLabel',
        renderer: ({ value, record }) => {
          const assetLocationName = record.get('assetLocationName');
          if (!isUndefined(value) && !isUndefined(assetLocationName)) {
            return `${assetLocationName}/${value}`;
          } else if (isUndefined(value) && !isUndefined(assetLocationName)) {
            return assetLocationName;
          } else if (!isUndefined(value) && isUndefined(assetLocationName)) {
            return value;
          } else {
            return '';
          }
        },
      },
      {
        name: 'description',
      },
      {
        name: 'reporterName',
        width: 100,
      },
      {
        name: 'basicTypeFlag',
        renderer: ({ value, record }) =>
          record.get('woId')
            ? value
              ? intl.get(`${viewPrompt}.sub`).d('委外')
              : intl.get(`${viewPrompt}.wo`).d('工单')
            : '-',
      },
      {
        name: 'woNum',
        width: 100,
        renderer: ({ value, record }) => (
          <a
            onClick={() =>
              this.handleLinkToWorkOrderDetail(
                record.get('basicTypeFlag'),
                record.get('woId'),
                value
              )
            }
          >
            {value || '-'}
          </a>
        ),
      },
    ];
  }

  render() {
    const { tenantId } = this.props;
    const { relateWoButtonDisabled, createWoButtonDisabled } = this.state;
    return (
      <>
        <Header title={intl.get(`${prompt}.view.title.serviceCenter`).d('服务中心')}>
          <Button color="primary" onClick={() => this.handleToDetail()}>
            {intl.get('hzero.common.button.create').d('新建')}
          </Button>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  key="relete-wo"
                  disabled={relateWoButtonDisabled}
                  onClick={this.handleRelateWo}
                >
                  {intl.get(`${prompt}.button.relateWo`).d('关联工作单')}
                </Menu.Item>
              </Menu>
            }
            placement="bottomCenter"
          >
            <Button
              style={{ marginLeft: 8 }}
              disabled={createWoButtonDisabled}
              onClick={this.handleCreateWo}
            >
              {intl.get(`${prompt}.button.createWo`).d('创建工作单')}
              <Icon type="expand_more" style={{ fontSize: '14px' }} />
            </Button>
          </Dropdown>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  key="relete-sub"
                  disabled={relateWoButtonDisabled}
                  onClick={() => this.handleRelateSub()}
                >
                  {intl.get(`${prompt}.button.relateSub`).d('关联委外申请')}
                </Menu.Item>
              </Menu>
            }
            placement="bottomCenter"
          >
            <Button
              style={{ marginLeft: 8 }}
              disabled={createWoButtonDisabled}
              onClick={this.handleCreateSub}
            >
              {intl.get(`${prompt}.button.createSub`).d('创建委外申请')}
              <Icon type="expand_more" style={{ fontSize: '14px' }} />
            </Button>
          </Dropdown>
          {/* <Button disabled={delButtDisabled} onClick={this.handleDelete}>
            {intl.get('hzero.common.button.delete').d('删除')}
          </Button> */}
          <ExcelExport
            requestUrl={`${HALM_MTC}/v1/${tenantId}/sr/export`}
            queryParams={this.state.exportParams}
          />
        </Header>
        <Content>
          {/* <div className={styles['sr-custom-table']}> */}
          <Table
            key="ServiceCenterList"
            customizedCode="AORI.SERVICE_CENTER.LIST"
            dataSet={this.props.listDS}
            columns={this.columns}
            // queryFieldsLimit={3}
            queryFields={{
              orgName: (
                <OrgPartnerLov
                  name="orgName" // 需求组织
                  handleOk={this.handleChangeReportOrg}
                />
              ),
              priorityLov: <Lov name="priorityLov" tableProps={priorityTableProps} />,
              reportDateTo: (
                <DateTimePicker
                  dataSet={this.props.listDS}
                  name="reportDateTo"
                  defaultTime={moment().endOf('day')}
                />
              ),
            }}
          />
          {/* </div> */}
        </Content>
      </>
    );
  }
}

export default ServiceCenter;
