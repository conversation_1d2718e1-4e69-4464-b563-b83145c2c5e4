/**
 * WorkOrder - 工作单详情页
 * @since 2020-09-21
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2020, Hand
 */

import React, { Component } from 'react';
import { Bind } from 'lodash-decorators';
import { observer } from 'mobx-react';
import { isUndefined, isArray, isEmpty, omit } from 'lodash';
import {
  Lov,
  DataSet,
  Button,
  Modal,
  Form,
  Table,
  Output,
  CheckBox,
  DateTimePicker,
  ModalProvider,
} from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Header } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { HALM_MTC, HALM_MDM } from 'alm/utils/config';
import { HZERO_PLATFORM } from 'utils/config';
import { getCurrentUserId, getCurrentOrganizationId, getResponse } from 'utils/utils';
import request from 'utils/request';
import { buttonsRender, isPassRender } from 'alm/utils/renderer';
import queryString from 'querystring';
import notification from 'utils/notification';
import WoLayout from 'alm/components/WoLayout';
import ReasonModal from 'alm/components/ReasonModal';
import EmployeesLov from 'alm/components/EmployeesLov';
import ApproveHistoryBtn from 'alm/components/ApproveHistoryBtn';
import InspectForm from 'alm/pages/EvaluateTemp/components/InspectForm';
import { handleStaffCallBack } from 'alm/components/DefaultMaintainer/utils';
import { selectOrganizationEmployeeRelation } from 'alm/services/organizationService';
import { searchEquipmentAssetDetail } from 'alm/services/equipmentAssetService';
import { statusColors } from 'alm/utils/constants';
import closeIcon from 'alm/assets/refuse-tip.png';
import checkRecordIcon from '../assets/check-record.svg';
import assignCheckerIcon from '../assets/assign-checker.svg';
import InfoExhibit from './InfoExhibit';
import FullTextSearch from './FullTextSearch';
import HeaderInfo from './HeaderInfo';
import FileUploadButton from './FileUploadButton';

import { basicDataDs, checkRecordDs } from '../Stores/detailDs';
import { woopListDs, wrdWoopListDs } from '../Stores/woopListDs';
import { sourceDs } from '../Stores/sourceTableDs';
import { handleResponseResult } from '../utils';
import {
  saveWoService,
  saveWoCopyService,
  queryWoDetailService,
  queryWoRelateListService,
  queryWoButtousService,
  updateWoStatusService,
  updateWoCloseReasonService,
} from '../api';
import getLang from '../Langs';

import styles from './index.module.less';

const userId = getCurrentUserId();
const organizationId = getCurrentOrganizationId();

const modalKey = Modal.key();

const viewPrompt = 'amtc.workOrder.view.message';
const modelPrompt = 'amtc.workOrder.model.workOrder';

const noUseFieldsArr = ['maintSiteLov', 'woTypeLov', 'contactLov', 'assetRouteLov', 'reporterLov'];

// 故障
const woMalfunctionListUrl = `${HALM_MTC}/v1/${organizationId}/wo-malfunctions`;
// 忽略检查项
const ignoreUrl = `${HALM_MTC}/v1/${organizationId}/wo-checklists/ignore`;
// 更新工单关联的sr数据
const updateRelateSrUrl = `${HALM_MTC}/v1/${organizationId}/sr/update-by-source`;
// 指定验收员
const assignCheckerUrl = `${HALM_MTC}/v1/${organizationId}/work-orders/changeChecker`;
// 查询当前用户在当前租户下的员工
const getCurrentEmployeeUrl = `${HZERO_PLATFORM}/v1/${organizationId}/employee-users/employee`;
// 获取当前用户对应员工所属组织下的服务区域
const getMaintSitesOfCurrentUserUrl = `${HALM_MDM}/v1/${organizationId}/maint-sites/by-user`;

@formatterCollections({
  code: [
    'amtc.workOrder',
    'amtc.woMalfunction',
    'amtc.woChecklists',
    'amtc.woOutsourcing',
    'amtc.woLabors',
    'amtc.material',
    'hzero.c7nUI',
    'alm.common',
    'alm.component',
    'amtc.woop',
    'appm.proBasicInfo',
    'ammt.itemRequisition',
    'ammt.materialTsMulti',
    'amtc.woCost',
    'alm.checklistEditModal',
  ],
})
@observer
export default class Detail extends Component {
  constructor(props) {
    super(props);
    const { state } = props.location;

    this.state = {
      queryDeatilLoading: false, // 基础数据查询
      woopListLoading: false, // 查询任务列表
      assignCheckerLoading: false, // 指定验收员
      editFlag: state && state.isEdit ? state.isEdit : false, // 标识：编辑还是查看
      showSearchFlag: false, // 搜索区域的显示隐藏

      // 控制表单字段的变量：
      // 工单字段disabled属性控制-初始值(required属性写在了Ds中，因为这样才能在必输校验的显示红框提醒)
      itemProperty: {
        maintSiteId: {}, // 服务区域
        woTypeId: {}, // 工单类型
        actId: {}, // 标准作业活动
        assetId: {}, // 设备
        assetLocationId: {}, // 位置
        serviceAgreement: {}, // 服务协议
        waitingWoopownerFlag: {}, // 接单明确工作单负责人
        targetStartDate: {}, // 目标开始日期
        targetFinishDate: {}, // 目标完成日期
        reportDate: {}, // 报告时间
        reportPriorityId: {}, // 报告的优先级
      },
      disableAllByStatus: false, // 禁用所有字段： 审批中、待验收、工作完成、取消、结束、无法执行五种状态下为true
      orgFlag: 0, // 取工单类型上enableOrgFlag（需要指定需求组织/客户）字段 => 控制几个表单控件的显示
      woBasicType: null, // 工单类型-基础大类字段
      showSourceFlag: 0, // 工单类型-显示来源信息字段
      scheduleRequirmentStatus: null, // 工单类型-计划状态字段

      // 系统数据：
      currentEmployee: {}, // 当前用户
      currentUser: {}, // 当前用户所属组织
      maintSitesIds: '', // 当前用户对应员工所属组织下的服务区域的ids

      // 任务tab 分页数据：
      woopListPagenation: {
        page: 0,
        size: 10,
        total: 0,
      },

      // 查看页面的明细数据：
      woTypeDetail: {}, // 非新建的时候查询工单数据带出的该工单所属工单类型明细数据
      currentWo: {}, // 非新建时，记录下进入该页面时获取的工单数据 =>  查看页面，头部信息展示
      // 查看页面的控制字段：
      showEditBtn: true, // 显示编辑按钮=> 控制是否显示 编辑 按钮: 工单状态为拟定、已拒绝、待处理、执行中、返工中显示编辑按钮
      billingInfoFlag: 0, // 需要结算信息（展示服务协议
      isOpenPerson: 0,
      outsourceFlag: 0,
      isOpenCheck: 0, // 取值于工单所属工单类型 => 是否开启检查项tab
      isOpenMaterial: 0, // 物料tab显示
      isOpenAssess: 0,
      showFailureFlag: false, // 控制故障tab显示: 工单类型上故障登记规则为非必须输入或必输  且 工单有设备、设备上有故障字典 才显示
      costFlag: 0, // 成本tab显示

      // 附件管理参数 start
      woopNum: 0, // 工序号
      uploadModule: {},
      // 附件管理参数 end

      activeKey: 'basicTab', // 保存从计划调度中心哪个tab跳转过来，用于详情页返回
      handleRecord: {}, // 手动填写的负责人/计划员的值
      tsData: {}, // 携参创建初始化缓存的重要数据
      btnsLoadingCompleteFlag: false, // 页面按钮加载完成flag
      btnDisplayCfg: {}, // 详情界面按钮显示配置
    };
    this.leftRef = React.createRef(); // 左侧搜索栏中的内容组件
    this.infoExhibitRef = React.createRef();
    this.reasonModalRef = React.createRef();
    this.createCheckRef = React.createRef();

    this.basicDataDs = new DataSet(basicDataDs());
    this.checkRecordDs = new DataSet(checkRecordDs());
    this.woopListDs = new DataSet(woopListDs());
    this.sourceDs = new DataSet(sourceDs('WO')); // 工单来源数据
    this.handleStaffCallBackBind = handleStaffCallBack.bind(this);
  }

  @Bind
  handleSetState(records) {
    this.setState(records);
  }

  async componentDidMount() {
    const {
      history,
      match: { params },
    } = this.props;
    const { woId } = params;
    await this.handleGetSystemInfo(isUndefined(woId));
    if (isUndefined(woId)) {
      // 新建 - 切换到basicTab
      this.infoExhibitRef?.current?.handleTabsChange?.('basicTab');
    }
    this.handleRefreshBasic();

    // 清除外部传入的isEdit，保持右键刷新到详情状态
    history.replace({ ...history.location, state: { ...history.location?.state, isEdit: false } });
  }

  componentDidUpdate(preProps) {
    if (
      preProps.location.search !== this.props.location.search ||
      preProps.location.pathname !== this.props.location.pathname
    ) {
      this.handleRefreshBasic();
      this.infoExhibitRef?.current?.handleTabsChange?.('basicTab');
    }
  }

  get checkRecordColumns() {
    return [
      {
        name: 'checkDate',
      },
      {
        name: 'passFlag',
        renderer: ({ value }) => isPassRender(value),
      },
      {
        name: 'checkerName',
      },
      {
        header: intl.get('hzero.common.table.column.option').d('操作'),
        lock: 'right',
        width: 80,
        renderer: ({ record }) => {
          const checkRecordId = record?.get('checkRecordId');
          const fileModuleId = record?.get('fileModuleId');
          return (
            <a onClick={() => this.openCheckModal(checkRecordId, fileModuleId)}>
              {intl.get('hzero.common.button.view').d('查看')}
            </a>
          );
        },
      },
    ];
  }

  // 刷新基本页
  @Bind
  async handleRefreshBasic() {
    const {
      location: { search, state = {} },
      match: { params },
    } = this.props;
    const { woId, woNum } = params;
    const { detail = {} } = state;
    const searchItem = queryString.parse(search.substring(1)) || {};
    const { currentEmployee, currentUser, itemProperty, woBasicType } = this.state;

    return new Promise(resolve => {
      // 非新建
      if (!isUndefined(woId)) {
        const startObj = {
          queryDeatilLoading: true,
          btnsLoadingCompleteFlag: false,
        };
        this.setState(startObj);
        Promise.all([
          queryWoDetailService(woId),
          queryWoRelateListService(woId),
          queryWoButtousService(woNum),
        ]).then(result => {
          if (result[0] && !result[0].failed) {
            // 处理response
            const res = handleResponseResult(result[0]);
            const { woTypeDTO, ...others } = res;
            this.basicDataDs.data = [
              { ...others, costObjectLimitType: woTypeDTO?.costObjectLimitType },
            ];

            // 设置 woBasicTypeOfAct 作为 工单类型lov查询参数
            this.basicDataDs.current.set('woBasicTypeOfAct', others.actBasicType);
            // 旧数据没有fileModuleId,生成一个
            if (!res.fileModuleId) {
              this.basicDataDs.current.set('fileModuleId', Date.now());
            }
            const endObj = {
              queryDeatilLoading: false,
              woTypeDetail: woTypeDTO || {}, // 该工单所属工单类型明细数据
              currentWo: others, // 工单信息
              isOpenPerson: woTypeDTO?.workcenterPeopleFlag || 0,
              outsourceFlag: woTypeDTO?.outsourceFlag || 0,
              isOpenCheck: woTypeDTO?.checklistsFlag || 0,
              isOpenMaterial: woTypeDTO?.itemsFlag || 0,
              isOpenAssess: woTypeDTO?.defaultAssessFlag || 0,
              billingInfoFlag: woTypeDTO?.billingInfoFlag || 0,
              showFailureFlag: others.showFailureFlag || false,
              costFlag: woTypeDTO?.costFlag || 0,
              // 先重置为初始状态，避免导航进入旧数据影响
              disableAllByStatus: false,
            };

            // 工单类型上的 工单工作对象控制（workObjCtrlCode）为 NOT_ALLOW (不允许输入) 时禁用：
            if (woTypeDTO?.workObjCtrlCode && woTypeDTO?.workObjCtrlCode === 'NOT_ALLOW') {
              endObj.itemProperty = {
                ...itemProperty,
                assetId: { disabled: true }, // 设备
                assetLocationId: { disabled: true }, // 位置
              };
            }

            // 渲染顶部按钮
            if (result[2] && !result[2].failed) {
              this.setState({
                btnDisplayCfg: result[2],
                showEditBtn: result[2]?.staticButtonList?.includes('editWo'),
                disableAllByStatus: !result[2]?.staticButtonList?.includes('editWo'),
              });
              this.statusBtns = buttonsRender(
                'WO',
                result[2]?.buttonMap || {},
                this.changeStatusBtnClick
              );
              endObj.btnsLoadingCompleteFlag = true;
            }

            this.setState(endObj);

            // 根据工单状态来控制字段必输或不可用
            this.setItemPropertyByWoStatus(res);
            // 根据工单类型(工单工作对象控制)设置字段属性
            this.setItemPropertyByWoType(woTypeDTO);
          }

          if (result[1] && !result[1].failed) {
            this.sourceDs.data = result[1] || [];
          }
        });
      } else {
        // 新建
        const { orgId: orgIdOfCurrentUser, orgName: orgNameOfCurrentUser, orgType } = currentUser;
        const {
          employeeId: employeeIdOfCurrentEmployee,
          employeeName: employeeNameOfCurrentEmployee,
          mobile: mobileOfCurrentEmployee,
        } = currentEmployee;

        const {
          srListstr,
          sourceParamType,
          maintSiteId: sourceMaintSiteId,
          maintSiteName: sourceMaintSiteName,
        } = searchItem;
        const srList = srListstr && JSON.parse(srListstr);
        // 目前有7种：1. 直接新建 2. 服务申请单建工单 3. 工单建工单（工单创建关联/跟踪工单）4. 标准作业建工单 5.复制工单 6.项目任务创建工单 7.告警记录创建工单
        let newData = {};
        const createByHand = isEmpty(searchItem);
        const createByAct = !isUndefined(sourceParamType) && sourceParamType === 'ACT';
        const createByWo =
          !isUndefined(sourceParamType) &&
          (sourceParamType === 'Relate' || sourceParamType === 'Track');
        const createBySr = !isUndefined(sourceParamType) && sourceParamType === 'SR';
        const createByCopyWo = !isUndefined(sourceParamType) && sourceParamType === 'COPYWO';
        const createByWbs = !isUndefined(sourceParamType) && sourceParamType === 'WBS';
        const createByEIO = sourceParamType === 'EQP_IN_OUT';
        const createByAlarm = !isUndefined(sourceParamType) && sourceParamType === 'ALARM';
        if (createByHand) {
          // 没有search部分： 说明是在工单列表页面点击新建按钮进入的（新建按钮新建），而非其他页面进入（比如：服务申请单-创建工单）
          newData = {
            reportOrgId: orgIdOfCurrentUser,
            reportOrgName: orgNameOfCurrentUser,
            reportOrgType: orgType,
            reporterId: employeeIdOfCurrentEmployee,
            reporterName: employeeNameOfCurrentEmployee,
          };
        } else if (createByAct) {
          // 通过标准作业创建
          // 数据来源是从state来的而不是search
          newData = {
            ...detail,
            reportOrgId: orgIdOfCurrentUser,
            reportOrgName: orgNameOfCurrentUser,
            reportOrgType: orgType,
            reporterId: employeeIdOfCurrentEmployee,
            reporterName: employeeNameOfCurrentEmployee,
            maintSiteId: sourceMaintSiteId || null,
            maintSiteName: sourceMaintSiteName,
          };
        } else if (createByWo) {
          // 工单创建关联工单/跟踪工单
          newData = {
            woName: searchItem.woName,
            actId: searchItem.actId || null,
            actName: searchItem.actName || null,
            maintSiteId: sourceMaintSiteId || null,
            maintSiteName: sourceMaintSiteName,
            sourceTypeCode: searchItem.sourceTypeCode || null,
            sourceTypeMeaning: searchItem.sourceTypeMeaning || null,
            sourceName: searchItem.sourceName,
            sourceId: Number(searchItem.sourceId),
            sourceReference: searchItem.woNum,
          };
        } else if (createBySr) {
          // 服务申请单创建工单(仅该情况下不要sourceId sourceName sourceReference, 保存的时候使用sourceSrIds)
          newData = {
            woName: searchItem.srName,
            reportOrgId: searchItem.reportOrgId || null,
            reportOrgName: searchItem.reportOrgName || null,
            reportOrgType: searchItem.orgType || null,
            reportPriorityId: searchItem.priorityId || null, // 报告优先级取SR的计划优先级
            reportPriorityName: searchItem.priorityName,
            maintSiteId: sourceMaintSiteId || null,
            maintSiteName: sourceMaintSiteName,
            assetLocationId: searchItem.assetLocationId || null,
            assetLocationName: searchItem.assetLocationName || null,
            assetId: searchItem.assetId || null,
            descAndLabel: searchItem.descAndLabel || null,
            reporterId: searchItem.reporterId || null,
            reporterName: searchItem.reporterName,
            locationDesc: searchItem.locationDesc,
            sourceTypeCode: 'SR',
            contactId: searchItem.contactId || null,
            contactName: searchItem.contactName,
            contactDesc: searchItem.contactDesc,
            phone: searchItem.phone,
            mapSourceCode: searchItem.mapSourceCode || null,
            mapSourceMeaing: searchItem.mapSourceMeaing,
            reportDate: searchItem.reportDate,
          };
          this.setState({
            tsData: {
              assetLocationId: searchItem.assetLocationId || null,
              assetLocationName: searchItem.assetLocationName || null,
              assetId: searchItem.assetId || null,
              descAndLabel: searchItem.descAndLabel || null,
            }, // 暂存SR带来的设备和位置
          });
          // 清空旧数据，避免多次从服务申请单创建工作单
          this.sourceDs.loadData();
          this.basicDataDs.loadData();
          if (woBasicType) {
            this.setState({
              woBasicType: null,
            });
          }
          // 如果是多个sr创建工单 报告人等信息逻辑同直接新建
          if (srList) {
            newData = {
              ...newData,
              reportOrgId: orgIdOfCurrentUser,
              reportOrgName: orgNameOfCurrentUser,
              reportOrgType: orgType,
              reporterId: employeeIdOfCurrentEmployee,
              reporterName: employeeNameOfCurrentEmployee,
            };
            const sourceData = srList.map(i => ({
              sourceType: 'SR',
              sourceId: i.srId,
              sourceNum: i.srNumber,
              sourceName: i.srName,
              canRemoveFlag: false, // 从服务单新建工单时不可移除；该字段对于sr来说，还可以用于区分是relate还是create的sr(true则为realte)
            }));
            this.sourceDs.data = sourceData;
          } else {
            this.sourceDs.create({
              sourceType: 'SR',
              sourceId: searchItem.srId,
              sourceNum: searchItem.srNumber,
              sourceName: searchItem.srName,
              canRemoveFlag: false, // 从服务单新建工单时不可移除
            });
          }
        } else if (createByCopyWo) {
          // 复制工单
          newData = {
            ...searchItem,
            projectRelatedFlag: searchItem.projectId ? 1 : 0,
            reportOrgId: orgIdOfCurrentUser,
            reportOrgName: orgNameOfCurrentUser,
            reportOrgType: orgType,
            reporterId: employeeIdOfCurrentEmployee,
            reporterName: employeeNameOfCurrentEmployee,
            mapSourceCode: searchItem.mapSourceCode || null,
          };
          if (searchItem.projectId) {
            this.sourceDs.create({
              sourceType: 'Project',
              sourceId: searchItem.projectId,
              sourceNum: searchItem.projectCode,
              sourceName: `${searchItem.projectName}——${searchItem.taskName}`,
              taskCode: searchItem.taskCode,
              taskName: searchItem.taskName,
              canRemoveFlag: true,
              newRecord: true, // 用于编辑状态下区分是否为新增的记录
            });
          }
        } else if (createByWbs) {
          // 项目wbs创建工单
          newData = {
            maintSiteId: searchItem.maintSiteId || null,
            maintSiteName: searchItem.maintSiteName || null,
            reportOrgId: orgIdOfCurrentUser,
            reportOrgName: orgNameOfCurrentUser,
            reportOrgType: orgType,
            reporterId: employeeIdOfCurrentEmployee,
            reporterName: employeeNameOfCurrentEmployee,
            projectId: searchItem.projectId,
            taskCode: searchItem.taskCode,
            // scheduledStartDate: searchItem.startDate || null,
            // sourceTypeCode: 'WBS',
          };
          // 清空数据防止多次从项目工作台跳转到工单，造成可关联多条项目任务
          this.sourceDs.loadData();
          this.basicDataDs.loadData();
          if (woBasicType) {
            this.setState({
              woBasicType: null,
            });
          }
          this.sourceDs.create({
            sourceType: 'Project',
            // sourceTypeCode: 'WBS',
            sourceId: searchItem.projectId,
            sourceNum: searchItem.projectCode,
            sourceName: `${searchItem.projectName}——${searchItem.taskName}`,
            taskCode: searchItem.taskCode,
            taskName: searchItem.taskName,
            canRemoveFlag: true,
            newRecord: true, // 用于编辑状态下区分是否为新增的记录
          });
        } else if (createByEIO) {
          newData = {
            reportOrgId: orgIdOfCurrentUser,
            reportOrgName: orgNameOfCurrentUser,
            reportOrgType: orgType,
            reporterId: employeeIdOfCurrentEmployee,
            reporterName: employeeNameOfCurrentEmployee,
          };
        } else if (createByAlarm) {
          this.setState({
            itemProperty: {
              ...itemProperty,
              maintSiteId: { disabled: true },
            },
          });
          newData = {
            ...omit(detail, ['sourceList']),
            reportOrgId: orgIdOfCurrentUser,
            reportOrgName: orgNameOfCurrentUser,
            reportOrgType: orgType,
            reporterId: employeeIdOfCurrentEmployee,
            reporterName: employeeNameOfCurrentEmployee,
          };
          this.sourceDs.loadData(detail.sourceList);
        }

        // 对需求组织相关信息进行处理
        if (createByHand || createByAct || createByCopyWo || createByWbs || createByEIO) {
          // 列表页点击新建按钮新建 或者 复制自模板（标准作业建工单）复制工单 取当前登录信息
          newData = {
            ...newData,
            orgId: orgIdOfCurrentUser, // 客户/需求组织
            orgName: orgNameOfCurrentUser,
            orgType,
            contactId: employeeIdOfCurrentEmployee, // 需求方联系人
            contactName: employeeNameOfCurrentEmployee,
            phone: mobileOfCurrentEmployee,
          };
        } else {
          // 其余几种情况的新建
          newData = {
            ...newData,
            orgId: searchItem.orgId, // 客户/需求组织
            orgName: searchItem.orgName,
            orgType,
            contactId: searchItem.contactId, // 需求方联系人
            contactName: searchItem.contactName,
            phone: searchItem.phone,
          };
        }
        // 首先先置空 避免类似：点击新建进入 然后又从sr进入 同时创建了两条数据的情况
        this.basicDataDs.data = [];
        this.basicDataDs.create(newData, 0);

        // 从项目创建工作单限制工单类型lov数据必须为可关联项目信息
        if (this.basicDataDs.current && createByWbs) {
          this.basicDataDs.current.setState('fromPage', 'WBS');
        }

        if ((createByWo && searchItem.actId) || (createByAct && searchItem.actId)) {
          // 如果为关联工单跟踪工单 并且带出标准作业活动，触发标准作业活动带出更多信息事件
          // 复制自模板（通过标准作业创建）也触发
          // eslint-disable-next-line
          this.infoExhibitRef?.current?.handleMoreDataWithAct?.(searchItem.actId);
        }

        if (createByCopyWo && searchItem.woTypeId) {
          // 如果为复制工单 要带出工单类型相关配置
          // 复制工单视为手动复制工作中心和人员
          this.setHandleRecord(searchItem);
          // eslint-disable-next-line
          this.infoExhibitRef?.current?.getWoType?.(searchItem.woTypeId, res => {
            // eslint-disable-next-line
            this.infoExhibitRef?.current?.handleWoTypeChange?.({
              ...res,
              defaultPriorityId: searchItem.priorityId,
              defaultPriorityName: searchItem.priorityName,
            });
          });
        }
        if (createByEIO) {
          searchEquipmentAssetDetail({
            assetInfoId: searchItem?.assetId,
            tenantId: organizationId,
          }).then(res => {
            // eslint-disable-next-line no-unused-expressions
            this.infoExhibitRef?.current?.onAssetChange(res);
          });
        }
      }
      resolve('suceess');
    });
  }

  // 获取各系统信息
  @Bind()
  handleGetSystemInfo(isNew) {
    const fun = [
      this.handleSearchCurrentEmployee(), // 获取当前用户（报告人）
    ];
    if (isNew) {
      fun.push(this.handleSearchEmyOrg()); // 获取当前用户（报告人）的组织)
      fun.push(this.getMaintSitesOfCurrentUser()); // 获取当前用户对应员工所属组织下的服务区域
    }
    return Promise.all(fun)
      .then(resultArr => {
        if (isNew) {
          this.setState({
            currentEmployee: resultArr[0],
            currentUser: resultArr[1],
            maintSitesIds: resultArr[2],
          });
        } else {
          this.setState({
            currentEmployee: resultArr[0],
          });
        }
      })
      .catch(errorInfo => {
        notification.error({ message: errorInfo });
      });
  }

  @Bind()
  handleSearchCurrentEmployee() {
    // 查询当前用户在当前租户下的员工
    return new Promise(resolve => {
      request(getCurrentEmployeeUrl, {
        method: 'GET',
        query: {
          enabledFlag: 1,
        },
      }).then(res => {
        if (res && !res.failed) {
          resolve(res);
        }
      });
    });
  }

  // 获取当前用户（报告人）的组织
  @Bind()
  async handleSearchEmyOrg() {
    if (!isUndefined(userId)) {
      const res = await selectOrganizationEmployeeRelation({ tenantId: organizationId, userId });
      return new Promise(resolve => {
        if (res && !res.failed) {
          resolve(res[0]);
        }
      });
    }
  }

  // 获取当前用户对应员工所属组织下的服务区域
  @Bind()
  getMaintSitesOfCurrentUser() {
    if (!isUndefined(userId)) {
      return new Promise(resolve => {
        request(`${getMaintSitesOfCurrentUserUrl}/${userId}`, {
          method: 'GET',
        }).then(res => {
          if (res && !res.failed && isArray(res) && res.length > 0) {
            const idArr = res.map(i => i.maintSiteId);
            const ids = idArr.join(',');
            resolve(ids);
          }
        });
      });
    }
  }

  @Bind()
  setHandleRecord(record) {
    const employee = {
      plannerGroupId: record.plannerGroupId,
      plannerGroupName: record.plannerGroupName,
      ownerGroupId: record.ownerGroupId,
      ownerGroupName: record.ownerGroupName,
      plannerId: record.plannerId,
      plannerName: record.plannerName,
      ownerId: record.ownerId,
      ownerName: record.ownerName,
    };
    // 清空标准作业的时候(isClear为true) 要清空负责人、计划员的值
    this.basicDataDs.current.set(employee);
    this.setState({ handleRecord: employee });
  }

  /**
   * 搜索区域隐藏显示
   */
  @Bind()
  showLeftSearch() {
    const { showSearchFlag } = this.state;
    const reShowSearchFlag = !showSearchFlag;
    this.setState({ showSearchFlag: reShowSearchFlag });
    // 展开时获取最新数据
    if (reShowSearchFlag) {
      this.refreshLeftData();
    }
  }

  /**
   * 根据工单状态来控制字段必输或不可用
   * 注：老版中名为 setItemProperty
   */
  @Bind()
  setItemPropertyByWoStatus(dataSource) {
    const { itemProperty, showEditBtn } = this.state;
    const disabledObj = { disabled: true };
    if (!showEditBtn) {
      this.setState({
        disableAllByStatus: false,
      });
    }
    // 审批中、工作完成、结束、取消、无法执行、待验收：不显示工单编辑按钮
    switch (dataSource.woStatus) {
      case 'REJECTED': // 已拒绝：字段必输逻辑与拟定状态一致
      case 'DRAFT': // 拟定
      case 'WSCH': // 等待安排（旧工单状态）
        break;
      case 'APPROVING': // 审批中
      case 'COMPLETED': // 工作完成
      case 'CLOSED': // 结束
      case 'CANCELED': // 取消
      case 'UNABLE': // 无法执行
      case 'WAITCOMPCHECK': // 待验收
        break;
      case 'APPROVED': // 待处理
        this.setState({
          itemProperty: {
            ...itemProperty,
            maintSiteId: disabledObj, // 服务区域
            woTypeId: disabledObj, // 工单类型
            actId: disabledObj, // 标准作业活动
          },
        });
        break;
      case 'INPRG': // 执行中
      case 'REWORKING':
        // case 'PAUSE': // 暂停作业(旧工单状态-可删除)
        // case 'WRD': // 需改派 (旧工单状态-可删除)
        this.setState({
          itemProperty: {
            ...itemProperty,
            maintSiteId: disabledObj, // 服务区域
            woTypeId: disabledObj, // 工单类型
            actId: disabledObj, // 标准作业活动
            assetId: disabledObj, // 设备
            assetLocationId: disabledObj, // 位置
            costObjectType: disabledObj, // 成本对象类型
            costObjectLov: disabledObj, // 成本对象
            waitingWoopownerFlag: disabledObj, // 抢单模式 接单明确工作单负责人
          },
        });
        break;
      default:
        break;
    }
  }

  /**
   * 根据工单类型(工单工作对象控制)控制 设备、位置 两个字段的 disabled，及 orgFlag 的值
   * @param {*} record 工单类型明细数据 清除选中的工单类型时 record={}
   * 注：老版中名为 handleChangeColumn
   */
  @Bind()
  setItemPropertyByWoType(record) {
    const { itemProperty } = this.state;
    // 设置ds中 currentWoType 便于动态控制 位置等字段的必输与disabled
    this.basicDataDs.current.set('currentWoType', record);
    if (record && Object.keys(record).length !== 0) {
      const {
        workObjCtrlCode,
        enableOrgFlag,
        woBasicType,
        showSourceFlag,
        scheduleRequirmentStatus,
      } = record || {};
      // 工单类型上的 工单工作对象控制（workObjCtrlCode）为 NOT_ALLOW (不允许输入) 时禁用 设备 及 位置，同时清空数据
      if (workObjCtrlCode === 'NOT_ALLOW') {
        this.basicDataDs.current.set('assetLocationName', null);
        this.basicDataDs.current.set('assetLocationId', null);
        this.basicDataDs.current.set('assetId', null);
        this.basicDataDs.current.set('assetNum', null);
        this.basicDataDs.current.set('descAndLabel', null);
      }

      // 当工单类型的 基础大类 是 检查类 时，一下字段是被隐藏的，所以去掉其值：
      // manuallySpecifyFlag手工指定联系人 orgId orgName客户/需求组织 contactId contactName需求方联系人 contactDesc联系人 phone联系电话
      // assetId assetName设备 assetLocationId assetLocationName位置  locationDesc位置补充说明  mapSourceCode地图来源
      // serviceAgreementId serviceAgreementName服务协议
      if (woBasicType === 'CHECKING_TYPE') {
        this.basicDataDs.current.set('manuallySpecifyFlag', null);
        this.basicDataDs.current.set('orgId', null); // 客户/需求组织
        this.basicDataDs.current.set('orgName', null);
        this.basicDataDs.current.init('contactLov'); // 需求方联系人
        this.basicDataDs.current.set('contactDesc', null);
        this.basicDataDs.current.set('assetLocationName', null);
        this.basicDataDs.current.set('assetLocationId', null);
        this.basicDataDs.current.set('assetId', null);
        this.basicDataDs.current.set('descAndLabel', null);
        this.basicDataDs.current.set('phone', null);
        this.basicDataDs.current.set('locationDesc', null);
        this.basicDataDs.current.set('mapSourceCode', null);
        this.basicDataDs.current.init('serviceAgreementLov');
      }
      // 当工单的类型的 计划状态 是 工单可直接执行 时，隐藏
      // durationScheduled计划工期 scheduledStartDate计划开始时间 scheduledFinishDate计划完成时间
      // targetStartDate目标开始时间 targetFinishDate目标完成时间
      if (scheduleRequirmentStatus === 'CANBE_EXECUTED_DIRECTLY') {
        this.basicDataDs.current.set('durationScheduled', null);
        this.basicDataDs.current.set('scheduledStartDate', null);
        this.basicDataDs.current.set('scheduledFinishDate', null);
        this.basicDataDs.current.set('targetStartDate', null);
        this.basicDataDs.current.set('targetFinishDate', null);
      }
      // 当工单的类型的 显示来源信息 未勾选时，隐藏：
      // reportOrgName reportOrgId报告人所在组织 reporterId reporterName报告人 reportDate报告时间
      // reportPriorityName reportPriorityId报告优先级
      if (!showSourceFlag) {
        this.basicDataDs.current.set('reportOrgName', null);
        this.basicDataDs.current.set('reportOrgId', null);
        this.basicDataDs.current.set('reportOrgType', null);
        this.basicDataDs.current.init('reporterLov');
        this.basicDataDs.current.set('reportDate', null);
        this.basicDataDs.current.set('reportPriorityName', null);
        this.basicDataDs.current.set('reportPriorityId', null);
      }

      // 页面默认可编辑工单工作对象控制（workObjCtrlCode）为 NOT_ALLOW (不允许输入) 时禁用 设备 及 位置
      this.setState({
        orgFlag: enableOrgFlag,
        woBasicType,
        showSourceFlag,
        scheduleRequirmentStatus,
        itemProperty:
          workObjCtrlCode === 'NOT_ALLOW'
            ? {
                ...itemProperty,
                assetId: { disabled: true }, // 设备
                assetLocationId: { disabled: true }, // 位置
              }
            : {
                ...itemProperty,
                // assetId: {}, // 设备
                // assetLocationId: {}, // 位置
              },
      });
    } else {
      this.setState({
        orgFlag: false,
        woBasicType: null,
        showSourceFlag: 0,
        scheduleRequirmentStatus: null,
        itemProperty: {
          ...itemProperty,
          // 工单类型 在 新建、拟定 等待安排 三种状态下才可编辑，此三种状态下，设备及位置也是允许编辑的
          assetId: { disabled: false }, // 设备
          assetLocationId: { disabled: false }, // 位置
        },
      });
    }
  }

  /**
   * 保存明细
   * @param {string} saveType 值为'saveAndGoToEdit':保存并且切换至编辑状态
   */
  @Bind()
  async handleSave(saveType = '') {
    const {
      location: { search },
      match: { params },
    } = this.props;
    const { woId } = params;
    const isNew = isUndefined(woId);
    const searchItem = queryString.parse(search.substring(1)) || {};
    const {
      sourceParamType,
      srListstr,
      srId,
      assetId,
      evalItemId,
      partCodeId,
      riskCodeId,
      faultDate,
      description,
    } = searchItem;

    // 服务申请单创建工单时，使用sourceSrIds，不用sourceId
    let sourceSrIds = [];
    if (isNew && !isUndefined(sourceParamType) && sourceParamType === 'SR') {
      sourceSrIds = srListstr ? JSON.parse(srListstr).map(i => i.srId) : [srId];
    }

    const result = await this.basicDataDs.validate();
    if (result) {
      const data = this.basicDataDs.current.toData();

      const isCopy = isNew && !isUndefined(sourceParamType) && sourceParamType === 'COPYWO';

      let body = {
        ...omit(data, noUseFieldsArr),
        tenantId: organizationId,
        mapType: 'BaiduMap', // 地图类型：百度地图
        sourceSrIds,
        editProjectFlag: data.woStatus === 'COMPLETED', // 工单完成时 传ture 避免后端接口中会多走一个完工逻辑让工单变结束
      };

      if (isCopy && data.needCopyTabs) {
        const needCopyTabs = data.needCopyTabs.split(',');
        body = {
          ...body,
          ...needCopyTabs.reduce((acc, curr) => {
            return {
              ...acc,
              [curr]: true,
            };
          }, {}),
        };
      }

      // 筛选出新增的（newRecord）关联（canRemoveFlag为true）服务申请单
      const sourceDsRecords = this.sourceDs.records.map(i => i.toData());
      const relatedSrs = sourceDsRecords.filter(i => {
        const { sourceType, newRecord, canRemoveFlag } = i;
        return sourceType === 'SR' && canRemoveFlag && newRecord;
      });

      this.setState({ saveLoading: true });

      const fun = _body => (isCopy ? saveWoCopyService(_body) : saveWoService(_body));

      return new Promise((resolve, reject) => {
        fun(body).then(async res => {
          if (res && !res.failed) {
            notification.success();
            // 非新建
            if (!isNew) {
              if (this.state.editFlag && relatedSrs.length > 0) {
                // 如果是编辑，还需要判断是否有新增的关联服务申请单需要保存
                request(updateRelateSrUrl, {
                  method: 'POST',
                  body: relatedSrs.map(i => ({ sourceId: i.sourceId, woId: Number(woId) })),
                }).then(updateRes => {
                  if (updateRes && !updateRes.failed) {
                    this.refreshBasicAndCurrentTab();
                  }
                });
              } else {
                this.refreshBasicAndCurrentTab();
              }
            } else {
              // 从服务申请详情创建的工单，若存在故障保存故障
              if (evalItemId) {
                await request(woMalfunctionListUrl, {
                  method: 'POST',
                  body: [
                    {
                      assetId,
                      partCodeId,
                      evalItemId,
                      riskCodeId,
                      malfunctionTime: faultDate,
                      remark: description, // 服务申请的描述带入记录备注
                      srId,
                      woId: res.woId,
                    },
                  ],
                });
              }
              // 列表页可能多个sr合并创建工单 也需要保存故障
              const existEval = srListstr ? JSON.parse(srListstr).some(i => !!i.evalItemId) : false;
              if (existEval) {
                await request(woMalfunctionListUrl, {
                  method: 'POST',
                  body: JSON.parse(srListstr).map(i => ({
                    assetId,
                    partCodeId: i.partCodeId,
                    evalItemId: i.evalItemId,
                    riskCodeId: i.riskCodeId,
                    malfunctionTime: i.faultDate,
                    remark: i.description, // 服务申请的描述带入记录备注
                    srId: i.srId,
                    woId: res.woId,
                  })),
                });
              }

              this.props.history.push({
                pathname: `/amtc/work-order/detail/${res.woId}/${res.woNum}`,
                state: { isEdit: saveType === 'saveAndGoToEdit' },
              });
            }
            resolve();
          } else {
            notification.error({
              message: res.message,
            });
            reject();
          }
          this.setState({ saveLoading: false });
        });
      });
    }
  }

  /**
   * 编辑
   */
  @Bind()
  async handleEdit() {
    const { editFlag, maintSitesIds } = this.state;
    if (editFlag) {
      // 切换到查看状态 要重置ds 清空改动
      this.basicDataDs.reset();
    } else if (Object.keys(maintSitesIds).length === 0) {
      // 切换到编辑状态
      const res = await this.getMaintSitesOfCurrentUser(); // 获取当前用户对应员工所属组织下的服务区域
      this.setState({
        maintSitesIds: res,
      });
    }
    this.setState({ editFlag: !editFlag });
  }

  /**
   * 跳转至明细页 - 供左侧查询框使用
   * @param {string} id - id
   */
  @Bind()
  handleGotoDetail(id, num) {
    this.props.history.push({
      pathname: `/amtc/work-order/detail/${id}/${num}`,
    });
    this.infoExhibitRef?.current?.handleTabsChange?.('basicTab'); // eslint-disable-line
  }

  @Bind
  handleIgnoreChecklist(params, callback) {
    request(ignoreUrl, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && res.failed) {
        notification.warning({ message: res.message });
        return Promise.reject();
      } else {
        callback(res);
      }
    });
  }

  // 刷新基本tab和当前Tab
  @Bind
  async refreshBasicAndCurrentTab() {
    const { editFlag, showSearchFlag } = this.state;
    const func = [];

    // 若左侧展开，重新查询左侧搜索框数据
    if (showSearchFlag) {
      func.push(this.refreshLeftData());
    }
    // 查询基本信息
    func.push(this.handleRefreshBasic());

    // 刷新当前Tab,第二个参数禁止调用两次工单详情按钮接口
    func.push(this.infoExhibitRef.current.handleTabsChange('', true));
    await Promise.all(func);

    if (editFlag) {
      this.setState({
        editFlag: false,
      });
    }
  }

  // 左侧搜索栏数据查询
  @Bind
  refreshLeftData() {
    return this.leftRef?.current?.handleSearch?.(0, 10, true);
  }

  // ---------------  改派 start ---------------

  /**
   * @param type 类型 工单/任务
   * @param woopId 任务Id
   */
  @Bind
  handleOpenWrdModal(type, woopId = null) {
    Modal.open({
      key: modalKey,
      destroyOnClose: true,
      closable: true,
      style: {
        width: 800,
      },
      title: getLang('ASSIGN'),
      children: this.getWrdModalChildren(type, woopId),
      onOk: () => this.handleWrdModalOk(type, woopId),
    });
  }

  // 工单负责人-负责人组改变
  @Bind
  handleChangeOwnerGroup(e) {
    this.wrdSearchDs.current.set('ownerGroupId', e.workCenterId);
    this.wrdSearchDs.current.set('ownerGroupName', e.workCenterName);
    this.wrdSearchDs.current.set('ownerId', e.employeeId);
    this.wrdSearchDs.current.set('ownerName', e.employeeName);
  }

  // 工单负责人-负责人组改变
  @Bind
  handleChangeOwner(e) {
    if (e) {
      this.handleChangeOwnerGroup(e);
    } else {
      // 清空时，只清空自己
      this.wrdSearchDs.current.set('ownerId', e.employeeId);
      this.wrdSearchDs.current.set('ownerName', e.employeeName);
    }
  }

  // 工序负责人-行-负责人组改变
  @Bind
  handleChangeLineOwnerGroup(e, record) {
    record.set('ownerGroupId', e.workCenterId);
    record.set('ownerGroupName', e.workCenterName);
    record.set('ownerId', e.employeeId);
    record.set('ownerName', e.employeeName);
  }

  // 工序负责人-行-负责人组改变
  @Bind
  handleChangeLineOwner(e, record) {
    if (e) {
      this.handleChangeLineOwnerGroup(e, record);
    } else {
      // 清空时，只清空自己
      record.set('ownerId', e.employeeId);
      record.set('ownerName', e.employeeName);
    }
  }

  @Bind
  getWrdModalChildren(type, woopId) {
    const {
      match: { params },
    } = this.props;
    const { woId } = params;
    const detail = this.basicDataDs?.current?.toData?.() || {};
    const {
      woBasicType,
      ownerGroupId,
      ownerGroupName,
      ownerId,
      ownerName,
      maintSiteId,
      scheduledStartDate,
      scheduledFinishDate,
      currentWoType = {},
    } = detail;

    const { scheduleRequirmentStatus } = currentWoType;

    this.wrdColumns = [
      {
        name: 'woopNum',
        width: 80,
      },
      {
        name: 'ownerGroupName',
        editor: record => {
          return (
            <EmployeesLov
              name="ownerGroupName"
              value={record.get('ownerGroupName')}
              record={record}
              queryParams={{
                maintSiteId,
              }}
              onOk={this.handleChangeLineOwnerGroup}
            />
          );
        },
      },
      {
        name: 'ownerName',
        editor: record => {
          return (
            <EmployeesLov
              name="ownerName"
              value={record.get('ownerName')}
              record={record}
              queryParams={{
                maintSiteId,
              }}
              onOk={this.handleChangeLineOwner}
            />
          );
        },
      },
      {
        name: 'woopStatusMeaning',
        align: 'center',
        width: 90,
        renderer: ({ record }) => {
          const { woopStatus, woopStatusMeaning } = record.toData();
          return (
            <Tag
              style={{
                color: (statusColors[woopStatus] && statusColors[woopStatus].fontColor) || '#000',
                border: 0,
                marginRight: 0,
              }}
              color={(statusColors[woopStatus] && statusColors[woopStatus].bgColor) || '#fff'}
            >
              {woopStatusMeaning}
            </Tag>
          );
        },
      },
      {
        name: 'scheduledStartDate',
        width: 150,
        editor: true,
      },
      {
        name: 'scheduledFinishDate',
        width: 150,
        editor: true,
      },
    ];

    this.wrdSearchDs = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'ownerGroupId',
          type: 'number',
        },
        {
          name: 'ownerGroupName',
          type: 'string',
          label: intl.get(`${modelPrompt}.ownerGroup`).d('负责人组'),
        },
        {
          name: 'ownerId',
          type: 'number',
        },
        {
          name: 'ownerName',
          type: 'string',
          label: intl.get(`${modelPrompt}.owner`).d('负责人'),
        },
        {
          name: 'scheduledStartDate',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.scheduledStartDate`).d('计划开始时间'),
          max: 'scheduledFinishDate',
          dynamicProps: {
            required: () => {
              // 工单基本类型是保养时必输
              return woBasicType === 'FAULT_MAINTAIN_TYPE';
            },
          },
        },
        {
          name: 'scheduledFinishDate',
          min: 'scheduledStartDate',
          type: 'dateTime',
          label: intl.get(`${modelPrompt}.scheduledFinishDate`).d('计划完成时间'),
          dynamicProps: {
            required: () => {
              // 工单基本类型是保养时必输
              return woBasicType === 'FAULT_MAINTAIN_TYPE';
            },
          },
        },
      ],
    });
    // 初始化wrdSearchDs的值：工单上的负责人、组
    this.wrdSearchDs.current.set('ownerGroupId', ownerGroupId);
    this.wrdSearchDs.current.set('ownerGroupName', ownerGroupName);
    this.wrdSearchDs.current.set('ownerId', ownerId);
    this.wrdSearchDs.current.set('ownerName', ownerName);
    this.wrdSearchDs.current.set('scheduledStartDate', scheduledStartDate);
    this.wrdSearchDs.current.set('scheduledFinishDate', scheduledFinishDate);

    this.wrdTableDs = new DataSet({
      ...wrdWoopListDs(),
      paging: false,
      selection: false,
    });
    // 工单改派：查询工单下所有需改派状态的任务；任务改派：仅查询当前任务本身
    this.wrdTableDs.setQueryParameter('woId', woId);
    if (type === 'WO') {
      this.wrdTableDs.setQueryParameter('excludeWoopStatusList', [
        'CANCELED',
        'UNABLE',
        'COMPLETED',
      ]);
      this.wrdTableDs.query();
    } else if (type === 'WOOP') {
      this.wrdTableDs.setQueryParameter('woopId', woopId);
      this.wrdTableDs.query();
    }
    return (
      <div>
        <div style={{ margin: '8px 0' }}>{intl.get(`${viewPrompt}.woOwner`).d('工单负责人')}</div>
        <Form dataSet={this.wrdSearchDs} labelWidth={120} columns={2}>
          <EmployeesLov
            name="ownerGroupName"
            queryParams={{ maintSiteId }}
            onOk={this.handleChangeOwnerGroup}
          />
          <EmployeesLov
            name="ownerName"
            queryParams={{ maintSiteId }}
            onOk={this.handleChangeOwner}
          />
          {/* 计划状态是“工单可直接执行”不显示计划开始/结束时间 */}
          {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
            <DateTimePicker name="scheduledStartDate" />
          )}
          {scheduleRequirmentStatus !== 'CANBE_EXECUTED_DIRECTLY' && (
            <DateTimePicker name="scheduledFinishDate" />
          )}
        </Form>
        <div style={{ margin: '8px 0' }}>{intl.get(`${viewPrompt}.woopOwner`).d('工序负责人')}</div>
        <Table
          key="woWoopOwner"
          customizedCode="AORI.WORK_ORDER.WOOP_OWNER"
          dataSet={this.wrdTableDs}
          columns={this.wrdColumns}
        />
      </div>
    );
  }

  /**
   * 改派弹窗确定
   */
  @Bind()
  async handleWrdModalOk(type) {
    const detail = this.basicDataDs?.current?.toData?.() || {};
    const newDetail = this.wrdSearchDs?.current?.toData?.() || {};
    const woopList = this.wrdTableDs?.toData?.() || [];

    if (!(await this.wrdSearchDs.validate())) return false;

    if (type === 'WO') {
      updateWoStatusService({
        url: 'assign',
        woId: detail.woId,
        woNum: detail.woNum,
        _token: detail._token,
        ownerGroupId: newDetail.ownerGroupId,
        ownerId: newDetail.ownerId,
        scheduledStartDate: newDetail.scheduledStartDate,
        scheduledFinishDate: newDetail.scheduledFinishDate,
        objectVersionNumber: detail.objectVersionNumber,
        woopWrdDTOList: woopList,
      }).then(async res => {
        if (res && res.failed) {
          notification.warning({ message: res.message });
          return Promise.reject();
        } else {
          notification.success();
          this.refreshBasicAndCurrentTab();
        }
      });
    } else if (type === 'WOOP') {
      this.infoExhibitRef.current?.woopListRef?.current?.changeWoopStatus('assign', {
        woId: detail.woId,
        woNum: detail.woNum,
        _token: detail._token,
        ownerGroupId: newDetail.ownerGroupId,
        ownerId: newDetail.ownerId,
        scheduledStartDate: newDetail.scheduledStartDate,
        scheduledFinishDate: newDetail.scheduledFinishDate,
        woopWrdDTOList: woopList,
      });
    }
  }

  // ---------------  改派 end ---------------

  // ---------------  任务tab start ---------------

  // 查询任务（工序）列表数据
  @Bind
  async handleSearchWoopList(page = 1, size = 10) {
    const {
      match: { params },
    } = this.props;
    const { woId } = params;
    this.setState({
      woopListLoading: true,
    });
    this.woopListDs.pageSize = size;
    this.woopListDs.setQueryParameter('woId', woId);
    return new Promise(async resolve => {
      const res = await this.woopListDs.query(page);
      const obj = {
        woopListPagenation: {
          page: res.number,
          size: res.size,
          total: res.totalElements,
        },
        woopListLoading: false,
      };
      this.setState(obj);
      resolve();
    });
  }

  // 翻页
  @Bind()
  handleWoopListPageChange(page, size) {
    this.setState({
      woopListPagenation: {
        ...this.state.woopListPagenation,
        page: page - 1,
        size,
      },
    });
    this.handleSearchWoopList(page, size);
  }
  // ---------------  任务tab end ---------------

  @Bind
  handleCopyWo() {
    const {
      match: { params },
    } = this.props;
    const { woId } = params;
    queryWoDetailService(woId).then(res => {
      if (res && !res.failed) {
        const woDetailData = handleResponseResult(res);
        const {
          woChecklistFlag,
          woMaterialFlag,
          copyServiceFlag,
          woMalfunctionFlag,
          showFailureFlag,
          woTypeDTO,
          projectId,

          assetLocationName,
          assetName,
          costObject,
          costObjectMeaning,

          woTypeDTO: { defaultCostObjectType },
        } = woDetailData;
        let {
          costCenterId,
          internalOrderId,
          wbsElementId,
          costCenterMeaning,
          internalOrderMeaning,
          wbsElementMeaning,
        } = woDetailData;
        // 在没有资产和位置的时候，用./infoExhibit.js中handleChangeCostObjOfLocOrAsset中的逻辑给成本对象赋值
        if (!assetLocationName && !assetName) {
          switch (defaultCostObjectType) {
            case 'COST_CENTER':
              costCenterId = costObject;
              costCenterMeaning = costObjectMeaning;
              break;
            case 'INTERNAL_ORDER':
              internalOrderId = costObject;
              internalOrderMeaning = costObjectMeaning;
              break;
            case 'WBS_ELEMENT':
              wbsElementId = costObject;
              wbsElementMeaning = costObjectMeaning;
              break;
            default:
          }
        }

        const { checklistsFlag, itemsFlag, billingInfoFlag } = woTypeDTO || {};
        const defaultArr = [];
        if (woChecklistFlag && checklistsFlag) {
          defaultArr.push('woChecklistFlag');
        }
        if (woMaterialFlag && itemsFlag) {
          defaultArr.push('woMaterialFlag');
        }
        if (copyServiceFlag && billingInfoFlag) {
          defaultArr.push('woServiceFlag');
        }
        if (woMalfunctionFlag && showFailureFlag) {
          defaultArr.push('woMalfunctionFlag');
        }
        if (projectId) {
          defaultArr.push('projectId');
        }

        const hasTab = defaultArr.length > 0;
        const copyDs = new DataSet({
          autoCreate: true,
          fields: [{ name: 'needCopyTabs', multiple: true, defaultValue: defaultArr }],
        });

        Modal.confirm({
          title: intl.get(`${viewPrompt}.title.modal.comfirmNeedCopyInfo`).d('确认复制信息'),
          children: hasTab ? (
            <React.Fragment>
              {woChecklistFlag && checklistsFlag && (
                <CheckBox
                  dataSet={copyDs}
                  name="needCopyTabs"
                  value="woChecklistFlag"
                  defaultChecked
                >
                  {' '}
                  {intl.get(`${viewPrompt}.button.checklist`).d('检查项')}
                </CheckBox>
              )}
              {woMaterialFlag && itemsFlag && (
                <CheckBox
                  dataSet={copyDs}
                  name="needCopyTabs"
                  value="woMaterialFlag"
                  defaultChecked
                >
                  {intl.get(`${viewPrompt}.tab.material`).d('物料')}
                </CheckBox>
              )}
              {copyServiceFlag && billingInfoFlag && (
                <CheckBox dataSet={copyDs} name="needCopyTabs" value="woServiceFlag">
                  {intl.get(`${viewPrompt}.button.serviceInfo`).d('结算信息')}
                </CheckBox>
              )}
              {woMalfunctionFlag && showFailureFlag && (
                <CheckBox dataSet={copyDs} name="needCopyTabs" value="woMalfunctionFlag">
                  {intl.get(`${viewPrompt}.button.malInfo`).d('故障信息')}
                </CheckBox>
              )}
              {projectId && (
                <CheckBox dataSet={copyDs} name="needCopyTabs" value="projectId">
                  {intl.get(`${viewPrompt}.button.projectInfo`).d('项目信息')}
                </CheckBox>
              )}
            </React.Fragment>
          ) : (
            intl.get(`${viewPrompt}.title.modal.comfirmCopy`).d('确认复制当前工作单？')
          ),
          onOk: () => {
            const copyDsData = copyDs.current.toData();
            const woData = this.basicDataDs.current.toData();
            const detail = {
              costCenterId,
              internalOrderId,
              wbsElementId,
              costCenterMeaning,
              internalOrderMeaning,
              wbsElementMeaning,

              maintSiteName: woData.maintSiteName,
              maintSiteId: woData.maintSiteId,

              woTypeId: woData.woTypeId,
              woTypeName: woData.woTypeName,
              woTypeCode: woData.woTypeCode,

              woName: woData.woName,

              description: woData.description,

              assetId: woData.assetId,
              descAndLabel: woData.descAndLabel,

              assetLocationId: woData.assetLocationId,
              assetLocationName: woData.assetLocationName,
              locationDesc: woData.locationDesc,

              mapSourceCode: woData.mapSourceCode,

              serviceAgreementId: woData.serviceAgreementId,
              agreementName: woData.agreementName,
              partnerName: woData.partnerName,
              agreementDescription: woData.agreementDescription,

              plannerGroupId: woData.plannerGroupId,
              plannerGroupName: woData.plannerGroupName,

              plannerId: woData.plannerId,
              plannerName: woData.plannerName,

              ownerGroupId: woData.ownerGroupId,
              ownerGroupName: woData.ownerGroupName,

              ownerId: woData.ownerId,
              ownerName: woData.ownerName,

              waitingWoopownerFlag: woData.waitingWoopownerFlag,

              priorityId: woData.priorityId,
              priorityName: woData.priorityName,

              durationScheduled: woData.durationScheduled,
              durationUom: woData.durationUom,
              durationUomMeaning: woData.durationUomMeaning,
              scheduledStartDate: woData.scheduledStartDate,
              targetStartDate: woData.targetStartDate,
              scheduledFinishDate: woData.scheduledFinishDate,
              targetFinishDate: woData.targetFinishDate,

              longitude: woData.longitude,
              latitude: woData.latitude,
              copyWoId: woData.woId,
            };

            detail.sourceParamType = 'COPYWO';
            // 由于url传参限制，将数组转为字符串
            if (copyDsData && copyDsData.needCopyTabs) {
              // 对 项目信息特殊处理
              const needCopyProject = copyDsData.needCopyTabs.includes('projectId');
              if (needCopyProject) {
                const projData = this.sourceDs.records
                  .map(item => item.toData())
                  .find(i => i.sourceType === 'Project');
                const names = projData.sourceName.split('——');
                const [projectName, taskName] = names;

                detail.taskCode = woData.taskCode;
                detail.projectId = woData.projectId;
                detail.taskName = taskName;
                detail.projectCode = projData.sourceNum;
                detail.projectName = projectName;
                detail.needCopyTabs = copyDsData.needCopyTabs
                  .filter(i => i !== 'projectId')
                  .join(',');
              } else {
                detail.needCopyTabs = copyDsData.needCopyTabs.join(',');
              }
            }
            this.props.history.push({
              pathname: `/amtc/work-order/create`,
              search: queryString.stringify(detail),
            });
          },
        });
      }
    });
  }

  // 验收通过/不通过
  @Bind
  async handleClickPassOrNo(passFlag) {
    const basicData = this.basicDataDs.current.toData();
    const { objectVersionNumber } = basicData;
    const inspectFormData = await this.createCheckRef?.current.handleSubmit();
    if (inspectFormData) {
      const { itemList, fileUrlList, moduleId } = inspectFormData;
      this.passModal = Modal.open({
        destroyOnClose: true,
        closable: true,
        title: '提示',
        style: {
          width: 305,
        },
        children: (
          <>
            {intl.get(`${viewPrompt}.inspectResult`).d('验收结果为')}：
            {passFlag ? (
              <span style={{ color: '#11D954' }}>
                {intl.get(`${viewPrompt}.button.pass`).d('通过')}
              </span>
            ) : (
              <span style={{ color: '#F23A50' }}>
                {intl.get(`${viewPrompt}.button.noPass`).d('不通过')}
              </span>
            )}
            ，{intl.get(`${viewPrompt}.whetherToSubmit`).d('是否提交')}？
          </>
        ),
        footer: (okBtn, cancelBtn) => [cancelBtn, okBtn],
        onOk: () => {
          this.checkModal.close();
          this.changeWoStatus('compcheck', {
            fileUrlList,
            checkItemList: itemList,
            passFlag,
            objectVersionNumber,
            fileModuleId: moduleId,
          });
        },
      });
    }
  }

  // 指定验收员
  @Bind
  assignChecker(i) {
    const basicData = this.basicDataDs.current.toData();
    const { woId, objectVersionNumber } = basicData;
    if (i?.employeeId) {
      this.setState({
        queryDeatilLoading: true,
      });
      request(assignCheckerUrl, {
        method: 'POST',
        body: {
          woId: Number(woId),
          checkerId: i.employeeId,
          objectVersionNumber,
        },
      }).then(res => {
        getResponse(res);
        if (res && !res.failed) {
          this.handleRefreshBasic();
        } else {
          this.setState({
            queryDeatilLoading: false,
          });
        }
      });
    } else {
      notification.warning({
        message: intl.get(`${viewPrompt}.chooseAssignChecker`).d('请选择验收员'),
      });
    }
  }

  // 验收
  @Bind
  check() {
    const { woTypeDetail, currentEmployee } = this.state;
    const { checkTempId } = woTypeDetail;
    const props = {
      mode: 'create',
      id: checkTempId,
      sourceTypeCode: 'WO',
      moduleId: Date.now(), // 新建验收记录，附件关联id为时间戳
      employeeId: currentEmployee?.employeeId,
    };
    this.checkModal = Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${viewPrompt}.modal.checkItems`).d('验收项'),
      children: <InspectForm {...props} ref={this.createCheckRef} />,
      footer: () => (
        <>
          <Button onClick={() => this.handleClickPassOrNo(0)}>
            {intl.get(`${viewPrompt}.button.noPass`).d('不通过')}
          </Button>
          <Button onClick={() => this.handleClickPassOrNo(1)} color="primary">
            {intl.get(`${viewPrompt}.button.pass`).d('通过')}
          </Button>
        </>
      ),
    });
  }

  // 打开验收记录
  @Bind
  openAcceptList() {
    const {
      match: { params },
    } = this.props;
    const { woId } = params;
    this.checkRecordDs.setQueryParameter('sourceId', Number(woId));
    this.checkRecordDs.setQueryParameter('sourceTypeCode', 'WO');
    this.checkRecordDs.query();

    const checkRecordModal = Modal.open({
      destroyOnClose: true,
      closable: true,
      drawer: true,
      style: {
        width: 800,
      },
      title: intl.get(`${viewPrompt}.modal.checkRecord`).d('验收记录'),
      children: (
        <Table
          key="woCheckRecord"
          customizedCode="AORI.WORK_ORDER.CHECK_RECORD"
          dataSet={this.checkRecordDs}
          columns={this.checkRecordColumns}
        />
      ),
      footer: okBtn => okBtn,
      onOk: () => checkRecordModal.close(),
    });
  }

  // 打开验收项
  @Bind
  openCheckModal(id, fileModuleId) {
    const { currentEmployee } = this.state;
    const props = {
      mode: 'view',
      id,
      sourceTypeCode: 'WO',
      moduleId: fileModuleId,
      employeeId: currentEmployee?.employeeId,
    };

    Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${viewPrompt}.modal.checkItems`).d('验收项'),
      children: <InspectForm {...props} />,
      footer: okBtn => okBtn,
    });
  }

  /**
   * 状态修改按钮点击事件
   */
  @Bind()
  changeStatusBtnClick(url, params) {
    const { woId } = this.basicDataDs?.current?.toData?.() || {};
    switch (url) {
      case 'compcheck':
        this.check();
        break;
      case 'assign':
        // 改派弹窗显示
        this.handleOpenWrdModal('WO');
        break;
      case 'cancel':
      case 'unable':
        // 无法执行、取消增加二次确认
        Modal.confirm({
          title:
            url === 'cancel'
              ? intl.get(`${viewPrompt}.message.confirmOfCancel`).d('确认取消当前工作单？')
              : intl.get(`${viewPrompt}.message.confirmOfUnable`).d('确认当前工作单无法执行？'),
        }).then(button => {
          if (button === 'ok') {
            request(`${HALM_MTC}/v1/${organizationId}/wo-material/valid-item-req`, {
              method: 'GET',
              query: {
                woId,
              },
            }).then(res => {
              if (res && res.failed) {
                notification.error({
                  message: res.message,
                });
              } else {
                this.clickUrl = url; // 点击的按钮url
                this.reasonModalRef.current.openRefuseReasonModal();
              }
            });
          }
        });
        break;
      default:
        this.changeWoStatus(url, params);
        break;
    }
  }

  // 触发状态变更逻辑
  @Bind
  changeWoStatus(url, params) {
    this.setState({
      queryDeatilLoading: true,
    });
    const detail = this.basicDataDs?.current?.toData?.() || {};
    const { woId, woNum, _token, objectVersionNumber } = detail;

    updateWoStatusService({ url, woId, woNum, _token, objectVersionNumber, ...params }).then(
      async res => {
        this.setState({
          queryDeatilLoading: false,
        });

        if (res && res.failed) {
          if (res.code === 'alm.error.ori.wo_material_incomplete_feeding') {
            Modal.confirm({
              title: res.message,
              onOk: () => {
                this.changeWoStatus('close', {
                  ignoreMaterialFlag: 1,
                  objectVersionNumber: objectVersionNumber + 1,
                });
              },
              onCancel: () => {
                this.refreshBasicAndCurrentTab();
              },
            });
          } else if (res.code === 'alm.error.ori.exist_approving_item_requisition') {
            Modal.warning({
              title: intl
                .get(`${viewPrompt}.cantClose`)
                .d('存在审批中的物料申请单，请撤回或完成审批操作后完工。'),
              onOk: () => {
                this.refreshBasicAndCurrentTab();
              },
            });
          } else if (
            [
              'alm.error.ori.all_eval_hierarchy_no_input',
              'alm.error.ori.eval_hierarchy_no_input',
            ].includes(res.code)
          ) {
            Modal.warning({
              title: res.message,
              onOk: this.refreshBasicAndCurrentTab,
            });
          } else if (
            [
              'alm.error.ori.all_wo_checklist_incomplete',
              'alm.error.ori.all_woop_checklist_incomplete',
            ].includes(res.code)
          ) {
            Modal.confirm({
              title: getLang('NOTICE'),
              children: res.message,
              onOk: () => {
                const ignoreType =
                  res.code === 'alm.error.ori.all_wo_checklist_incomplete' ? 'WO' : 'WOOP';
                this.handleIgnoreChecklist(
                  {
                    ignoreType,
                    woId: detail.woId,
                  },
                  () => {
                    this.changeWoStatus(url, params);
                  }
                );
              },
              onCancel: () => {
                // 跳转到 检查项-未完结检查项modal
                this.infoExhibitRef?.current?.handleTabsChange?.('checklistTab'); // eslint-disable-line
                setTimeout(() => {
                  this.infoExhibitRef?.current?.checklistRef?.current?.woChecklistRef?.showChecklist?.();
                }, 0);
              },
            });
          } else if (['alm.error.ori.cant_close_wo_before_create_relate'].includes(res.code)) {
            notification.warning({ message: res.message });
            this.refreshBasicAndCurrentTab();
          } else if (res.code === 'alm.error.ori.wo_default_checker_is_null') {
            Modal.warning({
              title: intl.get(`${viewPrompt}.remind`).d('提醒'),
              children: res.message,
              style: {
                width: 330,
              },
            }).then(() => {
              this.refreshBasicAndCurrentTab();
            });
          } else if (res.type === 'warn') {
            notification.warning({ message: res.message });
          } else {
            notification[res.type]({ message: res.message });
          }
        } else {
          notification.success();
          if (res?.allUnableOrCancelFlag) {
            this.clickUrl = url;
            this.reasonModalRef.current.openRefuseReasonModal();
          }
          this.refreshBasicAndCurrentTab();
        }
      }
    );
  }

  get btns() {
    const {
      match: { params },
      history,
    } = this.props;
    const { woId } = params;
    const isNew = isUndefined(woId);
    const { editFlag, showEditBtn, saveLoading, btnsLoadingCompleteFlag } = this.state;

    const detail = this.basicDataDs?.current?.toData?.() || {};

    const FileUploadButtonProps = {
      showContentFlag: true,
      woId,
      moduleId: detail?.fileModuleId,
      woNum: detail.woNum,
      woStatus: detail.woStatus,
    };

    // 隐藏 编辑按钮
    const hiddenEditFlag = isNew || editFlag || !showEditBtn;
    // 保存按钮： 新建/编辑显示
    const displayFlagBtn = isNew || editFlag;
    // 关闭按钮：编辑的时候显示
    const displayCloseBtn = !isNew && editFlag;

    // 显示 指定验收员
    const showAssignChecker = detail.assignCheckerFlag || detail.checkFlag;
    // 显示 验收记录
    const showCheckRecord = detail.checkRecordFlag;

    const { workflowFlag } = this.state.woTypeDetail;
    const displayApproveHistoryBtn =
      workflowFlag && detail.wkInstanceId && !isNew && !editFlag
        ? { display: 'block' }
        : { display: 'none' };

    const approveHistoryProps = {
      wkInstanceId: detail.wkInstanceId,
      history,
    };

    return [
      <Button onClick={() => this.handleSave('saveAndGoToEdit')} key="next" hidden={!isNew}>
        {intl.get(`${viewPrompt}.button.nextStep`).d('下一步')}
      </Button>,
      <Button
        loading={saveLoading}
        icon="save"
        color="primary"
        onClick={this.handleSave}
        key="save"
        hidden={!displayFlagBtn}
        waitType="throttle"
        wait={1000}
        style={{ marginLeft: 8 }}
      >
        {intl.get(`hzero.common.button.save`).d('保存')}
      </Button>,
      <Button color="primary" onClick={this.handleEdit} key="edit" hidden={hiddenEditFlag}>
        {intl.get('hzero.common.button.edit').d('编辑')}
      </Button>,
      !editFlag && btnsLoadingCompleteFlag ? [...this.statusBtns] : null,
      <Button
        icon="close"
        onClick={this.handleEdit}
        hidden={!displayCloseBtn}
        style={{ marginLeft: 8 }}
      >
        {intl.get('hzero.common.button.close').d('关闭')}
      </Button>,
      isNew ? null : <FileUploadButton {...FileUploadButtonProps} />, // 新建时不可上传
      <Button onClick={this.handleCopyWo} hidden={isNew || editFlag}>
        {intl.get(`${viewPrompt}.button.copyWo`).d('复制工单')}
      </Button>,
      <ApproveHistoryBtn style={displayApproveHistoryBtn} {...approveHistoryProps} />,
      // 指定验收员
      !!showAssignChecker && (
        <Lov
          dataSet={this.basicDataDs}
          className={styles['btn-with-custom-icon']}
          name="checkerLov"
          mode="button"
          clearButton={false}
          onChange={this.assignChecker}
        >
          <img src={assignCheckerIcon} alt="" />
          {intl.get(`${viewPrompt}.button.designateAChecker`).d('指定验收员')}
        </Lov>
      ),
      // 验收记录
      !!showCheckRecord && (
        <Button onClick={this.openAcceptList} className={styles['btn-with-custom-icon']}>
          <img src={checkRecordIcon} alt="" />
          {intl.get(`${viewPrompt}.modal.checkRecord`).d('验收记录')}
        </Button>
      ),
    ];
  }

  @Bind
  async handleOkReasonModal(value) {
    const detail = this.basicDataDs.current.toData() || {};
    const { woId, _token, objectVersionNumber } = detail;
    if (['cancel', 'unable'].includes(this.clickUrl)) {
      this.changeWoStatus(this.clickUrl, { closingReason: value });
    } else {
      await updateWoCloseReasonService({
        _token,
        objectVersionNumber,
        closingReason: value,
        woId,
      });
      this.refreshBasicAndCurrentTab();
    }

    this.clickUrl = null; // 清空存储的“点击的按钮状态”
  }

  /**
   * 关闭原因的渲染
   */
  @Bind()
  reasonRender() {
    const { editFlag } = this.state;
    const reason = this.basicDataDs.current.get('closingReason');
    return (
      <div className={styles['content-right-middle']} style={editFlag ? { marginTop: 0 } : {}}>
        <Output
          name="denialReason"
          tooltip="none"
          renderer={() => {
            return (
              <div>
                <div className={styles['right-middle-desc']}>
                  <img src={closeIcon} alt="" />
                  <span>{intl.get(`${viewPrompt}.closingReason`).d('关闭原因')}</span>
                </div>
                <div className={styles['right-middle-content']}>{reason}</div>
              </div>
            );
          }}
        />
      </div>
    );
  }

  @Bind()
  handleSetBtnCfg(res) {
    this.setState({
      btnDisplayCfg: res,
      showEditBtn: res?.staticButtonList?.includes('editWo'),
      disableAllByStatus: !res?.staticButtonList?.includes('editWo'),
    });
  }

  render() {
    const {
      location: { search },
      match: { params },
      history,
    } = this.props;
    const { woId } = params;
    const isNew = isUndefined(woId);
    const searchItem = queryString.parse(search.substring(1)) || {};
    const {
      tsData,
      editFlag,
      showSearchFlag,
      orgFlag,
      outsourceFlag,
      isOpenPerson,
      queryDeatilLoading,
      currentUser,
      currentEmployee,
      maintSitesIds,
      itemProperty,
      woopListLoading,
      woopListPagenation,
      isOpenCheck,
      showFailureFlag,
      costFlag,
      billingInfoFlag,
      woBasicType,
      showSourceFlag,
      scheduleRequirmentStatus,
      showEditBtn,
      disableAllByStatus,
      isOpenMaterial,
      handleRecord,
      btnDisplayCfg,
    } = this.state;
    const headerData = !isNew && this.basicDataDs?.current ? this.basicDataDs.current.toData() : {}; // 头部数据 查看界面使用

    const {
      woName,
      woNum,
      woStatus,
      woStatusMeaning,
      ownerName,
      woTypeName,
      maintSiteName,
      description,
      closingReason,
    } = headerData;

    const detailFormProps = {
      woId,
      woNum,
      isNew,
      editFlag,
      orgFlag,
      tsData,
      outsourceFlag,
      isOpenPerson,
      itemProperty,
      searchItem,
      currentUser,
      currentEmployee,
      maintSitesIds,
      woopListLoading,
      woopListPagenation,
      history,
      isOpenCheck,
      billingInfoFlag,
      woBasicType,
      showSourceFlag,
      scheduleRequirmentStatus,
      showFailureFlag,
      costFlag,
      showEditBtn,
      disableAllByStatus,
      isOpenMaterial,
      handleRecord,
      tenantId: organizationId,
      sourceDs: this.sourceDs,
      basicDataDs: this.basicDataDs,
      woopListDs: this.woopListDs,
      btnDisplayCfg,
      onSearchWoopList: this.handleSearchWoopList, // 查询任务列表
      onRefreshBasicAndCurrentTab: this.refreshBasicAndCurrentTab,
      onWoopListPageChange: this.handleWoopListPageChange,
      onSetState: this.handleSetState, // 设置state变量
      onRefresh: this.handleRefreshBasic,
      onsetItemPropertyByWoType: this.setItemPropertyByWoType,
      onIgnoreChecklist: this.handleIgnoreChecklist,
      onOpenWrdModal: this.handleOpenWrdModal,
      onHandleRecord: this.setHandleRecord,
      onChangeWoStatus: this.changeWoStatus,
      onHandleStaffCallBack: this.handleStaffCallBackBind,
      onSetBtnCfg: this.handleSetBtnCfg,
    };

    // 左侧搜索栏参数
    const fullTextSearchProps = {
      isNew,
      showSearchFlag,
      currentWoId: woId,
      onSetState: this.handleSetState,
      onSetShowSearchFlag: this.showLeftSearch,
      onGotoDetail: this.handleGotoDetail,
    };

    const HeaderInfoProps = {
      woName,
      woStatus,
      woStatusMeaning,
      description,
      woNum,
      rightTopBottom: [
        {
          name: intl.get(`${modelPrompt}.maintSite`).d('服务区域'),
          value: maintSiteName,
        },
        {
          name: intl.get(`${modelPrompt}.woType`).d('工单类型'),
          value: woTypeName,
        },
        {
          name: intl.get(`${modelPrompt}.owner`).d('负责人'),
          value: ownerName,
        },
      ],
      btns: this.btns,
    };

    const reasonModalProps = {
      title: intl.get(`${viewPrompt}.fillInTheReason2`).d('单据即将关闭，请填写原因'),
      onOk: this.handleOkReasonModal,
      onCancel: this.handleOkReasonModal,
    };

    const woLayoutProps = {
      isNew,
      editFlag,
      showSearchFlag,
      queryDeatilLoading,
      header: (
        <Header
          title={intl.get(`${viewPrompt}.title.detail`).d('工作单')}
          backPath="/amtc/work-order/list"
        >
          {isNew && this.btns}
        </Header>
      ),
      left: <FullTextSearch ref={this.leftRef} {...fullTextSearchProps} />,
      rightTop: <HeaderInfo {...HeaderInfoProps} />,
      rightMiddle: closingReason ? this.reasonRender() : null,
      rightContent: (
        <>
          <ReasonModal {...reasonModalProps} ref={this.reasonModalRef} />
          <InfoExhibit {...detailFormProps} ref={this.infoExhibitRef} />
        </>
      ),
    };

    return (
      <ModalProvider getContainer={false}>
        <WoLayout {...woLayoutProps} />
      </ModalProvider>
    );
  }
}
