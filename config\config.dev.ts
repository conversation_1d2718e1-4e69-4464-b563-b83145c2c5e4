/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-14 10:18:31
 * @LastEditTime: 2023-06-15 11:52:21
 * @LastEditors: <<EMAIL>>
 */
import { IConfig } from 'umi'; // ref: https://umijs.org/config/

export default {
  define: {
    'process.env': {
      // API_HOST: 'https://qmshzerotestgw.ysyec.com',
      API_HOST: 'https://hzeropregw.ysyec.com',
      CUSZ_CODE_BEFORE: 'KEY_FOCUS', // 个性化单元前缀
      LOV_CODE_BEFORE: 'MT', // lov值集前缀
      HMES_BASIC: '/inja-qms-mes',
      TARZAN_COMMON: '/inja-qms-tznc',
      HRPT_COMMON: '/hrpt',
      TARZAN_MODEL: '/inja-qms-tznm',
      TARZAN_REPORT: '/inja-qms-tznr',
      TARZAN_METHOD: '/inja-qms-tznd',
      TARZAN_SAMPLING: '/inja-qms-tznq',
      TARZAN_HSPC: '/inja-qms-tzns',
      TARZAN_MONGO: '/tzng',
      TARZAN_HOHR: '/inja-qms-mes',
      ADDITIONAL: process.env.ADDITIONAL,
      PLATFORM_VERSION: 'SAAS',
      CLIENT_ID: 'localhost',
      BASE_PATH: '/',
      MULTIPLE_SKIN_ENABLE: true,
      SKIP_NO_CHANGE_MODULE: true,
      REACT_APP_SC_DISABLE_SPEEDY: 'false',
      PACKAGE_PUBLIC_URL: '',
      SKIP_TS_CHECK_IN_START: true,
      // aps
      POOL_QUERY: 'query',
      // 服务的请求url前缀
      APS_COMMON: '/inja-qms-tznc',
      APS_METHOD: '/aps',
      APS_METHODTZND: '/tznd',
      BASE_SERVER: '/key-focus-aps',
      BASE_SERVERPURCHASE: '/aps-purchase',
      BASE_SERVERPLAN: '/key-focus-aps-mltp',
      TARZAN_METHODTZND: '/tznd',
      // ALM服务请求url前缀
      API_PREFIX: '/aori',
    },
  },
} as IConfig;
