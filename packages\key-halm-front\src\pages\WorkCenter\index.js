/**
 * WorkCenter - 工作中心
 * @since 2021-02-24
 * <AUTHOR>
 * @version: 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import React, { Component } from 'react';
import { DataSet, Button, Table, Spin, Icon } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import StaffLov from 'alm/components/StaffLov';
import { connect } from 'dva';
import request from 'utils/request';
import withProps from 'utils/withProps';
import classNames from 'classnames';
import { routerRedux } from 'dva/router';
import { Bind } from 'lodash-decorators';
import { enableRender } from 'utils/renderer';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { HALM_MTC } from 'alm/utils/config';

import { tableDS } from './Stores/WorkCenterDS';
import styles from './index.module.less';
import getLangs from './Langs';

@formatterCollections({
  code: ['alm.common', 'alm.component', 'amtc.workCenter'],
})
@withProps(
  () => {
    const listDS = new DataSet(tableDS());
    return {
      listDS,
    };
  },
  { cacheState: true, cleanWhenClose: false, keepOriginDataSet: true }
)
@connect(() => ({
  tenantId: getCurrentOrganizationId(),
}))
class WorkCenter extends Component {
  constructor(props) {
    super(props);
    this.state = {
      queryEmpObj: null,
    };
  }

  /**
   * 跳转到详情页
   * @param {number} workCenterId 工作中心ID
   * @param {boolean} flag 是否编辑
   * @param {object} parentInfo 上级信息
   */
  @Bind()
  handleGoToDetail(workCenterId, flag, parentInfo) {
    const { dispatch } = this.props;
    // 获取查询条件员工信息
    const { queryEmpObj } = this.state;
    const pathname = workCenterId
      ? `/amtc/work-center/detail/${workCenterId}`
      : '/amtc/work-center/create';
    dispatch(
      routerRedux.push({
        pathname,
        state: {
          isNew: !workCenterId && flag,
          editFlag: workCenterId && flag,
          parentInfo,
          empObj: !flag ? queryEmpObj : null,
        },
      })
    );
  }

  handleLoadData = ({ record, dataSet }) => {
    const { tenantId } = this.props;
    return new Promise(resolve => {
      if (record.get('childFlag') && !record.children) {
        record.setState('loading', true);
        // 查询其下全部子数据（不是子孙数据），只受 是否启用 条件限制，不受其他查询条件限制
        request(`${HALM_MTC}/v1/${tenantId}/workcenter`, {
          method: 'GET',
          query: {
            isExpand: 1,
            parentId: record.get('workcenterId'),
          },
        }).then(res => {
          if (res) {
            dataSet.appendData(res, record);
            resolve();
          }

          record.setState('loading', false);
        });
      } else {
        resolve();
      }
    });
  };

  @Bind()
  expandIcon({ prefixCls, expanded, expandable, record, onExpand }) {
    if (!record.get('childFlag')) {
      // 子结点渲染
      return <span style={{ paddingLeft: '0.18rem' }} />;
    }
    if (record.getState('loading') === true) {
      // 自定义状态渲染
      return <Spin tip="loading" delay={200} size="small" />;
    }
    const iconPrefixCls = `${prefixCls}-expand-icon`;
    const classString = classNames(iconPrefixCls, {
      [`${iconPrefixCls}-expanded`]: expanded,
    });
    return (
      <Icon
        type="baseline-arrow_right"
        className={classString}
        onClick={onExpand}
        tabIndex={expandable ? 0 : -1}
      />
    );
  }

  get columns() {
    return [
      {
        name: 'workcenterName',
        renderer: ({ value, record }) => (
          <a onClick={() => this.handleGoToDetail(record.get('workcenterId'), false)}>{value}</a>
        ),
      },
      {
        name: 'workcenterNum',
      },
      {
        name: 'maintSiteName',
      },
      {
        name: 'enabledFlag',
        width: 100,
        align: 'left',
        renderer: ({ value }) => enableRender(value),
      },
      {
        header: getLangs('OPTION'),
        width: 150,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              <a onClick={() => this.handleGoToDetail(record.get('workcenterId'), true)}>
                {getLangs('EDIT')}
              </a>
              <a onClick={() => this.handleGoToDetail(null, true, record.toData())}>
                {getLangs('ADD_CHILDREN')}
              </a>
            </span>
          );
        },
      },
    ];
  }

  // 人员查询条件生效后才带入详情界面
  @Bind()
  handleBeoreLoad({ dataSet }) {
    if (dataSet?.queryDataSet) {
      const { employeeId, employeeName } = dataSet.queryDataSet.current.toData();
      this.setState({
        queryEmpObj: {
          employeeId,
          employeeName,
        },
      });
    }
  }

  @Bind()
  handleQueryReset({ dataSet }) {
    // 重置查询条件时对人员Lov绑定的key字段清空
    // eslint-disable-next-line no-param-reassign
    dataSet.current.data.employeeId = null;
  }

  componentDidMount() {
    if (this.props?.listDS) {
      this.props.listDS.addEventListener('beforeLoad', this.handleBeoreLoad);
      this.props.listDS.queryDataSet.addEventListener('reset', this.handleQueryReset);
    }
  }

  componentWillUnmount() {
    if (this.props?.listDS) {
      this.props.listDS.removeEventListener('beforeLoad', this.handleBeoreLoad);
      this.props.listDS.queryDataSet.addEventListener('reset', this.handleQueryReset);
    }
  }

  render() {
    const { queryDataSet } = this.props?.listDS;
    return (
      <React.Fragment>
        <Header title={getLangs('WORK_CENTER_TITLE')}>
          <Button color="primary" icon="add" onClick={() => this.handleGoToDetail(null, true)}>
            {getLangs('CREATE')}
          </Button>
        </Header>
        <Content>
          <Table
            key="workCenterList"
            customizedCode="AORI.WORK_CENTER.LIST"
            mode="tree"
            rowHeight={40}
            className={styles['halm-tree-table']}
            dataSet={this.props.listDS}
            columns={this.columns}
            treeLoadData={this.handleLoadData}
            expandIcon={this.expandIcon}
            queryFields={{
              employeeName: (
                <StaffLov
                  title={getLangs('EMP_NAME')}
                  dataSet={queryDataSet}
                  name="employeeName"
                  valueField="employeeId"
                />
              ),
            }}
          />
        </Content>
      </React.Fragment>
    );
  }
}

export default WorkCenter;
