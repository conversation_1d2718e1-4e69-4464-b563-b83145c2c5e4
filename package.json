{"name": "inja-qms-front", "private": true, "scripts": {"start": "cross-env UMI_ENV=dev umi dev", "build:dep-all:prod": "cross-env UMI_ENV=prod SKIP_NO_CHANGE_MODULE=true umi hzero-build-dep --all", "build:app:prod": "cross-env UMI_ENV=prod SKIP_NO_CHANGE_MODULE=true NODE_OPTIONS='--max_old_space_size=81960'  umi hzero-build --only-build-parent", "build": "umi hzero-build", "build:app": "umi hzero-build --only-build-parent", "build:c7n": "cross-env NODE_ENV=development SKIP_NO_CHANGE_MODULE=true node ./node_modules/umi/bin/umi.js hzero-build-dep --package-name choerodon-ui", "build:ms-dev": "cross-env JSXINJS=true UMI_ENV=dev NODE_OPTIONS='--max_old_space_size=10240' umi hzero-build --only-build-micro", "build:ms": "cross-env JSXINJS=true UMI_ENV=prod NODE_OPTIONS='--max_old_space_size=20480' umi hzero-build --only-build-micro", "build:ms-dev-all": "cross-env JSXINJS=true UMI_ENV=dev umi hzero-build --only-build-micro --all-packages", "build:ms-all": "cross-env JSXINJS=true UMI_ENV=prod SKIP_NO_CHANGE_MODULE=true umi hzero-build --only-build-micro --all-packages", "build:hb": "cross-env UMI_ENV=prod umi hb-cmf", "build:hb-dev": "cross-env UMI_ENV=dev umi hb-cmf", "transpile": "umi hzero-transpile", "build:analyze": "ANALYZE=1 umi build", "hzero-build:dep-all": "umi hzero-build-dep --all", "hzero-build:dep": "umi hzero-build-dep --package-name", "start:additional": "cross-env ADDITIONAL=true umi dev", "build:additional": "cross-env ADDITIONAL=true umi hzero-build", "build:additional:ms": "cross-env ADDITIONAL=true umi hzero-build --only-build-micro", "build:additional-dep": "cross-env ADDITIONAL_NAME=145_hotfix ADDITIONAL_OUTPUT=145_hotfix umi hzero-build-dep --package-name", "build:hzero": "micro-prepare hzero", "prepare": "husky install && node node_modules/@hzerojs/plugin-micro/postinstall-script", "lint": "eslint \"packages/**/*.{js,jsx,tsx,ts}\" && npm run lint:style && tsc --noEmit --emitDeclarationOnly false", "lint:fix": "eslint --quiet --fix 'packages/**/*.{js,jsx,tsx,ts}' && npm run lint:style", "lint:style": "stylelint \"packages/**/*.less\" --syntax less", "lint-staged": "lint-staged", "bootstrap": "yarn --registry https://nexus-c7n.inja.com/repository/npm-group/", "test": "node scripts/test.js", "changelog": "node node_modules/.bin/conventional-changelog -p eslint -i CHANGELOG.md -s -r 0 && git add CHANGELOG.md", "tree": "tree -I node_modules -L 3", "release": "standard-version", "release-module": "node scripts/release.js", "icon": "npx cross-env ICON_FONT_URL=//at.alicdn.com/t/font_1440728_2mntu9m71ej.css node scripts/icon.js", "hzero-version": "node scripts/version.js", "prettier": "prettier -c --write \"packages/**/*.{js,jsx,tsx,ts}\""}, "dependencies": {"@antv/data-set": "latest", "@antv/g2": "^4.1.14", "@antv/g6": "^3.5.3", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@typescript-eslint/eslint-plugin": "^5.38.1", "@typescript-eslint/parser": "^5.38.1", "ali-table-fix-hd": "0.0.4-alpha.0.8", "array-move": "3.0.1", "axios": "~0.19.2", "@jiaminghi/data-view-react": "^1.2.5", "babel-plugin-module-resolver": "^3.1.1", "babel-plugin-use-const-enum": "^0.0.9", "bizcharts": "^4.0.14", "copy-to-clipboard": "^3.3.1", "core-js": "^3.6.4", "cropperjs": "1.5.6", "cross-env": "^7.0.2", "echarts": "^4.8.0", "echarts-for-react": "^2.0.14", "echarts-gl": "^1.1.2", "escape-goat": "^3.0.0", "event-emitter": "^0.3.5", "hzerojs-plugin-yqcloud-jssdk": "^1.0.13", "gg-editor": "2.0.4", "hcm-common-front": "7.0.1-beta.1", "hcm-components-front": "7.0.5", "hcm-api-front": "7.0.0", "hcm-hlct-front": "7.0.8", "hcm-message-front": "7.0.5", "hcm-method-front": "7.0.0", "hcm-model-front": "7.0.0", "hcm-report-front": "7.0.0", "hspc-front": "7.0.1", "hfins-front-common": "1.5.1-alpha", "hzero-front": "1.11.26-alpha.3", "hzero-front-cusz": "1.2.3-beta.1", "hzero-front-himp": "1.10.0", "@hzero-front-ui/cfg": "5.1.6-alpha.1", "hzero-ui": "^1.0.76", "immutability-helper": "3.0.1", "is-absolute-url": "^2.0.0", "jsencrypt": "^3.0.0-rc.1", "jspdf": "^2.5.1", "lerna": "^3.20.2", "less-loader": "^6", "lint-staged": "^10.0.8", "load-script": "1.0.0", "lodash-decorators": "^6.0.1", "mf-ali-table-fix-hd": "0.0.1", "mini-css-extract-plugin": "2.4.5", "new-github-release-url": "^1.0.0", "pdfjs-dist-2.1.266": "npm:pdfjs-dist@2.1.266", "prettier": "^1.19.1", "rc-drawer-menu": "1.1.0", "styled-components": "^5.1.1", "universal-cookie": "^4.0.3", "draftjs-utils": "^0.9.4"}, "devDependencies": {"inja-layout": "1.2.0-alpha.38", "inja-theme": "1.2.0-alpha.2", "@babel/cli": "*", "@babel/plugin-proposal-class-properties": "7.7.4", "@babel/plugin-proposal-decorators": "^7.7.4", "@hzerojs/preset-hzero": "1.2.23", "@smock/umi-plugin-smock": "^0.1.22", "@smock/umi-plugin-sproxy": "^0.1.14", "@types/prettier": "1.16.1", "@types/react": "16.14.0", "@types/react-dom": "16.9.8", "@typescript-eslint/eslint-plugin": "^5.38.1", "@typescript-eslint/parser": "^5.38.1", "conventional-changelog-cli": "^2.0.12", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint": "^7.32.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-import-helpers": "^1.1.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-react": "^7.27.0", "lerna": "3.20.2", "prettier": "^2.6.2", "react": "^16.12.0", "husky": "^8.0.3", "react-cropper": "1.3.0", "react-custom-scrollbars": "~4.2.1", "react-document-title": "^2.0.3", "react-dom": "^16.12.0", "react-grid-layout": "^1.0.0", "react-intl-universal": "^2.3.2", "react-pdf-js": "^5.1.0", "react-resizable": "^1.10.1", "react-sortable-pane": "1.1.0", "react-tsparticles": "^2.12.2", "react-viewer": "2.11.1", "react-virtualized": "latest", "standard-version": "^5.0.2", "tsparticles-slim": "^2.12.0", "stylelint-config-prettier": "^5.2.0", "typescript": "^4.2.0", "mobx": "^4.15.7", "umi": "^3.2.22", "mobx-react": "~6.1.4", "webpack-merge": "^5.8.0", "yarn": "^1.13.0"}, "resolutions": {"typescript": "^4.2.0", "redux": "3.7.2", "echarts-for-react": "^2.0.14", "echarts": "^4.8.0", "string-width": "^4", "strip-ansi": "^6", "react": "~16.12.0", "react-dom": "~16.12.0", "debug": "^4.3.2", "styled-components": "~5.3.6", "@types/react": "^16.0.0", "@types/react-dom": "^16.0.0", "react-router": "5.2.1", "react-router-dom": "5.2.1", "dva": "2.6.0-beta.22", "colors": "1.3.2", "@babel/runtime-corejs2": "~7.6.3", "@hzero-front-ui/cfg": "5.1.6-alpha.1", "hzero-front": "1.11.26-alpha.3", "@hzerojs/preset-hzero": "1.2.41", "choerodon-ui": "1.6.6-alpha.17", "choerodon-ui-font": "0.2.13", "hzero-ued-icon": "0.1.17", "electron-to-chromium": "http://nexus.saas.hand-china.com/repository/hone-npm-group/electron-to-chromium/-/electron-to-chromium-1.4.761.tgz"}, "description": "hzero-demo", "author": "", "version": "0.0.2", "eslintConfig": {"extends": "react-app"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-merge": "git submodule update", "post-checkout": "git submodule update"}}, "browserslist": [">0.2%", "not dead", "not ie <= 10", "not op_mini all"], "lint-staged": {"packages/*/src/**/*.{js,jsx,tsx,ts}": ["eslint --quiet --fix", "git add"], "packages/*/src/**/*.{js,jsx,tsx,ts,less}": ["prettier --write", "git add"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "resolver": "jest-pnp-resolver", "setupFiles": ["react-app-polyfill/jsdom"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/?(*.)(spec|test).{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "testURL": "http://localhost", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["<rootDir>/node_modules/jest-watch-typeahead/filename.js", "<rootDir>/node_modules/jest-watch-typeahead/testname.js"]}, "workspaces": ["packages/*"]}