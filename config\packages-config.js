/**
 * 打包的所有服务，可按需使用
 */

const packages = [
  // 产品模块
  // 增加spc模块
  {
    name: 'hspc-front',
    registerRegex: '/(hspc)/',
  },
  {
    name: 'hcm-api-front',
    registerRegex: '/(hcm)/',
  },
  {
    name: 'key-hlct-front',
    registerRegex: '/(hwkf|hwms|himp|hmes|sampling|hcmc|pub|sslm)/',
  },
  {
    name: 'key-method-front',
    registerRegex: '/(hmes|feature|model|hwms)/',
  },
  {
    name: 'key-hspc-front',
    registerRegex: '/(hspc)/',
  },

];

module.exports = packages;
