/**
 * @feature 打印方法
 * @date 2021-3-10
 * <AUTHOR> <<EMAIL>>
 */
import { findDOMNode } from 'react-dom';

const printElement = options => {
  const {
    content,
    pageStyle,
    onBeforePrint,
    onAfterPrint,
    bodyClass = '',
    copyStyles = true,
  } = options;

  const contentEl = content;

  if (contentEl === undefined || contentEl === null) {
    return;
  }

  const printWindow = document.createElement('iframe');
  printWindow.style.position = 'absolute';
  printWindow.style.top = '-1000px';
  printWindow.style.left = '-1000px';

  // eslint-disable-next-line react/no-find-dom-node
  const contentNodes = findDOMNode(contentEl);
  const linkNodes = document.querySelectorAll('link[rel="stylesheet"]');

  const linkTotal = linkNodes.length || 0;
  const linksLoaded = [];
  const linksErrored = [];

  const removeWindow = target => {
    setTimeout(() => {
      target.parentNode.removeChild(target);
    }, 500);
  };

  const triggerPrint = target => {
    if (onBeforePrint) {
      onBeforePrint();
    }

    setTimeout(() => {
      target.contentWindow.focus();
      target.contentWindow.print();
      removeWindow(target);

      if (onAfterPrint) {
        onAfterPrint();
      }
    }, 500);
  };

  const markLoaded = (linkNode, loaded) => {
    if (loaded) {
      linksLoaded.push(linkNode);
    } else {
      linksErrored.push(linkNode);
    }

    if (linksLoaded.length + linksErrored.length === linkTotal) {
      triggerPrint(printWindow);
    }
  };

  printWindow.onload = () => {
    /* IE11 support */
    if (window.navigator && window.navigator.userAgent.indexOf('Trident/7.0') > -1) {
      printWindow.onload = null;
    }

    const domDoc = printWindow.contentDocument || printWindow.contentWindow.document;
    const srcCanvasEls = [...contentNodes.querySelectorAll('canvas')];

    domDoc.open();
    domDoc.write(contentNodes.outerHTML);
    domDoc.close();

    /* remove date/time from top */
    const defaultPageStyle =
      pageStyle === undefined
        ? '@page { size: auto;  margin: 0mm; } @media print { body { -webkit-print-color-adjust: exact; } } html,body { overflow: auto!important; height: auto!important; }'
        : pageStyle;

    const styleEl = domDoc.createElement('style');
    styleEl.appendChild(domDoc.createTextNode(defaultPageStyle));
    domDoc.head.appendChild(styleEl);

    if (bodyClass.length) {
      domDoc.body.classList.add(bodyClass);
    }

    const canvasEls = domDoc.querySelectorAll('canvas');

    if (Array.isArray(canvasEls)) {
      [...canvasEls].forEach((node, index) => {
        node.getContext('2d').drawImage(srcCanvasEls[index], 0, 0);
      });
    }

    if (copyStyles !== false) {
      const headEls = document.querySelectorAll('style, link[rel="stylesheet"]');

      [...headEls].forEach((node, index) => {
        if (node.tagName === 'STYLE') {
          const newHeadEl = domDoc.createElement(node.tagName);

          if (node.sheet) {
            let styleCSS = '';

            for (let i = 0; i < node.sheet.cssRules.length; i++) {
              if (typeof node.sheet.cssRules[i].cssText === 'string') {
                styleCSS += `${node.sheet.cssRules[i].cssText}\r\n`;
              }
            }

            newHeadEl.setAttribute('id', `react-to-print-${index}`);
            newHeadEl.appendChild(domDoc.createTextNode(styleCSS));
            domDoc.head.appendChild(newHeadEl);
          }
        } else {
          const attributes = [...node.attributes];

          const hrefAttr = attributes.filter(attr => attr.nodeName === 'href');
          const hasHref = hrefAttr.length ? !!hrefAttr[0].nodeValue : false;

          if (hasHref) {
            const newHeadEl = domDoc.createElement(node.tagName);

            attributes.forEach(attr => {
              newHeadEl.setAttribute(attr.nodeName, attr.nodeValue);
            });

            newHeadEl.onload = markLoaded.bind(null, newHeadEl, true);
            newHeadEl.onerror = markLoaded.bind(null, newHeadEl, false);
            domDoc.head.appendChild(newHeadEl);
          } else {
            markLoaded(node, true); // `true` because we've already shown a warning for this
          }
        }
      });
    }

    if (linkTotal === 0 || copyStyles === false) {
      triggerPrint(printWindow);
    }
  };

  document.body.appendChild(printWindow);
};

export default printElement;
