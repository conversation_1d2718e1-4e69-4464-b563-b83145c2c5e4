import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  SelectBox,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Content, Header } from 'components/Page';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import moment from 'moment';
import intl from 'utils/intl';
import { getCurrentUser } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n, TarzanSpin, C7nFormItemSort } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { FieldType } from 'choerodon-ui/dataset/data-set/enum';
import {
  detailDS,
  disposalDS,
  lineDtlDS,
  ncRecordLineDS,
  scanFormDS,
} from './stores/NcReportDocDtlDS';
import DetailDrawerComponent from './DetailDrawerComponent';
import {
  GetInspectBusInfo,
  SaveNcReportDoc,
  QueryInspectDocLine,
  QueryUnifyIdpValue,
} from './services';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hwms.ncReportDocMaintain';
const userInfo = getCurrentUser();

interface DynamicDisplayFlagProps {
  reworkRouterFlag: boolean; // dispositionFunction=REWORK_ROUTER
  reworkSourceFlag: boolean; // dispositionFunction=REWORK_SOURCE/REWORK_ANY
  degradeFlag: boolean; // dispositionFunction=DEGRADE
  eoDischargeFlag: boolean; // dispositionFunction=EO_DISCHARGE
  crossWoFlag: boolean; // dispositionFunction=CROSS_WO_INTERCEPT
  concessionFlag: boolean; // dispositionFunction=CONCESSION_INTERCEPTION
}

const NcReportDetail = props => {
  const {
    businessKey,
    hwkfData: { taskId, selfUserId },
  } = props;
  const kid = businessKey;
  const [canEdit, setCanEdit] = useState(true);
  const [activeKey, setActiveKey] = useState<any>(['baseInfo', 'ncRecordLine']);
  const [disposalType, setDisposalType] = useState<string>(''); // 处置类型
  const [createMethod, setCreateMethod] = useState<string>(''); // 创建方式
  const [ncReportStatus, setNcReportStatus] = useState<string>('NEW'); // 不良记录单状态
  const [disposalFunList, setDisposalFunList] = useState<any[]>([]); // 处置方法列表
  const [dispositionFunction, setDispositionFunction] = useState(''); // 处置方法（整体处置）
  const [dynamicDisplayFlag, setDynamicDisplayFlag] = useState<DynamicDisplayFlagProps>({
    reworkRouterFlag: false,
    reworkSourceFlag: false,
    degradeFlag: false,
    eoDischargeFlag: false,
    crossWoFlag: false,
    concessionFlag: false,
  });
  const lineDetailDs = useMemo(() => new DataSet(lineDtlDS()), [kid]);
  const ncRecordLineDs = useMemo(
    () =>
      new DataSet({
        ...ncRecordLineDS(),
        children: { ncReportLineDtlList: lineDetailDs },
      }),
    [kid],
  );
  const disposalDs = useMemo(() => new DataSet(disposalDS()), [kid]);
  const scanFormDs = useMemo(() => new DataSet(scanFormDS()), [kid]);
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
        children: {
          ncReportLineList: ncRecordLineDs,
          disposalList: disposalDs,
          scanFormDs,
        },
      }),
    [kid],
  );
  const { run: saveNcReportDoc, loading: saveLoading } = useRequest(SaveNcReportDoc(), {
    manual: true,
  });
  const { run: queryUnifyIdpValue, loading: querySelectLoading } = useRequest(
    QueryUnifyIdpValue(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: getInspectBusInfo, loading: queryLoading } = useRequest(GetInspectBusInfo(), {
    manual: true,
  });
  const { run: queryInspectDocLine, loading: queryInspectDocLoading } = useRequest(
    QueryInspectDocLine(),
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (kid === 'create') {
      queryBusinessTypeRuleInfo(undefined);
      return;
    }
    handleQueryDetail(kid);
  }, [kid]);

  const handleQueryDetail = id => {
    detailDs.setQueryParameter('ncReportId', id);
    detailDs.setQueryParameter('assignId', selfUserId);
    detailDs.setQueryParameter('taskId', taskId);
    detailDs.query().then(res => {
      const { disposalType, ncReportStatus, inspectBusinessType, disposalList,createMethod } = res;
      const newDisposalType = !disposalType || disposalType === "NO" ? "ALL" : disposalType;
      detailDs?.current?.set("disposalType", newDisposalType);
      setDisposalType(newDisposalType);
      setActiveKey(['baseInfo', 'ncRecordLine', newDisposalType]);
      if (newDisposalType === 'ALL' && disposalList?.length) {
        setDispositionFunction(disposalList[0].dispositionFunction);
        const dispositionGroupLov = {
          dispositionGroupId: disposalList[0]?.dispositionGroupId,
          description: disposalList[0]?.dispositionGroupDesc,
          dispositionGroup: disposalList[0]?.dispositionGroup,
          dispositionGroupDesc: disposalList[0]?.dispositionGroupDesc,
        };
        disposalDs.current?.set('dispositionGroupLov', dispositionGroupLov);
      }
      setCreateMethod(createMethod);
      setNcReportStatus(ncReportStatus);
      // handleChangeBusRule(inspectBusinessType);
      // 同步处置行上的optionList
      (ncRecordLineDs || []).forEach(_record => handleSyncOptionList(_record, false));

      const _selectParam = detailDs.getField('inspectBusinessType')?.get('lovPara');
      if (!_selectParam?.siteId) {
        queryUnifyIdpValue({
          params: {
            ..._selectParam,
            siteId: res.siteId,
            lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
          },
        }).then(res2 => {
          if (res2 && res2.length) {
            // 查询业务类型规则对应的处置方法配置
            // const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
            // const currentRecord = optionDs?.find(
            //   _record => _record?.get('inspectBusinessType') === inspectBusinessType,
            // );
            const currentRecord = res2?.find(
              _record => _record?.inspectBusinessType === inspectBusinessType,
            );
            let params;
            if (!inspectBusinessType) {
              params = undefined;
            } else {
              params = { dispositionGroupId: currentRecord?.dispositionGroupId };
            }
            queryBusinessTypeRuleInfo(params);
          }
        });
      }
    });
  };

  // 根据业务类型规则查询动态列信息
  const queryBusinessTypeRuleInfo = params => {
    getInspectBusInfo({
      params,
      onSuccess: res => {
        const newList = res.filter((item) => item.dispositionFunction !== "PASS");
        setDisposalFunList(newList || []);
        if (res && res.length) {
          res.forEach(item => {
            if (item?.dispositionFunction) {
              disposalDs.addField(item?.dispositionFunction, {
                type: FieldType.number,
                label: item.description,
                defaultValue: 0,
                min: 0,
                validator: (value, _, record: any) => {
                  // 当前行除“通过”外其余总数
                  let sumQty = 0;
                  res.forEach(item => {
                    if (item?.dispositionFunction !== 'PASS') {
                      sumQty += Number(record.get(item?.dispositionFunction) || 0);
                    }
                  });
                  let parentNumber;
                  if (record?.get('ncRecordType') === 'EO_MATERIAL_NC') {
                    // optionList
                    const optionItem = (record?.get('optionList') || []).find(
                      item =>
                        item.componentMaterialLotCode === record?.get('componentMaterialLotCode'),
                    );
                    parentNumber = Number(optionItem?.sumAssembleQty);
                  } else {
                    // 当前处置行对应的不良记录行
                    const parentRecord = ncRecordLineDs.find(
                      _rec =>
                        _rec?.get('ncRecordType') === record?.get('ncRecordType') &&
                        _rec?.get('ncObjectType') === record?.get('ncObjectType') &&
                        _rec?.get('ncObjectCode') === record?.get('ncObjectCode'),
                    );
                    parentNumber = Number(parentRecord?.get('qty'));
                  }
                  // 处置方法为“通过”时直接跳过校验
                  if (item?.dispositionFunction === 'PASS') {
                    return true;
                  }
                  // 更新“通过”数量
                  record.set('PASS', parentNumber - sumQty);
                  // 所填总数大于不良行上的数量时报错
                  if (sumQty > parentNumber) {
                    return intl
                      .get(`${modelPrompt}.validator.dispositionFunction`, {
                        functionDesc: item?.description,
                        count: parentNumber - sumQty + value,
                      })
                      .d(
                        `${item?.description}项所填数量必须小于等于${parentNumber - sumQty + value}`,
                      );
                  }
                  return true;
                },
              });
            }
          });
          const _displayFlag: DynamicDisplayFlagProps = {
            reworkRouterFlag: false,
            reworkSourceFlag: false,
            degradeFlag: false,
            eoDischargeFlag: false,
            crossWoFlag: false,
            concessionFlag: false,
          };
          res.forEach(item => {
            switch (item?.dispositionFunction) {
              case 'REWORK_ROUTER':
                _displayFlag.reworkRouterFlag = true;
                break;
              case 'REWORK_SOURCE':
                _displayFlag.reworkSourceFlag = true;
                break;
              case 'DEGRADE':
                _displayFlag.degradeFlag = true;
                break;
              case 'EO_DISCHARGE':
                _displayFlag.eoDischargeFlag = true;
                break;
              case 'CROSS_WO_INTERCEPT':
                _displayFlag.crossWoFlag = true;
                break;
              case 'CONCESSION_INTERCEPTION':
                _displayFlag.concessionFlag = true;
                break;
              default:
                break;
            }
          });
          setDynamicDisplayFlag(_displayFlag);
        }
      },
    });
  };

  const findPartDisposalRecords = (ncRecordType, ncObjectType, ncObjectLov) => {
    const getNcObjectCode = type => {
      switch (type) {
        case 'MATERIAL_LOT':
          return ncObjectLov?.materialLotCode;
        case 'MATERIAL':
          return ncObjectLov?.materialName;
        case 'EO':
          return ncObjectLov?.eoNum;
        default:
          return undefined;
      }
    };
    return disposalDs.filter(
      _record =>
        _record.get('ncRecordType') === ncRecordType &&
        _record.get('ncObjectType') === ncObjectType &&
        _record.get('ncObjectCode') === getNcObjectCode(ncObjectType),
    );
  };

  // 更新处置上的下拉数据源
  const handleSyncOptionList = (record, currentFlag) => {
    const disRecords = findPartDisposalRecords(
      record?.get('ncRecordType'),
      record?.get('ncObjectType'),
      record?.get('ncObjectLov'),
    );
    const lineDtlList = currentFlag ? lineDetailDs.toData() : record.toData().ncReportLineDtlList;
    const _newOptionList = getOptionList(lineDtlList);
    (disRecords || []).forEach(_record => {
      _record.set('optionList', _newOptionList);
      // 检查处置所选的实物是否都在_newOptionList中,不在的话清空
      if (
        _record.get('componentMaterialLotCode') &&
        _newOptionList.findIndex(
          i => i.componentMaterialLotCode === _record.get('componentMaterialLotCode'),
        ) === -1
      ) {
        _record.init('componentMaterialLotCode', undefined);
        _record.init('PASS', 0);
      }
    });
  };

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    // 处理保存数据
    const data = detailDs.current?.toData();
    const { ncReportLineList, disposalType, disposalList } = data;
    // 处理处置数据保存格式
    const disposalData = disposalList;
    // 处置数量
    let disposalQty = 0;
    // 不良记录单行数量
    let ncQty = 0;
    // 处理班次日期保存格式
    ncReportLineList.forEach(i => {
      ncQty += i.qty || 0;
      i.shiftDate = i.shiftDate?.split(' ')[0] || undefined;
    });
    if (disposalType === 'PART') {
      disposalData.forEach(item => {
        const _functionList: any = [];
        disposalFunList.forEach(listItem => {
          if (item[listItem?.dispositionFunction]) {
            const qty =  item[listItem?.dispositionFunction];
            _functionList.push({
              disposalFunctionId: listItem?.dispositionFunctionId,
              dispositionFunction: listItem?.dispositionFunction,
              qty,
            });
            if (listItem?.dispositionFunction !== "PASS") {
              disposalQty += qty || 0;
            }
          }
        });
        item.disposalFunctionList = _functionList;
      });
    }

    // 处置方法的处置数量+抽样数量+破坏数量不能大于报检总数
    // const _samplingQty = data.samplingQty || 0;
    const _destroyQty = data.destroyQty || 0;
    const _inspectSumQty = data.inspectSumQty || 0;
    if ((disposalQty + _destroyQty) !== _inspectSumQty && disposalType === "PART" && data.createMethod !== "inja-qms-mes") {
      notification.warning({
        message: intl.get(`${modelPrompt}.info.validateQty`).d("处置方法的处置数量+破坏数量必须等于报检总数, 请检查！"),
      });
      return;
    }
    // 创建方式为车间不良时并且为对象处置：行数量合必须等于处置数量
    if (disposalType === "PART" && data.createMethod === "inja-qms-mes" && ncQty !== disposalQty) {
      notification.warning({
        message: intl.get(`${modelPrompt}.info.validateQty`).d("处置方法数量总和必须等于不良对象数量!, 请检查！"),
      });
      return;
    }
    // 保存
    saveNcReportDoc({
      params: {
        ...data,
        taskId,
        assignId: selfUserId,
        disposalList: undefined,
        disposalType: disposalType === '' ? undefined : disposalType,
        ncReportLineList,
        allNcReportLineDisposal: disposalType === 'ALL' ? disposalData[0] : undefined,
        partNcReportLineDisposalList: disposalType === 'PART' ? disposalData : undefined,
      },
      onSuccess: () => {
        notification.success({});
        setCreateMethod('');
        setCanEdit(false);
        handleQueryDetail(kid);
      },
    });
  };

  const DetailDrawerTitle = observer(({ record, dataSet }) => {
    const _title = record.get('revisionCode')
      ? `${intl.get(`${modelPrompt}.title.lineDetail`).d('行明细')}:
          ${record.get('ncObjectTypeDesc') || ''}${record.get('ncObjectCode') || ''}/
          ${record.get('revisionCode') || ''}`
      : `${intl.get(`${modelPrompt}.title.lineDetail`).d('行明细')}:
          ${record.get('ncObjectTypeDesc') || ''}${record.get('ncObjectCode') || ''}`;

    const handleAddLineDetail = () => {
      let _maxLineNum = 0;
      lineDetailDs.forEach(_record => {
        if (_record.get('lineNumber') > _maxLineNum) {
          _maxLineNum = _record.get('lineNumber');
        }
      });
      const newLine = Math.floor(_maxLineNum / 10) * 10 + 10;
      lineDetailDs.create({
        lineNumber: newLine,
      });
    };

    return (
      <div className={styles['drawer-title']}>
        <span className={styles['title-left']} style={{ fontSize: '14px' }}>
          {_title}
        </span>
        <span className={styles['title-right']}>
          <Button color={ButtonColor.primary} onClick={handleAddLineDetail} disabled>
            {intl.get(`${modelPrompt}.button.addLineDtl`).d('添加')}
          </Button>
          <span className={styles['title-right-info']}>
            {intl
              .get(`${modelPrompt}.info.totalDetail`, {
                count: dataSet.length,
              })
              .d(`共 ${dataSet.length} 项明细`)}
          </span>
        </span>
      </div>
    );
  });

  const handleOpenLineDtlDrawer = record => {
    record.setState('isCancel', false);
    record.setState('isSubmit', false);
    Modal.open({
      ...drawerPropsC7n({
        ds: lineDetailDs,
        canEdit: false,
      }),
      title: <DetailDrawerTitle record={record} dataSet={lineDetailDs} />,
      style: {
        width: 1080,
      },
      onClose: () =>
        record.getState('isSubmit')
          ? record.setState('isCancel', false)
          : record.setState('isCancel', true),
      afterClose: () => record.getState('isCancel') && lineDetailDs.reset(),
      onCancel: () => {
        record.setState('isSubmit', false);
        record.setState('isCancel', true);
      },
      onOk: () => {
        record.setState('isSubmit', true);
        record.setState('isCancel', false);
        lineDetailDs.loadData(lineDetailDs.toData());
      },
      children: <DetailDrawerComponent lineDtlDs={lineDetailDs} canEdit={false} record={record} />,
    });
  };

  const handleResetDisposal = () => {
    const _disposalType = detailDs.current?.get('disposalType');
    const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
    const currentRecord = optionDs?.find(
      _record =>
        _record?.get('inspectBusinessType') === detailDs.current?.get('inspectBusinessType'),
    );
    if (_disposalType === 'PART' || !_disposalType) {
      disposalDs.loadData([]);
    } else {
      disposalDs.loadData([
        {
          dispositionGroupId: currentRecord?.get('dispositionGroupId'),
          dispositionGroupDesc: currentRecord?.get('dispositionGroupDesc'),
          disposalUserLov: {
            id: userInfo.id,
            realName: userInfo.realName,
          },
          disposalTime: moment(moment().format('YYYY-MM-DD HH:mm:ss')),
        },
      ]);
    }
  };

  const getNcRecordTypeDesc = (value, record) => {
    // 拿到类型对应的OptionDs
    const optionDs = ncRecordLineDs.getField('ncRecordType')?.getOptions(record);
    return optionDs?.find(_rec => _rec.get('value') === value);
  };

  const getNcObjectTypeDesc = (value, record) => {
    // 拿到类型对应的OptionDs
    const optionDs = ncRecordLineDs.getField('ncObjectType')?.getOptions(record);
    return optionDs?.find(_rec => _rec.get('value') === value);
  };

  const handleChangeDisposalType = value => {
    setDisposalType(value);
    if (value) {
      // 展开处置对应的折叠面板
      setActiveKey(['baseInfo', 'ncRecordLine', value]);
    }
    // 切换处置类型时，处置组旧数据需要清空
    handleResetDisposal();
    // 没有行明细时不做处理
    if (!ncRecordLineDs.length) {
      return;
    }
    if (createMethod === 'QMS') {
      if (value === 'ALL') {
        // 当不良记录单头创建方式=QMS，处置类型选择了整体处置时需自动生成不良对象类型=INSPECT_DOC的行
        handleCreateQmsLine();
      } else {
        // 当不良记录单头创建方式=QMS，处置类型切换了整体处置外的其余类型时需删除不良对象类型=INSPECT_DOC的行
        ncRecordLineDs.forEach(_record => {
          if (_record.get('ncObjectType') === 'INSPECT_DOC') {
            ncRecordLineDs.remove(_record);
          }
        });
      }
    }
    if (value === 'PART') {
      disposalDs.remove(disposalDs.current);
      // 根据不良记录行上的信息初始化处置DS
      ncRecordLineDs.forEach(_record => {
        const recordTypeRecord = getNcRecordTypeDesc(_record.get('ncRecordType'), _record);
        const objectTypeRecord = getNcObjectTypeDesc(_record.get('ncObjectType'), _record);
        disposalDs.create({
          ncRecordType: _record.get('ncRecordType'),
          ncRecordTypeDesc: recordTypeRecord?.get('meaning'),
          ncObjectType: _record.get('ncObjectType'),
          ncObjectTypeDesc: objectTypeRecord?.get('meaning'),
          ncObjectId: _record.get('ncObjectId'),
          ncObjectCode: _record.get('ncObjectCode'),
          ncObjectRevisionCode: _record.get('revisionCode'),
          PASS: _record.get('ncRecordType') !== 'EO_MATERIAL_NC' ? _record.get('qty') : 0,
          uomId: _record.get('uomId'),
          uomName: _record.get('uomName'),
          optionList: getOptionList(_record.toData().ncReportLineDtlList),
        });
      });
    }
  };

  // 自动生成不良对象类型=INSPECT_DOC的行
  const handleCreateQmsLine = () => {
    queryInspectDocLine({
      params: { inspectDocIds: ncRecordLineDs.map(_record => _record.get('sourceDocId')) },
      onSuccess: res => {
        (res || []).forEach(item => {
          ncRecordLineDs.create(
            {
              ncRecordType: ncRecordLineDs.current?.get('ncRecordType'),
              ncObjectType: 'INSPECT_DOC',
              ncObjectId: item.inspectDocId,
              ncObjectCode: item.inspectDocNum,
              qty: item.inspectSumQty,
              uomLov: item.uomId ? { uomId: item.uomId, uomName: item.uomName } : undefined,
              supplierLov: { supplierId: item.supplierId, supplierName: item.supplierName },
              customerLov: { customerId: item.customerId, customerName: item.customerName },
              locatorLov: { locatorId: item.locatorId, locatorName: item.locatorName },
              equipmentLov: { equipmentId: item.equipmentId, equipmentCode: item.equipmentCode },
              operationLov: { operationId: item.operationId, operationName: item.operationName },
            },
            0,
          );
        });
      },
    });
  };

  const handleChangeFunction = val => {
    setDispositionFunction(val?.dispositionFunction);
    disposalDs.current?.init('interceptWorkcellLov', undefined);
    disposalDs.current?.init('interceptRouterStepLov', undefined);
    disposalDs.current?.init('dischargeWorkcellLov', undefined);
    disposalDs.current?.init('crossOperationLov', undefined);
    disposalDs.current?.init('reworkStepLov', undefined);
    disposalDs.current?.init('reworkWorkcellLov', undefined);
    disposalDs.current?.init('reworkRouterLov', undefined);
    disposalDs.current?.init('degradeLevel', undefined);
    disposalDs.current?.init('degradeMaterialLov', undefined);
    disposalDs.current?.init('degradeRevisionCode', undefined);
  };

  // 拼接返回optionList的displayValue
  const getOptionValue = item => {
    const labelText = !['MAT', 'LOT'].includes(item.identifyType)
      ? intl.get(`${modelPrompt}.option.materialLot`).d('物料批')
      : intl.get(`${modelPrompt}.option.material`).d('物料');
    const materialText = item.componentRevisionCode
      ? `${item.componentMaterialName}/${item.componentRevisionCode}`
      : item.componentMaterialName;

    return !['MAT', 'LOT'].includes(item.identifyType)
      ? `${labelText}: ${item.componentMaterialLotCode}`
      : `${labelText}: ${materialText}`;
  };

  const getOptionList = data => {
    // 行明细去重，作为实物数据源
    const _optionList: any = [];
    (data || []).forEach(item => {
      const _index = _optionList.findIndex(
        (i: any) => i.componentMaterialLotCode === item.componentMaterialLotCode,
      );
      if (_index === -1) {
        _optionList.push({
          ...item,
          displayValue: getOptionValue(item),
        });
      }
    });
    return _optionList;
  };

  const handleComponentDisposal = record => {
    const recordTypeRecord = getNcRecordTypeDesc(record.get('ncRecordType'), record);
    const objectTypeRecord = getNcObjectTypeDesc(record.get('ncObjectType'), record);
    disposalDs.create({
      ncRecordType: record.get('ncRecordType'),
      ncRecordTypeDesc: recordTypeRecord?.get('meaning'),
      ncObjectType: record.get('ncObjectType'),
      ncObjectTypeDesc: objectTypeRecord?.get('meaning'),
      ncObjectId: record.get('ncObjectId'),
      ncObjectCode: record.get('ncObjectCode'),
      ncObjectRevisionCode: record.get('revisionCode'),
      PASS: record.get('ncRecordType') !== 'EO_MATERIAL_NC' ? record.get('qty') : 0,
      uomId: record.get('uomId'),
      uomName: record.get('uomName'),
      optionList: getOptionList(record.toData().ncReportLineDtlList),
    });
  };

  const ncRecordLineColumns: ColumnProps[] = useMemo(() => {
    const DisposalButton = observer(({ record, disposalType }) => {
      return (
        <a
          disabled={
            !canEdit || record.get('ncRecordType') !== 'EO_MATERIAL_NC' || disposalType !== 'PART'
          }
          onClick={() => handleComponentDisposal(record)}
        >
          {intl.get(`${modelPrompt}.operation.physicalDisposal`).d('组件实物处置')}
        </a>
      );
    });
    return [
      {
        name: 'ncRecordType',
        lock: ColumnLock.left,
        width: 120,
      },
      {
        name: 'ncObjectType',
        lock: ColumnLock.left,
        width: 120,
      },
      {
        name: 'ncObjectLov',
        width: 140,
      },
      {
        name: 'revisionCode',
        width: 140,
      },
      {
        name: 'qty',
      },
      {
        name: 'sourceDocNum',
        hidden: kid === 'create',
      },
      {
        name: 'sourceNcRecordNum',
        width: 150,
        hidden: kid === 'create',
      },
      {
        name: 'uomLov',
      },
      {
        name: 'lot',
      },
      {
        name: 'supplierLot',
      },
      {
        name: 'supplierLov',
      },
      {
        name: 'customerLov',
      },
      { name: 'containerLov' },
      { name: 'workcellLov' },
      { name: 'locatorLov' },
      { name: 'equipmentLov' },
      { name: 'operationLov' },
      { name: 'shiftTeamLov' },
      {
        name: 'shiftDate',
      },
      { name: 'shiftCode' },
      { name: 'ncStartUserLov' },
      {
        name: 'ncStartTime',
      },
      { name: 'remark' },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 180,
        renderer: ({ record }) => (
          <span className="action-link">
            <a onClick={() => handleOpenLineDtlDrawer(record)}>
              {intl.get(`${modelPrompt}.operation.lineDetail`).d('行明细')}
            </a>
            <DisposalButton record={record} disposalType={disposalType} />
          </span>
        ),
      },
    ];
  }, [canEdit, disposalType, createMethod, kid]);

  const handleChangeBusRule = value => {
    if (value) {
      // 切换业务类型规则是，清空处置行上的数量
      if (disposalFunList.length && disposalDs.length) {
        disposalDs.forEach(_record => {
          disposalFunList.forEach(item => {
            _record.set(item?.dispositionFunction, 0);
          });
        });
      }
      const optionDs = detailDs.getField('inspectBusinessType')?.getOptions(detailDs.current);
      const currentRecord = optionDs?.find(
        _record => _record?.get('inspectBusinessType') === value,
      );
      detailDs.current?.set('reviewType', currentRecord?.get('reviewType'));
      const dispositionGroupLov = {
        dispositionGroupId: currentRecord?.get('dispositionGroupId'),
        description: currentRecord?.get('dispositionGroupDesc'),
        dispositionGroup: currentRecord?.get('dispositionGroup'),
      };
      disposalDs.current?.set('dispositionGroupId', currentRecord?.get('dispositionGroupId'));
      disposalDs.current?.set('dispositionGroup', currentRecord?.get('dispositionGroup'));
      disposalDs.current?.set('dispositionGroupLov', dispositionGroupLov);
      queryBusinessTypeRuleInfo({ dispositionGroupId: currentRecord?.get('dispositionGroupId') });
    }
  };

  const handleChangeMaterialLotCode = (value, record) => {
    const currentData: any = (record.get('optionList') || []).find(
      (i: any) => i.componentMaterialLotCode === value,
    );
    record.set('PASS', currentData?.sumAssembleQty || 0);
    record.set('uomId', currentData?.uomId || undefined);
    record.set('uomName', currentData?.uomName || undefined);
  };

  // ncReportStatus=NEW时详情页所有字段均可编辑，同新建页面
  // ncReportStatus=HANDLE且ncReviewStatus=空或UNREVIEW或REJECT时详情页仅允许处置字段编辑
  // ncReportStatus=COMPLETED/CANCEL时详情页所有字段均不可编辑；
  // ncReportStatus=HANDLE且ncReviewStatus=REVIEWING审核中、REVIEW已审核时所详情页有字段均不可编辑；
  const partDisposalColumns: any = useMemo(() => {
    return [
      {
        name: 'editColumn',
        align: ColumnAlign.center,
        lock: ColumnLock.left,
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => disposalDs.remove(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button
              icon="remove"
              disabled={record.get('ncRecordType') !== 'EO_MATERIAL_NC'}
              funcType={FuncType.flat}
            />
          </Popconfirm>
        ),
      },
      { name: 'ncRecordTypeDesc', width: 150 },
      { name: 'ncObjectTypeDesc', width: 150 },
      {
        name: 'ncObjectCode',
        width: 150,
        renderer: ({ record }) =>
          record?.get('ncObjectRevisionCode')
            ? `${record?.get('ncObjectCode')}/${record?.get('ncObjectRevisionCode')}`
            : record?.get('ncObjectCode'),
      },
      {
        name: 'componentMaterialLotCode',
        width: 150,
        editor: record => {
          const groupRecords = record.dataSet.filter(_record => {
            return (
              _record.get('ncRecordType') === record.get('ncRecordType') &&
              _record.get('ncObjectType') === record.get('ncObjectType') &&
              _record.get('ncObjectCode') === record.get('ncObjectCode') &&
              (!record.get('ncObjectRevisionCode') ||
                _record.get('ncObjectRevisionCode') === record.get('ncObjectRevisionCode')) &&
              _record.get('componentMaterialLotCode') !== record.get('componentMaterialLotCode')
            );
          });
          const filterList = (groupRecords || []).map(_record =>
            _record.get('componentMaterialLotCode'),
          );
          return (
            canEdit &&
            record.get('ncRecordType') === 'EO_MATERIAL_NC' && (
              <Select
                name="componentMaterialLotCode"
                onChange={val => handleChangeMaterialLotCode(val, record)}
                optionsFilter={optionRecord => {
                  return !filterList.includes(optionRecord.get('componentMaterialLotCode'));
                }}
              />
            )
          );
        },
        renderer: ({ record, value }) =>
          record.get('ncRecordType') === 'EO_MATERIAL_NC' ? value : null,
      },
      ...disposalFunList.map(item => ({
        name: item.dispositionFunction,
        header: item.description,
        width: 150,
        editor: () => canEdit && item.dispositionFunction !== 'PASS' && <NumberField />,
      })),
      {
        name: 'uomLov',
        width: 130,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.reworkRouterFlag && {
        name: 'reworkRouterLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.reworkSourceFlag && {
        name: 'reworkStepLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.reworkSourceFlag && {
        name: 'reworkWorkcellLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.reworkSourceFlag && {
        name: 'reworkOperationLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.degradeFlag && {
        name: 'degradeLevel',
        width: 150,
        editor: () => canEdit && <TextField />,
      },
      dynamicDisplayFlag.degradeFlag && {
        name: 'degradeMaterialLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.degradeFlag && {
        name: 'degradeRevisionCode',
        width: 150,
        editor: () => canEdit && <Select />,
      },
      dynamicDisplayFlag.eoDischargeFlag && {
        name: 'dischargeWorkcellLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.crossWoFlag && {
        name: 'crossOperationLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.concessionFlag && {
        name: 'interceptOperationLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.concessionFlag && {
        name: 'interceptWorkcellLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      dynamicDisplayFlag.concessionFlag && {
        name: 'interceptRouterStepLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      {
        name: 'remark',
        editor: () => canEdit && <TextField />,
      },
      {
        name: 'disposalUserLov',
        width: 150,
        editor: () => canEdit && <Lov />,
      },
      {
        name: 'disposalTime',
        width: 150,
        editor: () => canEdit && <DateTimePicker />,
      },
      {
        name: 'disposalApartment',
        width: 150,
        editor: () => canEdit && <TextField />,
      },
    ];
  }, [canEdit, disposalFunList, dynamicDisplayFlag, ncReportStatus]);

  return (
    <div className="hmes-style">
      <TarzanSpin
        dataSet={detailDs}
        spinning={saveLoading || querySelectLoading || queryLoading || queryInspectDocLoading}
      >
        <Header title={intl.get(`${modelPrompt}.title.detail`).d('不良记录单维护')}>
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
            </>
          ) : (
            <Button icon="edit-o" color={ButtonColor.primary} onClick={() => setCanEdit(true)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </Button>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={val => setActiveKey(val)}>
            <Panel key="baseInfo" header={intl.get(`${modelPrompt}.title.baseInfo`).d('基础信息')}>
              <Form dataSet={detailDs} columns={3} labelWidth={112}>
                <TextField name="ncReportNum" />
                <Select name="ncReportType" />
                <Select name="ncReportStatus" />
                <Lov name="siteLov" />
                <Select name="inspectBusinessType"
                  onChange={value => handleChangeBusRule(value)}
                  noCache
                />
                <Select name="reviewType" />
                <Select name="ncReviewStatus" />
                <NumberField name="inspectSumQty" />
                <NumberField name="okQty" />
                <NumberField name="ngQty" />
                <NumberField name="samplingQty" />
                <Select name="createMethod" />
                <SelectBox
                  name="disposalType"
                  onChange={handleChangeDisposalType}
                  onOption={({ record }) => {
                    const filterLine = ncRecordLineDs.filter(
                      _record => _record.get('ncRecordType') === 'EO_MATERIAL_NC',
                    );
                    return {
                      disabled: record.get('value') === 'ALL' && !!filterLine?.length,
                    };
                  }}
                />
                <NumberField name="destroyQty" />
                <C7nFormItemSort name="materialLov" itemWidth={['70%', '30%']}>
                  <Lov name="materialLov" />
                  <Select name="revisionCode" />
                </C7nFormItemSort>
                <TextArea name="remark" colSpan={3} rowSpan={2} newLine />
              </Form>
            </Panel>
            <Panel
              key="ncRecordLine"
              header={intl.get(`${modelPrompt}.title.ncRecordLine`).d('不良记录单行')}
            >
              <Table
                dataSet={ncRecordLineDs}
                columns={ncRecordLineColumns}
                filter={record => {
                  if (createMethod === 'QMS') {
                    return disposalType === 'ALL'
                      ? record.get('ncObjectType') === 'INSPECT_DOC'
                      : record.get('ncObjectType') !== 'INSPECT_DOC';
                  }
                  return true;
                }}
              />
            </Panel>
            {disposalType === 'ALL' && (
              <Panel key="ALL" header={intl.get(`${modelPrompt}.title.allDisposal`).d('整体处置')}>
                <Form dataSet={disposalDs} columns={3} disabled={!canEdit}>
                  <Lov name="dispositionGroupLov" />
                  <Lov name="dispositionFunctionLov" onChange={handleChangeFunction} />
                  <TextField name="functionTypeDesc" />
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptOperationLov" />
                  )}
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptWorkcellLov" />
                  )}
                  {dispositionFunction === 'CONCESSION_INTERCEPTION' && (
                    <Lov name="interceptRouterStepLov" />
                  )}
                  {dispositionFunction === 'EO_DISCHARGE' && <Lov name="dischargeWorkcellLov" />}
                  {dispositionFunction === 'CROSS_WO_INTERCEPT' && <Lov name="crossOperationLov" />}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkStepLov" />}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkWorkcellLov" />}
                  {dispositionFunction === 'REWORK_SOURCE' && <Lov name="reworkOperationLov" />}
                  {dispositionFunction === 'REWORK_ROUTER' && <Lov name="reworkRouterLov" />}
                  {dispositionFunction === 'DEGRADE' && <TextField name="degradeLevel" />}
                  {dispositionFunction === 'DEGRADE' && <Lov name="degradeMaterialLov" />}
                  {dispositionFunction === 'DEGRADE' && <Select name="degradeRevisionCode" />}
                  <TextField name="remark" />
                  <Lov name="disposalUserLov" />
                  <DateTimePicker name="disposalTime" />
                  <TextField name="disposalApartment" />
                </Form>
              </Panel>
            )}
            {disposalType === 'PART' && (
              <Panel
                key="PART"
                header={intl.get(`${modelPrompt}.title.partDisposal`).d('对象处置')}
              >
                <Table dataSet={disposalDs} columns={partDisposalColumns} />
              </Panel>
            )}
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(NcReportDetail);
