import React from 'react';
import { isFunction, isArray } from 'lodash';
import { Bind } from 'lodash-decorators';
import { Modal, Button, TextField, DataSet, Form, Output, notification } from 'choerodon-ui/pro';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_MDM } from 'alm/utils/config';

import BasicBMap from '../BasicBMap';

import styles from './index.less';

const organizationId = getCurrentOrganizationId();
// 根据省市区的名字查询省市区的代码
const queryRegionUrl = `${HALM_MDM}/v1/${organizationId}/asset-locations/region-by-name`;

let _modal;
const modalKey = Modal.key();

const { BMap } = window;

export default class MapModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.mapRef = React.createRef();
  }

  formDs = new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'address', // 文本输入框里的内容
        label: '详细地址',
        type: 'string',
      },
      {
        name: 'lonlat',
        type: 'string',
        label: '当前经纬度',
      },
      {
        name: 'lng', // 经度
        type: 'string',
      },
      {
        name: 'lat', // 纬度
        type: 'string',
      },
      {
        name: 'zoom', // 缩放比例
        type: 'number',
      },
      // 下面的数据是为了在点击“确认是这里”按钮时，获取到正确的信息
      // 避免出现用户随意输入了一个地址，比如：宝龙广场，既没有点击查询，有没有拖拽标记点，直接确认
      // 导致省市区这些数据缺失的问题
      {
        name: 'currentProvince', // 省
        type: 'string',
      },
      {
        name: 'currentCity', // 市
        type: 'string',
      },
      {
        name: 'currentDistrict', // 区
        type: 'string',
      },
      {
        name: 'currentAddress', // 详细地址 - 截掉省市区之后的详细地址
        type: 'string',
      },
    ],
  });

  // modal的属性
  get modalProps() {
    const { onClose } = this.props;
    return {
      key: modalKey,
      destroyOnClose: true,
      closable: true,
      style: {
        width: 900,
      },
      title: '地图校准',
      children: this.getChildren(),
      footer: null,
      onClose,
      className: styles['map-modal'],
    };
  }

  componentDidMount() {
    const {
      locationData: { province, city, district, address, lng, lat, zoom },
    } = this.props;
    const p = province || '';
    const c = city || '';
    const d = district || '';
    const a = address || '';
    const newAddress = `${p}${c}${d}${a}`;
    // 初始化ds数据
    this.formDs.current.set('address', newAddress);
    this.formDs.current.set('lng', lng);
    this.formDs.current.set('lat', lat);
    this.formDs.current.set('zoom', zoom);
    if (lng && lat) {
      this.formDs.current.set('lonlat', `${lng},${lat}`);
    }

    this.formDs.current.set('currentProvince', p);
    this.formDs.current.set('currentCity', c);
    this.formDs.current.set('currentDistrict', d);
    this.formDs.current.set('currentAddress', a);

    // 挂载modal
    this.renderDrawer();
  }

  componentWillUnmount() {
    _modal.close();
  }

  @Bind()
  renderDrawer() {
    _modal = Modal.open(this.modalProps);
  }

  @Bind()
  async handleSearch() {
    if (this.mapRef && this.mapRef.current && this.mapRef.current.handleSearch) {
      const { current } = this.mapRef;

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { address, ...otherProps } = current.props;
      current.props = Object.assign(otherProps, {
        address: this.formDs.current.get('address'),
      });
      current.handleSearch();
    }
  }

  /**
   * 地图数据加载成功的回调
   * @param data 接口返回的数据
   */
  @Bind()
  dataLoadSuccess(data) {
    const lon = parseFloat(data.lng).toFixed(6);
    const lat = parseFloat(data.lat).toFixed(6);
    const { province, city, district, address } = data; // 这个address是去掉省市区之后的详细地址
    this.formDs.current.set('lonlat', `${lon},${lat}`);
    this.formDs.current.set('lng', lon);
    this.formDs.current.set('lat', lat);
    this.formDs.current.set('currentProvince', province);
    this.formDs.current.set('currentCity', city);
    this.formDs.current.set('currentDistrict', district);
    this.formDs.current.set('currentAddress', address);
  }

  /**
   * marker拖拽事件
   */
  @Bind()
  dragEvents(marker) {
    if (marker) {
      marker.addEventListener('dragend', this.showInfo); // 拖拽结束时触发此事件
    }
  }

  /**
   * 拖拽结束的回调函数
   * @param {*} e
   */
  @Bind()
  showInfo(e) {
    const gc = new BMap.Geocoder();
    const { formDs } = this;
    gc.getLocation(e.point, data => {
      const {
        address,
        point: { lng, lat },
        addressComponents,
      } = data;
      const { province, city, district, street, streetNumber } = addressComponents;

      formDs.current.set('lonlat', `${lng},${lat}`);
      formDs.current.set('address', address);
      formDs.current.set('lng', lng);
      formDs.current.set('lat', lat);
      this.formDs.current.set('currentProvince', province);
      this.formDs.current.set('currentCity', city);
      this.formDs.current.set('currentDistrict', district);
      this.formDs.current.set('currentAddress', `${street}${streetNumber}`); // 详细地址
    });
  }

  getChildren() {
    const mapProps = {
      address: this.formDs.current.get('address'),
      initialZoom: this.formDs.current.get('zoom'),
      initialLon: this.formDs.current.get('lng'),
      initialLat: this.formDs.current.get('lat'),
      allowedScrollWheelZoom: true,
      allowedDrag: true,
      dataLoadSuccess: this.dataLoadSuccess,
      dragEvents: this.dragEvents,
    };
    return (
      <React.Fragment>
        <Form dataSet={this.formDs} columns={7}>
          <div name="address" colSpan={4}>
            <TextField
              name="address"
              colSpan={2}
              style={{ width: 'calc(100% - 80px)', marginTop: -5 }}
              onChange={value => {
                this.formDs.current.set('address', value);
              }}
            />
            <Button
              color="primary"
              style={{ marginLeft: 10, verticalAlign: 'bottom' }}
              onClick={this.handleSearch}
            >
              查询
            </Button>
          </div>
          <div name="lonlat" colSpan={3}>
            <Output
              name="lonlat"
              style={{
                width: 'calc(100% - 120)',
                marginTop: -5,
                textAlign: 'left',
              }}
            />{' '}
            <Button
              color="primary"
              style={{ marginLeft: 10, marginBottom: 10 }}
              onClick={this.handleChangeAddress}
            >
              确认是这里
            </Button>
          </div>
        </Form>
        <BasicBMap {...mapProps} ref={this.mapRef} />
      </React.Fragment>
    );
  }

  /**
   * 修改地点信息
   */
  @Bind()
  handleChangeAddress() {
    const { onClose, onChangeAddress } = this.props;
    // 调接口查询省市区代码 成功后再继续执行
    const province = this.formDs.current.get('currentProvince');
    const city = this.formDs.current.get('currentCity');
    const district = this.formDs.current.get('currentDistrict');
    request(queryRegionUrl, {
      method: 'GET',
      query: {
        fullName: `${province}/${city}/${district}`,
      },
    }).then(res => {
      // 成功的情况下直接返回 省市区代码的字符串 查不到的情况下啥也不返回
      if (res && isArray(res)) {
        if (isFunction(onChangeAddress)) {
          let zoom = 0;
          if (this.mapRef && this.mapRef.current && this.mapRef.current.map) {
            zoom = this.mapRef.current.map.getZoom();
          }
          const addressData = {
            province,
            city,
            district,
            zoom, // 地图缩放比例
            region: res, // 接口查出来的 省市区代码的数组
            address: this.formDs.current.get('currentAddress'),
            lng: this.formDs.current.get('lng'),
            lat: this.formDs.current.get('lat'),
          };
          onChangeAddress(addressData);
        }
        if (isFunction(onClose)) {
          onClose();
        }
        _modal.close();
      } else {
        notification.error({
          message: '该省市区不存在,请重新选择！',
        });
      }
    });
  }

  render() {
    return <></>;
  }
}
