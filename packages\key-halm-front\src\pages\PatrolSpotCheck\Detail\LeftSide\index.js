import React from 'react';
import { Icon, Skeleton } from 'choerodon-ui';

import { yesOrNoRender } from 'utils/renderer';

import classNames from 'classnames';
import styles from './index.module.less';
import peopleImg from '../../assets/planner.svg';
import getLangs from '../../Langs';

export default class LeftSide extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { detail } = this.props;
    const {
      woTypeName,
      maintSiteName,
      spotCheckObjType,
      spotCheckObjName,
      scheduledStartDate,
      scheduledFinishDate,
      durationScheduled,
      durationUomMeaning,
      actualStartDate,
      actualFinishDate,
      plannerGroupName,
      plannerName,
      ownerGroupName,
      ownerName,
      needSignFlag = 0,
      woType = {},
    } = detail;

    const { requiredBfWorkCode } = woType;

    const lineData = [
      {
        label: getLangs('WO_TYPE'),
        value: woTypeName,
      },
      {
        label: getLangs('MAINTSITE'),
        value: maintSiteName,
      },
      {
        label: getLangs('SPOT_CHECK_OBJ_TYPE'),
        value: spotCheckObjType === 'CHECKROUTE' ? getLangs('ROUTE') : getLangs('POINT'),
      },
      {
        label: spotCheckObjType === 'CHECKROUTE' ? getLangs('ROUTE') : getLangs('POINT'),
        value: spotCheckObjName,
      },
      {
        label: getLangs('PLAN_TIME'),
        value: `${scheduledStartDate || ''}-${scheduledFinishDate || ''}`,
      },
      {
        label: getLangs('PLAN_CYCLE'),
        value: `${durationScheduled || ''}（${durationUomMeaning || ''}）`,
      },
    ];

    if (
      spotCheckObjType === 'CHECKPOINT' &&
      !!requiredBfWorkCode &&
      requiredBfWorkCode !== 'NONE'
    ) {
      lineData.push({
        label: getLangs('NEED_SIGN_FLAG'),
        value: yesOrNoRender(needSignFlag),
      });
    }

    const peopleData = [
      {
        type: 'cell',
        data: {
          groupName: plannerGroupName || '-',
          Name: plannerName || '-',
          label: getLangs('PLANNER_NAME'),
          img: <img src={peopleImg} alt="" />,
        },
      },
      {
        type: 'middle',
      },
      {
        type: 'cell',
        data: {
          groupName: ownerGroupName || '-',
          Name: ownerName || '-',
          label: getLangs('OWNER_NAME'),
          img: <img src={peopleImg} alt="" />,
        },
      },
    ];

    const dateData = [
      {
        type: 'cell',
        data: {
          groupName: actualStartDate || '-',
          label: getLangs('ACTUAL_START_DATE'),
          img: <Icon type="schedule" style={{ fontSize: 16, color: '#3889FF', marginRight: 4 }} />,
        },
      },
      {
        type: 'middle',
      },
      {
        type: 'cell',
        data: {
          groupName: actualFinishDate || '-',
          label: getLangs('ACTUAL_FINISH_DATE'),
          img: <Icon type="schedule" style={{ fontSize: 16, color: '#3889FF', marginRight: 4 }} />,
        },
      },
    ];

    const cardData = actualStartDate || actualFinishDate ? [peopleData, dateData] : [peopleData];

    return (
      <Skeleton loading={this.props.queryDeatilLoading}>
        <div className={styles['side-container']}>
          {lineData.map((i, index) => {
            return (
              <div className={styles['basic-line']} key={`basic-line-${index + 1}`}>
                <span className={styles['basic-line-label']}>{i.label}：</span>
                <span
                  className={classNames({
                    [styles['basic-line-value']]: true,
                    [styles.bold]: index === 3,
                  })}
                >
                  {i.value}
                </span>
              </div>
            );
          })}
          {cardData.map(card => (
            <div className={styles['people-line']}>
              {card.map((i, index) => {
                const { type, data } = i;
                const { groupName, Name, label, img } = data || {};

                return type === 'middle' ? (
                  <div
                    className={styles['people-line-middle']}
                    key={`people-line-middle-${index + 1}`}
                  />
                ) : (
                  <div className={styles['people-line-cell']} key={`people-line-cell-${index + 1}`}>
                    <div className={styles['people-line-cell-line']}>{groupName}</div>
                    <div className={styles['people-line-cell-line']}>{Name}</div>
                    <div className={styles['people-line-cell-line']}>
                      {img}
                      {label}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </Skeleton>
    );
  }
}
