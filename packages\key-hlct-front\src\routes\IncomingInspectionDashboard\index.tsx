/*
 * @Description: 来料检验实时看板
 */
import React, { useEffect, useState, useRef, useMemo } from 'react';
import moment from 'moment';
import * as echarts from 'echarts';
import 'echarts-gl';
import { DataSet, DatePicker } from 'choerodon-ui/pro';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { openTab } from 'utils/menuTab';
import styles from './index.module.less';
import './datepicker-fix.module.less'; // 引入DatePicker样式修复
import ECharts from './components/ECharts';
import { getPie3D, getParametricEquation } from './pieChartHelper';
import { dashboardService } from './services';
import type {
  ProgressStatsData,
  DefectiveStatsData,
  MaterialStatsData,
  SupplierStatsData,
} from './services';

// 扩展类型，添加前端状态管理需要的属性
interface ExtendedSupplierStatsData extends SupplierStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}

interface ExtendedMaterialStatsData extends MaterialStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}



const OverflowScroller = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [style, setStyle] = useState<any>({});

  useEffect(() => {
    const container = containerRef.current;

    if (container) {
      const checkOverflow = () => {
        const isCurrentlyOverflowing = container.scrollWidth > container.clientWidth;
        setIsOverflowing(isCurrentlyOverflowing);

        if (isCurrentlyOverflowing) {
          const overflowAmount = container.scrollWidth - container.clientWidth;
          const duration = Math.max(10, overflowAmount / 15); // 15px/s, min 10s
          setStyle({
            '--scroll-amount': `-${overflowAmount + 16}px`, // Add some padding
            '--scroll-duration': `${duration}s`,
          });
        }
      };

      const resizeObserver = new ResizeObserver(checkOverflow);
      resizeObserver.observe(container);

      checkOverflow();
      document.fonts.ready.then(() => {
        checkOverflow();
      });

      return () => resizeObserver.disconnect();
    }
  }, [children]);

  return (
    <div ref={containerRef} className={`${styles.tableCell} ${className}`}>
      <span
        className={isOverflowing ? styles.scrollingText : ''}
        style={style as React.CSSProperties}
      >
        {children}
      </span>
    </div>
  );
};

// 移除假数据，使用真实接口数据

const MaterialFilterContent = ({ dataSet, onSelect, selectedRecord }) => {
  return (
    <div className={styles.materialFilterList}>
      <div className={styles.materialFilterListHeader}>
        <span>物料编码</span>
        <span>物料描述</span>
      </div>
      <div className={styles.materialFilterListBody}>
        {dataSet.records.map(record => (
          <div
            key={record.get('code')}
            className={`${styles.materialFilterListRow} ${
              selectedRecord && selectedRecord.get('code') === record.get('code')
                ? styles.selected
                : ''
            }`}
            onClick={() => onSelect(record)}
          >
            <span>{record.get('code')}</span>
            <span>{record.get('description')}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

// 分页滚动hook - 当滚动到底部时加载更多数据（带平滑动画）
const usePaginatedTableScroll = (scrollRef, data, pagination, loadMoreData, scrollSpeed = 2000) => {
  useEffect(() => {
    const tableContainer = scrollRef.current;
    if (!tableContainer) return;

    let isPaused = false;
    let animationId: number | null = null;
    let scrollTimeout: any = null;
    let isScrolling = false;

    // 平滑滚动函数
    const smoothScroll = (targetScrollTop: number, duration = 800) => {
      if (isScrolling) return;
      isScrolling = true;

      const startScrollTop = tableContainer.scrollTop;
      const distance = targetScrollTop - startScrollTop;
      const startTime = performance.now();

      const animateScroll = (currentTime: number) => {
        if (isPaused || !tableContainer) {
          isScrolling = false;
          return;
        }

        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数（easeInOutCubic）
        const easeInOutCubic =
          progress < 0.5
            ? 4 * progress ** 3
            : 1 - (-2 * progress + 2) ** 3 / 2;

        const currentScrollTop = startScrollTop + distance * easeInOutCubic;
        tableContainer.scrollTop = currentScrollTop;

        if (progress < 1) {
          animationId = requestAnimationFrame(animateScroll);
        } else {
          isScrolling = false;
          // 检查是否到达底部
          if (
            Math.abs(
              targetScrollTop - (tableContainer.scrollHeight - tableContainer.clientHeight),
            ) < 5
          ) {
            handleReachBottom();
          }
        }
      };

      animationId = requestAnimationFrame(animateScroll);
    };

    // 处理到达底部的逻辑
    const handleReachBottom = () => {
      if (pagination.hasMore && !pagination.loading) {
        loadMoreData();
        // 数据加载完成后重置到顶部
        setTimeout(() => {
          if (tableContainer && !isPaused) {
            smoothScroll(0, 1200); // 重置滚动用更长的动画时间
          }
        }, 1500);
      } else {
        // 没有更多数据时，直接重置到顶部
        setTimeout(() => {
          if (tableContainer && !isPaused) {
            smoothScroll(0, 1000);
          }
        }, 1000);
      }
    };

    const doScroll = () => {
      if (isPaused || !tableContainer || isScrolling) return;

      const rowElement = tableContainer.querySelector(`.${styles.tableRow}, .${styles.tableRow2}`);
      const scrollStep = rowElement ? rowElement.offsetHeight : 50;
      const currentScrollTop = tableContainer.scrollTop;
      const maxScrollTop = tableContainer.scrollHeight - tableContainer.clientHeight;

      // 计算下一个滚动位置
      let nextScrollTop = currentScrollTop + scrollStep;

      // 如果接近底部，直接滚动到底部
      if (nextScrollTop >= maxScrollTop - 10) {
        nextScrollTop = maxScrollTop;
      }

      // 使用平滑滚动
      smoothScroll(nextScrollTop, 600);
    };

    const startScrolling = () => {
      if (tableContainer.scrollHeight > tableContainer.clientHeight) {
        scrollTimeout = setTimeout(() => {
          if (!isPaused && !isScrolling) {
            doScroll();
            startScrolling(); // 递归调用以继续滚动
          }
        }, scrollSpeed);
      }
    };

    const stopScrolling = () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
        scrollTimeout = null;
      }
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }
      isScrolling = false;
    };

    const handleMouseEnter = () => {
      isPaused = true;
      stopScrolling();
    };

    const handleMouseLeave = () => {
      isPaused = false;
      isScrolling = false;
      startScrolling();
    };

    // 初始启动滚动
    startScrolling();

    tableContainer.addEventListener('mouseenter', handleMouseEnter);
    tableContainer.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      stopScrolling();
      if (tableContainer) {
        tableContainer.removeEventListener('mouseenter', handleMouseEnter);
        tableContainer.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [scrollSpeed, data, pagination, loadMoreData]);
};

const IncomingInspectionDashboard = () => {
  const [formattedTime, setFormattedTime] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [week, setWeek] = useState(moment().format('dddd'));

  const [startDate, setStartDate] = useState(moment('2025-03-01'));
  const [endDate, setEndDate] = useState(moment('2025-08-01'));

  const [activeDefectiveRow, setActiveDefectiveRow] = useState<number | null>(null);

  // 全屏相关状态
  const [isFullscreen, setIsFullscreen] = useState(false);
  const boardContainerRef = useRef<HTMLDivElement>(null);

  // 接口数据状态
  const [progressStats, setProgressStats] = useState<ProgressStatsData>({
    pending: 0,
    overdue: 0,
    inProgress: 0,
    completed: 0,
  });
  const [defectiveStats, setDefectiveStats] = useState<DefectiveStatsData[]>([]);
  const [materialStats, setMaterialStats] = useState<MaterialStatsData[]>([]);
  const [supplierStats, setSupplierStats] = useState<SupplierStatsData[]>([]);

  // 分页数据状态管理
  const [defectivePageData, setDefectivePageData] = useState<any[]>([]);
  const [defectivePageInfo, setDefectivePageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 1,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [materialPageData, setMaterialPageData] = useState<ExtendedMaterialStatsData[]>([]);
  const [materialPageInfo, setMaterialPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 1,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [supplierPageData, setSupplierPageData] = useState<ExtendedSupplierStatsData[]>([]);
  const [supplierPageInfo, setSupplierPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 1,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [selectedMaterial, setSelectedMaterial] = useState<Record | null>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<Record | null>(null);

  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [tempSelectedMaterial, setTempSelectedMaterial] = useState<Record | null>(null);

  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);
  const [tempSelectedSupplier, setTempSelectedSupplier] = useState<Record | null>(null);

  const tableScrollRef = useRef<HTMLDivElement>(null);
  const materialTableScrollRef = useRef<HTMLDivElement>(null);
  const supplierTableScrollRef = useRef<HTMLDivElement>(null);
  const pieChartRef = useRef<HTMLDivElement>(null);

  // 分页加载函数
  const loadMoreDefectiveData = async () => {
    if (defectivePageInfo.loading || !defectivePageInfo.hasMore) return;

    setDefectivePageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      const response = await dashboardService.getDefectiveDetails(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        undefined, // 不筛选特定不良项目
        defectivePageInfo.current,
        defectivePageInfo.pageSize,
      );

      // 转换数据格式以匹配现有的显示结构
      const newData = response.content.map((item, index) => ({
        id: defectivePageInfo.current * defectivePageInfo.pageSize + index + 1,
        inspectionId: item.inspectionId,
        materialCode: item.materialCode,
        material: item.material,
        supplier: item.supplier,
        supplierCode: item.supplierCode,
        creationDate: item.creationDate,
        status: '检验完成',
        isPassed: false,
        defectiveItem: item.defectiveItem,
        inspector: item.inspector || '未知',
        arrivalBatch: `B${String(item.inspectDocId).slice(-3)}`,
        overdueDays: 0,
        isNewlyLoaded: true, // 标记新加载的数据
      }));

      setDefectivePageData(prev => [...prev, ...newData]);
      setDefectivePageInfo(prev => ({
        ...prev,
        current: prev.current + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setDefectivePageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载不良信息数据失败:', error);
      setDefectivePageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreMaterialData = async () => {
    if (materialPageInfo.loading || !materialPageInfo.hasMore) return;

    setMaterialPageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      // 需要根据供应商编码找到对应的ID
      let supplierIdForFilter: number | undefined;
      if (selectedSupplier) {
        // 从供应商数据中找到对应的ID
        const supplierItem = [...supplierPageData, ...supplierStats].find(
          item => item.supplierCode === selectedSupplier.get('code'),
        );
        supplierIdForFilter = supplierItem?.supplierId;
      }

      const response = await dashboardService.getMaterialStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        supplierIdForFilter, // 使用找到的供应商ID进行筛选
        materialPageInfo.current,
        materialPageInfo.pageSize,
      );

      // 转换数据格式并标记新加载的数据
      const newData = response.content.map((item, index) => ({
        ...item,
        id: materialPageInfo.current * materialPageInfo.pageSize + index + 1, // 前端生成连续序号
        isNewlyLoaded: true,
      }));

      setMaterialPageData(prev => [...prev, ...newData]);
      setMaterialPageInfo(prev => ({
        ...prev,
        current: prev.current + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载物料数据失败:', error);
      setMaterialPageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreSupplierData = async () => {
    if (supplierPageInfo.loading || !supplierPageInfo.hasMore) return;

    setSupplierPageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      // 需要根据物料编码找到对应的ID
      let materialIdForFilter: number | undefined;
      if (selectedMaterial) {
        // 从物料数据中找到对应的ID
        const materialItem = [...materialPageData, ...materialStats].find(
          item => item.materialCode === selectedMaterial.get('code'),
        );
        materialIdForFilter = materialItem?.materialId;
      }

      const response = await dashboardService.getSupplierStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        materialIdForFilter, // 使用找到的物料ID进行筛选
        supplierPageInfo.current,
        supplierPageInfo.pageSize,
      );

      // 转换数据格式并标记新加载的数据
      const newData = response.content.map((item, index) => ({
        ...item,
        id: supplierPageInfo.current * supplierPageInfo.pageSize + index + 1, // 前端生成连续序号
        isNewlyLoaded: true,
      }));

      setSupplierPageData(prev => [...prev, ...newData]);
      setSupplierPageInfo(prev => ({
        ...prev,
        current: prev.current + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载供应商数据失败:', error);
      setSupplierPageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  // 移除filteredInspections，直接使用接口数据

  // 获取接口数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        const startDateStr = startDate.format('YYYY-MM-DD');
        const endDateStr = endDate.format('YYYY-MM-DD');

        // 获取进度统计数据
        const progressData = await dashboardService.getProgressStats(startDateStr, endDateStr);
        setProgressStats(progressData);

        // 获取不良统计数据
        const defectiveData = await dashboardService.getDefectiveStats(startDateStr, endDateStr);
        setDefectiveStats(defectiveData);

        // 获取物料统计数据（第一页）
        // 初始加载时不应用筛选，获取所有数据用于生成筛选列表
        const materialData = await dashboardService.getMaterialStats(startDateStr, endDateStr, undefined, 0, 20);
        setMaterialStats(materialData.content);
        // 为初始数据添加序号
        const materialDataWithId = materialData.content.map((item, index) => ({
          ...item,
          id: index + 1, // 第一页从1开始
        }));
        setMaterialPageData(materialDataWithId);
        setMaterialPageInfo({
          current: 1,
          pageSize: 20,
          total: materialData.totalElements,
          hasMore: materialData.number < materialData.totalPages - 1,
          loading: false,
        });

        // 获取供应商统计数据（第一页）
        // 初始加载时不应用筛选，获取所有数据用于生成筛选列表
        const supplierData = await dashboardService.getSupplierStats(startDateStr, endDateStr, undefined, 0, 20);
        setSupplierStats(supplierData.content);
        // 为初始数据添加序号
        const supplierDataWithId = supplierData.content.map((item, index) => ({
          ...item,
          id: index + 1, // 第一页从1开始
        }));
        setSupplierPageData(supplierDataWithId);
        setSupplierPageInfo({
          current: 1,
          pageSize: 20,
          total: supplierData.totalElements,
          hasMore: supplierData.number < supplierData.totalPages - 1,
          loading: false,
        });

      } catch (error) {
        console.error('获取数据失败:', error);
      }
    };

    fetchData();
  }, [startDate, endDate]);

  // 监听筛选条件变化，重新加载对应的数据
  useEffect(() => {
    if (selectedSupplier && materialPageData.length === 0) {
      // 当选择了供应商且物料数据为空时，加载物料数据
      loadMoreMaterialData();
    }
  }, [selectedSupplier]);

  useEffect(() => {
    if (selectedMaterial && supplierPageData.length === 0) {
      // 当选择了物料且供应商数据为空时，加载供应商数据
      loadMoreSupplierData();
    }
  }, [selectedMaterial]);

  const pieChartData = useMemo(() => {
      const safeProgressStats = progressStats || {
        completed: 0,
        pending: 0,
        overdue: 0,
        inProgress: 0,
      };

      return [
        { name: '检验完成', value: safeProgressStats.completed || 0, itemStyle: { color: '#4a90e2' } },
        { name: '待检验', value: safeProgressStats.pending || 0, itemStyle: { color: '#f5a623' } },
        { name: '超期待检', value: safeProgressStats.overdue || 0, itemStyle: { color: '#d0021b' } },
        { name: '检验中', value: safeProgressStats.inProgress || 0, itemStyle: { color: '#50e3c2' } },
      ].sort((a, b) => a.value - b.value);
  }, [progressStats]);

  const defectiveStatsData = useMemo(() => {
      const safeDefectiveStats = Array.isArray(defectiveStats) ? defectiveStats : [];
      const topDefectiveData = safeDefectiveStats.slice(0, 5);
      const categories = ['', ...topDefectiveData.map(s => s?.name || '未知'), ''];
      const counts = [
        { value: 0, itemStyle: { color: 'transparent' } },
        ...topDefectiveData.map(s => s?.count || 0),
        { value: 0, itemStyle: { color: 'transparent' } },
      ];
      const ratios = [
        null, 
        ...topDefectiveData.map(s => (s?.ratio || 0) / 100),
        null,
      ];

      return {
        categories,
        counts,
        ratios,
      };
    
  }, [defectiveStats]);

  // 使用真实的不良详情数据
  const defectiveInfoData = useMemo(() => {
    try {
      // 确保 defectivePageData 是数组
      return Array.isArray(defectivePageData) ? defectivePageData : [];
    } catch (error) {
      console.error('处理不良详情数据时发生错误:', error);
      return [];
    }
  }, [defectivePageData]);



  // 重置分页数据的函数
  const resetDefectivePagination = () => {
    const initialData = defectiveInfoData.slice(0, 20);
    setDefectivePageData(initialData);
    setDefectivePageInfo({
      current: 1,
      pageSize: 20,
      total: defectiveInfoData.length,
      hasMore: defectiveInfoData.length > 20,
      loading: false,
    });
  };



  // 初始化和重置分页数据
  useEffect(() => {
    resetDefectivePagination();
  }, [defectiveInfoData]);



  // 使用分页滚动hook
  usePaginatedTableScroll(
    tableScrollRef,
    defectivePageData,
    defectivePageInfo,
    loadMoreDefectiveData,
    2000,
  );
  usePaginatedTableScroll(
    materialTableScrollRef,
    materialPageData,
    materialPageInfo,
    loadMoreMaterialData,
    2000,
  );
  usePaginatedTableScroll(
    supplierTableScrollRef,
    supplierPageData,
    supplierPageInfo,
    loadMoreSupplierData,
    2000,
  );

  const onDefectiveChartClick = (params: any) => {
    if (params.componentType === 'series' && params.seriesType === 'bar') {
      const projectName = params.name;
      // 跳过空的占位项
      if (!projectName) return;

      const clickedData = defectivePageData.find(d => d.defectiveItem === projectName);
      if (clickedData) {
        setActiveDefectiveRow(clickedData.id as any);
        setTimeout(() => {
          setActiveDefectiveRow(null);
        }, 5000);
      }
    }
  };

  // 处理检验单号点击跳转
  const handleInspectionIdClick = (inspectionId: string) => {
    // 跳转到检验平台的全部页签
    openTab({
      title: '检验平台',
      key: '/hwms/inspection-platform-wg/list',
      path: '/hwms/inspection-platform-wg/list',
      state: {
        sourceDocId: inspectionId,
        sourceDocNum: inspectionId,
      },
    });
  };

  // 从物料统计数据中提取物料列表
  const allMaterials = useMemo(() => {
    try {
      const materials: { [key: string]: { code: string; description: string } } = {};

      // 从物料分页数据中提取
      if (Array.isArray(materialPageData)) {
        (materialPageData as MaterialStatsData[]).forEach(item => {
          if (item?.materialCode) {
            materials[item.materialCode] = {
              code: item.materialCode,
              description: item.material || '未知物料',
            };
          }
        });
      }

      // 从物料统计数据中提取（如果有的话）
      if (Array.isArray(materialStats)) {
        (materialStats as MaterialStatsData[]).forEach(item => {
          if (item?.materialCode) {
            materials[item.materialCode] = {
              code: item.materialCode,
              description: item.material || '未知物料',
            };
          }
        });
      }

      return Object.values(materials);
    } catch (error) {
      console.error('处理物料数据时发生错误:', error);
      return [];
    }
  }, [materialPageData, materialStats]);

  // 从供应商统计数据中提取供应商列表
  const allSuppliers = useMemo(() => {
    try {
      const suppliers: { [key: string]: { code: string; description: string } } = {};

      // 从供应商分页数据中提取
      if (Array.isArray(supplierPageData)) {
        (supplierPageData as ExtendedSupplierStatsData[]).forEach(item => {
          if (item?.supplierCode) {
            suppliers[item.supplierCode] = {
              code: item.supplierCode,
              description: item.supplier || '未知供应商',
            };
          }
        });
      }

      // 从供应商统计数据中提取（如果有的话）
      if (Array.isArray(supplierStats)) {
        (supplierStats as SupplierStatsData[]).forEach(item => {
          if (item?.supplierCode) {
            suppliers[item.supplierCode] = {
              code: item.supplierCode,
              description: item.supplier || '未知供应商',
            };
          }
        });
      }

      return Object.values(suppliers);
    } catch (error) {
      console.error('处理供应商数据时发生错误:', error);
      return [];
    }
  }, [supplierPageData, supplierStats]);

  const materialDataSet = useMemo(
    () =>
      new DataSet({
        data: allMaterials,
        fields: [
          { name: 'code', type: FieldType.string, label: '物料编码' },
          { name: 'description', type: FieldType.string, label: '物料描述' },
        ],
        selection: DataSetSelection.single,
      }),
    [allMaterials],
  );

  const supplierDataSet = useMemo(
    () =>
      new DataSet({
        data: allSuppliers,
        fields: [
          { name: 'code', type: FieldType.string, label: '供应商编码' },
          { name: 'description', type: FieldType.string, label: '供应商描述' },
        ],
        selection: DataSetSelection.single,
      }),
    [allSuppliers],
  );

  const openMaterialModal = () => {
    setTempSelectedMaterial(selectedMaterial);
    setIsMaterialModalOpen(true);
  };

  const handleMaterialSelect = (record: any) => {
    setTempSelectedMaterial(record);
  };

  const handleMaterialConfirm = () => {
    setSelectedMaterial(tempSelectedMaterial);
    if (tempSelectedMaterial) {
      setSelectedSupplier(null);
      // 重置供应商数据，重新加载
      setSupplierPageData([]);
      setSupplierPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });
    }
    setIsMaterialModalOpen(false);
  };

  const handleMaterialCancel = () => {
    setIsMaterialModalOpen(false);
  };

  const openSupplierModal = () => {
    setTempSelectedSupplier(selectedSupplier);
    setIsSupplierModalOpen(true);
  };

  const handleSupplierSelect = (record: any) => {
    setTempSelectedSupplier(record);
  };

  const handleSupplierConfirm = () => {
    setSelectedSupplier(tempSelectedSupplier);
    if (tempSelectedSupplier) {
      setSelectedMaterial(null);
      // 重置物料数据，重新加载
      setMaterialPageData([]);
      setMaterialPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });
    }
    setIsSupplierModalOpen(false);
  };

  const handleSupplierCancel = () => {
    setIsSupplierModalOpen(false);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setFormattedTime(moment().format('YYYY-MM-DD HH:mm:ss'));
      setWeek(moment().format('dddd'));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 全屏检测和缩放处理
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );

      setIsFullscreen(isCurrentlyFullscreen);

      // 添加或移除缩放类
      if (boardContainerRef.current) {
        if (isCurrentlyFullscreen) {
          boardContainerRef.current.classList.add(styles.simpleFullscreen);
        } else {
          boardContainerRef.current.classList.remove(styles.simpleFullscreen);
        }
      }
    };

    // 监听全屏变化事件
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // 初始检查
    handleFullscreenChange();

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    const optionData = pieChartData;
    let myChart: echarts.ECharts | null = null;
    if (pieChartRef.current) {
      myChart = echarts.init(pieChartRef.current);
    } else {
      return () => {};
    }

    const option: any = getPie3D(optionData, 0.8);
    myChart.setOption(option);

    // 添加窗口resize监听，实现图表自适应
    const handleResize = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    // 使用ResizeObserver监听容器大小变化
    let resizeObserver: ResizeObserver | null = null;
    if (pieChartRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        handleResize();
      });
      resizeObserver.observe(pieChartRef.current);
    }

    // 初始化时也调用一次resize确保正确显示
    setTimeout(() => {
      if (myChart) {
        myChart.resize();
      }
    }, 100);

    option.legend = {
      show: true,
      bottom: '5%',
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      itemGap: 20,
      itemWidth: 15,
      itemHeight: 15,
      data: optionData.map(item => ({
        name: item.name,
        icon: 'rect',
      })),
    };

    option.series.push({
      name: 'pie2d',
      type: 'pie',
      labelLine: {
        show: true,
        length: 30,
        length2: 60,
        smooth: false,
        lineStyle: {
          color: '#fff',
          width: 1,
        },
      },
      startAngle: -20,
      clockwise: false,
      radius: ['35%', '40%'],
      center: ['50%', '45%'],
      data: optionData,
      itemStyle: {
        opacity: 0,
      },
      label: {
        show: true,
        position: 'outside',
        distanceToLabelLine: 5,
        alignTo: 'labelLine',
        bleedMargin: 5,
        formatter: (params: any) => {
          const item = optionData.find(i => i.name === params.name);
          if (!item) {
            return '';
          }
          const bfb = ((item.value / pieChartData.reduce((a, b) => a + b.value, 0)) * 100).toFixed(
            0,
          );
          // 创建动态的rich样式名称
          const colorKey = `color_${params.dataIndex}`;
          return `{${colorKey}|${bfb}%}\n{name|${params.name}}`;
        },
        rich: (() => {
          const richConfig: any = {
            name: {
              color: '#fff',
              fontSize: 14,
              lineHeight: 18,
            },
          };
          // 为每个数据项创建对应颜色的样式
          optionData.forEach((item, index) => {
            richConfig[`color_${index}`] = {
              fontSize: 18,
              lineHeight: 22,
              fontWeight: 'bold',
              color: item.itemStyle?.color || '#fff',
            };
          });
          return richConfig;
        })(),
      },
    });
    myChart.setOption(option);

    let selectedIndex = '';
    let hoveredIndex = '';

    myChart.on('click', function(params: any) {
      if (!myChart) return;
      const seriesIndex = params.seriesIndex;
      if (params.seriesName === 'pie2d') return;
      if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
        const isSelected = !option.series[seriesIndex].pieStatus.selected;
        const isHovered = option.series[seriesIndex].pieStatus.hovered;
        const k = option.series[seriesIndex].pieStatus.k;
        const startRatio = option.series[seriesIndex].pieData.startRatio;
        const endRatio = option.series[seriesIndex].pieData.endRatio;

        if (selectedIndex !== '' && selectedIndex !== `${seriesIndex}`) {
          const oldSelectedIndex = parseInt(selectedIndex, 10);
          option.series[oldSelectedIndex].parametricEquation = getParametricEquation(
            option.series[oldSelectedIndex].pieData.startRatio,
            option.series[oldSelectedIndex].pieData.endRatio,
            false,
            false,
            k,
            option.series[oldSelectedIndex].pieData.value,
          );
          option.series[oldSelectedIndex].pieStatus.selected = false;
        }

        option.series[seriesIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[seriesIndex].pieData.value,
        );
        option.series[seriesIndex].pieStatus.selected = isSelected;

        if (isSelected) {
          selectedIndex = `${seriesIndex}`;
        } else {
          selectedIndex = '';
        }
        myChart.setOption(option);
      }
    });

    myChart.on('mouseover', function(params: any) {
      if (!myChart) return;
      const seriesIndex = params.seriesIndex;
      if (hoveredIndex === `${seriesIndex}`) {
        return;
      }

      if (hoveredIndex !== '') {
        const oldHoveredIndex = parseInt(hoveredIndex, 10);
        if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
          const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
          const k = option.series[oldHoveredIndex].pieStatus.k;
          option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
            option.series[oldHoveredIndex].pieData.startRatio,
            option.series[oldHoveredIndex].pieData.endRatio,
            isSelected,
            false,
            k,
            option.series[oldHoveredIndex].pieData.value,
          );
          option.series[oldHoveredIndex].pieStatus.hovered = false;
          hoveredIndex = '';
        }
      }

      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
        if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
          const isSelected = option.series[seriesIndex].pieStatus.selected;
          const k = option.series[seriesIndex].pieStatus.k;
          option.series[seriesIndex].parametricEquation = getParametricEquation(
            option.series[seriesIndex].pieData.startRatio,
            option.series[seriesIndex].pieData.endRatio,
            isSelected,
            true,
            k,
            option.series[seriesIndex].pieData.value + 5,
          );
          option.series[seriesIndex].pieStatus.hovered = true;
          hoveredIndex = `${seriesIndex}`;
        }
      }
      myChart.setOption(option);
    });

    myChart.on('globalout', function() {
      if (!myChart) return;
      if (hoveredIndex !== '') {
        const oldHoveredIndex = parseInt(hoveredIndex, 10);
        if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
          const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
          const k = option.series[oldHoveredIndex].pieStatus.k;
          option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
            option.series[oldHoveredIndex].pieData.startRatio,
            option.series[oldHoveredIndex].pieData.endRatio,
            isSelected,
            false,
            k,
            option.series[oldHoveredIndex].pieData.value,
          );
          option.series[oldHoveredIndex].pieStatus.hovered = false;
          hoveredIndex = '';
        }
      }
      myChart.setOption(option);
    });

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      myChart?.dispose();
    };
  }, [pieChartData]);

  useEffect(() => {
    const timer = setInterval(() => {
      setFormattedTime(moment().format('YYYY-MM-DD HH:mm:ss'));
      setWeek(moment().format('dddd'));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const defectiveStatsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#777',
      textStyle: {
        color: '#fff',
      },
      formatter: (params: any) => {
        // 过滤掉空的占位项和null值
        const validParams = params.filter((param: any) => {
          return param.name !== '' && param.value !== null && param.value !== undefined;
        });
        if (validParams.length === 0) return '';

        const name = validParams[0].name;
        let result = `${name}<br/>`;

        validParams.forEach((param: any) => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          let value = param.value;

          if (typeof value === 'object' && value.value !== undefined) {
            value = value.value;
          }

          if (seriesName === '不良个数') {
            result += `${marker}${seriesName}: ${value}个<br/>`;
          } else if (seriesName === '不良占比') {
            result += `${marker}${seriesName}: ${(value * 100).toFixed(2)}%<br/>`;
          }
        });

        return result;
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true,
    },
    legend: {
      data: ['不良个数', '不良占比'],
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      itemGap: 20,
      bottom: '5%',
    },
    xAxis: [
      {
        type: 'category',
        data: defectiveStatsData.categories,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: '#fff',
          interval: 0,
          fontSize: 12,
          rotate: defectiveStatsData.categories.some(c => c.length > 5) ? 30 : 0,
          align: 'center',
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: '#fff',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
        boundaryGap: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: 10,
        interval: 1,
        axisLabel: {
          formatter: '{value}',
          color: '#fff',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '',
        min: 0,
        max: 1.0,
        interval: 0.1,
        axisLabel: {
          formatter(value: number) {
            return `${(value * 100).toFixed(2)}%`;
          },
          color: '#fff',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '不良个数',
        type: 'bar',
        yAxisIndex: 0,
        barWidth: '40%',
        barCategoryGap: '30%',
        showBackground: false,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(255, 154, 46, 0.2)' },
            { offset: 0.5, color: 'rgba(255, 154, 46, 0.8)' },
            { offset: 1, color: '#ff9a2e' },
          ]),
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(255, 154, 46, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 2,
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#fff',
          fontSize: 12,
          fontWeight: 'bold',
        },
        tooltip: {
          valueFormatter(value: number) {
            return `${value}个`;
          },
        },
        data: defectiveStatsData.counts,
      },
      {
        name: '不良占比',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          color: '#00ffc5',
          width: 2,
        },
        itemStyle: {
          color: '#00ffc5',
          borderColor: '#fff',
          borderWidth: 2,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0, 255, 197, 0.5)',
            },
            {
              offset: 1,
              color: 'rgba(0, 255, 197, 0)',
            },
          ]),
        },

        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            return params.value === null ? '' : `${(params.value * 100).toFixed(2)}%`;
          },
          color: '#00ffc5',
          fontSize: 12,
          fontWeight: 'bold',
        },
        data: defectiveStatsData.ratios,
      },
    ],
  };

  return (
    <div ref={boardContainerRef} className={styles.boardContainer}>
      <header className={styles.header}>
        <div className={styles.headerLeft}>
          <div className={styles.datePickerWrapper}>
            <DatePicker
              className="custom-date-picker"
              popupCls="custom-datepicker-popup"
              placeholder="选择开始时间"
              value={startDate}
              onChange={date => setStartDate(date)}
              style={{ background: 'transparent', border: 'none' }}
            />
          </div>
          <div className={styles.datePickerWrapper}>
            <DatePicker
              className="custom-date-picker"
              popupCls="custom-datepicker-popup"
              placeholder="选择结束时间"
              value={endDate}
              onChange={date => setEndDate(date)}
              style={{ background: 'transparent', border: 'none' }}
            />
          </div>
        </div>
        <div className={styles.headerCenter}>
          <div className={styles.title} />
          <div className={styles.enTitle}>DIGTAL VISUAL MONITORING SYSTEM</div>
        </div>
        <div className={styles.headerRight}>
          <div className={styles.timeWrapper}>
            <div className={styles.time}>{moment(formattedTime).format('HH:mm:ss')}</div>
            <div className={styles.date}>{moment(formattedTime).format('YYYY-MM-DD')}</div>
            <div className={styles.week}>{week}</div>
          </div>
        </div>
      </header>
      <main className={styles.mainContent}>
        <div className={styles.topRow}>
          <div className={styles.panel}>
            <div className={styles.panelHeader}>
              <div className={styles.panelTitle}>来料检验进度图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <div className={styles.pieChartBackground} />
              <div ref={pieChartRef} style={{ width: '100%', height: '100%' }} />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={styles.panelHeader} style={{ padding: '1.8rem 0 0 3.8rem' }}>
              <div className={styles.panelTitle}>来料检验不良项目统计图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <ECharts
                option={defectiveStatsOption}
                style={{ width: '100%', height: '100%', minHeight: '300px' }}
                onEvents={{ click: onDefectiveChartClick }}
                forceFullWidth
                notMerge={false}
                lazyUpdate={false}
              />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={styles.panelHeader} style={{ padding: '1.8rem 0 0 3.8rem' }}>
              <div className={styles.panelTitle}>不良项目信息</div>
            </div>
            <div className={styles.panelBody} style={{ padding: '0' }}>
              <div className={styles.customTable}>
                <div className={styles.tableHeader}>
                  <span>不良项目</span>
                  <span>检验单号</span>
                  <span>物料</span>
                  <span>供应商</span>
                  <span>下达时间</span>
                  <span>检验员</span>
                </div>
                <div className={styles.tableBody} ref={tableScrollRef}>
                  {defectivePageData.map(item => (
                    <div
                      className={`${styles.tableRow} ${
                        activeDefectiveRow === item.id ? styles.activeRow : ''
                      } ${item.isNewlyLoaded ? styles.entering : ''}`}
                      key={item.id}
                    >
                      <OverflowScroller>{item.defectiveItem}</OverflowScroller>
                      <OverflowScroller>
                        <span
                          className={styles.clickableLink}
                          onClick={() => handleInspectionIdClick(item.inspectionId)}
                          style={{ color: '#c3d1ff' }}
                        >
                          {item.inspectionId}
                        </span>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller>{item.supplier}</OverflowScroller>
                      <OverflowScroller>{item.creationDate}</OverflowScroller>
                      <OverflowScroller>{item.inspector}</OverflowScroller>
                    </div>
                  ))}
                  {defectivePageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.bottomRow}>
          <div className={styles.panel} style={{ paddingTop: '3rem' }}>
            <div className={styles.panelHeader} style={{ padding: '0.8rem 0 0 6rem' }}>
              <div className={styles.panelTitle}>来料检验-物料</div>
              <div className={styles.panelExtra}>
                <div className={`${styles.assetButton}`} onClick={openMaterialModal}>
                  物料筛选
                </div>
              </div>
            </div>
            <div className={styles.panelBody} style={{ padding: '0 0 0 1.5rem' }}>
              <div className={styles.customTable}>
                <div className={styles.tableHeader2}>
                  <span>序号</span>
                  <span>物料</span>
                  <span>物料到货批</span>
                  <span>物料合格率</span>
                </div>
                <div className={styles.tableBody} ref={materialTableScrollRef}>
                  {materialPageData.map(item => (
                    <div
                      className={`${styles.tableRow2} ${item.isNewlyLoaded ? styles.entering : ''}`}
                      key={item.id}
                    >
                      <OverflowScroller>
                        <div className={styles.tableIcon}>
                          <span style={{
                            fontSize: '14px',
                            color: '#fff',
                            fontWeight: 'bold',
                            lineHeight: '20px',
                          }}>{item.id}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller className={styles.alignRight}>
                        {item.arrivalBatchCount}
                      </OverflowScroller>
                      <OverflowScroller className={styles.alignRight}>
                        {item.passRate}
                      </OverflowScroller>
                    </div>
                  ))}
                  {materialPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className={styles.panel} style={{ paddingTop: '3rem' }}>
            <div className={styles.panelHeader} style={{ padding: '0.8rem 0 0 3rem' }}>
              <div className={styles.panelTitle}>来料检验-供应商</div>
              <div className={styles.panelExtra}>
                <div className={`${styles.assetButton}`} onClick={openSupplierModal}>
                  供应商筛选
                </div>
                <div className={styles.assetButton}>导出</div>
              </div>
            </div>
            <div className={styles.panelBody} style={{ padding: '0' }}>
              <div className={styles.customTable}>
                <div className={styles.tableHeader2}>
                  <span>序号</span>
                  <span>供应商</span>
                  <span>供应商到货批</span>
                  <span>供应商合格率</span>
                </div>
                <div className={styles.tableBody} ref={supplierTableScrollRef}>
                  {supplierPageData.map(item => (
                    <div
                      className={`${styles.tableRow2} ${item?.isNewlyLoaded ? styles.entering : ''}`}
                      key={item?.id}
                    >
                      <OverflowScroller>
                        <div className={styles.tableIcon}>
                          <span style={{
                            fontSize: '14px',
                            color: '#fff',
                            fontWeight: 'bold',
                            lineHeight: '20px',
                          }}>{item.id}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.supplier}</OverflowScroller>
                      <OverflowScroller className={styles.alignRight}>
                        {item.arrivalBatchCount}
                      </OverflowScroller>
                      <OverflowScroller className={styles.alignRight}>
                        {item.passRate}
                      </OverflowScroller>
                    </div>
                  ))}
                  {supplierPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {isMaterialModalOpen && (
        <div className={styles.modalOverlay}>
          <div className={styles.materialFilterModalContainer}>
            <div className={styles.materialFilterModalTitle}>
              <span className={styles.titleText}>物料筛选</span>
            </div>
            <div className={styles.materialFilterModalBody}>
              <MaterialFilterContent
                dataSet={materialDataSet}
                onSelect={handleMaterialSelect}
                selectedRecord={tempSelectedMaterial}
              />
            </div>
            <div className={styles.materialFilterModalFooter}>
              <button type="button" className={styles.modalButton} onClick={handleMaterialCancel}>
                取消
              </button>
              <button
                type="button"
                className={`${styles.modalButton} ${styles.primary}`}
                onClick={handleMaterialConfirm}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}

      {isSupplierModalOpen && (
        <div className={styles.modalOverlay}>
          <div className={styles.materialFilterModalContainer}>
            <div className={styles.materialFilterModalTitle}>
              <span className={styles.titleDecorator}>&lt;&lt;&lt;</span>
              <span className={styles.titleText}>供应商筛选</span>
              <span className={styles.titleDecorator}>&gt;&gt;&gt;</span>
            </div>
            <div className={styles.materialFilterModalBody}>
              <div className={styles.materialFilterList}>
                <div className={styles.materialFilterListHeader}>
                  <span>供应商编码</span>
                  <span>供应商描述</span>
                </div>
                <div className={styles.materialFilterListBody}>
                  {supplierDataSet.records.map(record => (
                    <div
                      key={record.get('code')}
                      className={`${styles.materialFilterListRow} ${
                        tempSelectedSupplier &&
                        tempSelectedSupplier.get('code') === record.get('code')
                          ? styles.selected
                          : ''
                      }`}
                      onClick={() => handleSupplierSelect(record)}
                    >
                      <span>{record.get('code')}</span>
                      <span>{record.get('description')}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className={styles.materialFilterModalFooter}>
              <button type="button" className={styles.modalButton} onClick={handleSupplierCancel}>
                取消
              </button>
              <button
                type="button"
                className={`${styles.modalButton} ${styles.primary}`}
                onClick={handleSupplierConfirm}
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncomingInspectionDashboard;
