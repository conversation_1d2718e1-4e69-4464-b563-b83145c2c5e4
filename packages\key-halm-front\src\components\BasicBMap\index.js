/**
 * 百度地图基础组件
 * 主要实现：创建标记点、标记点拖拽
 * 官方API： http://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference.html
 *
 * 客户化参数如下:
 * @param {String} address 地址 根据该地址进行经纬度查询
 * @param {String} mapHeight 地图高度                         默认400px
 * @param {String} mapWidth 地图宽度                          默认 100%
 * @param {Function} beforeDataLoad 地图数据加载前的处理
 * @param {Function} dataLoadSuccess 地图数据加载成功的回调    参数: data
 * @param {Function} dataLoadError 地图数据加载失败的回调
 * @param {Boolean} allowedDrag 是否允许拖拽                  默认false
 * @param {Function} dragEvents 拖拽事件                     参数: marker
 * @param {Boolean} allowedScrollWheelZoom 是否允许缩小放大   默认false
 * @param {Function} otherMapOperate 其他地图操作            参数: map, marker
 * @param {Boolean} initialZoom 默认zoom
 * @param {String} initialLon 默认经度
 * @param {String} initialLat 默认纬度
 *
 * 注：初次挂载的时候，如果有默认经纬度，就是用这个，不调用接口查询。
 */

import React from 'react';
import { Bind } from 'lodash-decorators';
import { isFunction, isUndefined } from 'lodash';
import { notification, Spin, Skeleton } from 'choerodon-ui';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { HALM_PFM } from 'alm/utils/config';
import uuid from 'uuid/v4';

const organizationId = getCurrentOrganizationId();
const queryLonAndLatUrl = `${HALM_PFM}/v1/${organizationId}/maps/query-lon-and-lat`;

const { BMap } = window;

export default class BasicMap extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false, // 加载状态
    };
    this.map = null;
    // 唯一的mapId，避免同一个id造成地图不重新渲染绘制地图
    this.mapUuid = `addressdiv${uuid()}`;
  }

  componentDidMount() {
    if (!isUndefined(BMap)) {
      this.map = new BMap.Map(this.mapUuid, { enableMapClick: false });
      const { initialLon, initialLat, address } = this.props;
      // 1. 如果有初始经纬度 就用该经纬度生成地图
      // 2. 没有经纬度 但有初始地址 用该地址查询其经纬度之后生成地图
      // 3. 初始经纬度、初始地址都没有 则默认定位到北京
      if (initialLon && initialLat) {
        this.generateMap(initialLon, initialLat);
      } else if (address) {
        this.handleSearch();
      } else {
        // this.map.centerAndZoom('北京'); // 弃用原因：直接写中文，在后面调用 this.generateMap变更地图时，地图无法变更
        this.map.centerAndZoom(new BMap.Point(116.403964, 39.915332), 10); // 北京天安门
      }
    }
  }

  componentDidUpdate(prevProps) {
    const { initialLon, initialLat, address } = this.props;
    if (initialLon && initialLat) {
      if (initialLon !== prevProps.initialLon || initialLat !== prevProps.initialLat) {
        this.generateMap(initialLon, initialLat);
      }
    } else if (address) {
      // eslint-disable-next-line
      if (address !== prevProps.address) {
        this.handleSearch();
      }
    } else {
      this.map.centerAndZoom(new BMap.Point(116.403964, 39.915332), 10); // 北京天安门
      // 移除已有标记点
      this.map.clearOverlays();
    }
  }

  // 根据当前默认地址address查询其经纬度然后生成地图
  handleSearch() {
    const { address = '', beforeDataLoad, dataLoadSuccess, dataLoadError } = this.props;
    if (isFunction(beforeDataLoad)) {
      beforeDataLoad();
    }
    const newAddress = this.textFilter(address);
    this.setState({
      loading: true,
    });
    request(queryLonAndLatUrl, {
      method: 'GET',
      query: {
        address: newAddress,
      },
    }).then(res => {
      if (res && res.success) {
        const { data } = res;
        const lon = parseFloat(data.lng).toFixed(6);
        const lat = parseFloat(data.lat).toFixed(6);
        if (isFunction(dataLoadSuccess)) {
          dataLoadSuccess(data);
        }
        // 初始化地图并创建标记点
        this.generateMap(lon, lat);
      } else {
        if (isFunction(dataLoadError)) {
          dataLoadError(res);
        }
        notification.error({
          message: res && res.message,
        });
      }
      this.setState({
        loading: false,
      });
    });
  }

  @Bind()
  generateMap(lon, lat) {
    const {
      allowedDrag = false,
      allowedScrollWheelZoom = false,
      dragEvents,
      otherMapOperate,
      initialZoom = 17,
    } = this.props;

    if (!isUndefined(BMap)) {
      let myPoint = {};
      myPoint = new BMap.Point(lon, lat);
      this.map.centerAndZoom(myPoint, initialZoom); // 设初始化地图, zoom级别默认设置为17
      this.map.clearOverlays(); // 移除之前的标注
      const marker = new BMap.Marker(myPoint); // 创建标注
      this.map.addOverlay(marker); // 将标注添加到地图中
      if (allowedDrag) {
        marker.enableDragging();
        if (isFunction(dragEvents)) {
          dragEvents(marker);
        }
      }
      if (allowedScrollWheelZoom) {
        this.map.enableScrollWheelZoom(true);
      }
      if (isFunction(otherMapOperate)) {
        otherMapOperate(this.map, marker);
      }
    }
  }

  // 处理手动输入可能会导致的特殊字符等问题
  @Bind()
  textFilter(str) {
    const pattern = new RegExp("[`~%!@^=''?~！$@￥……&‘”“'？*()（ ），,。.、]");
    let rs = '';
    if (str && str.length) {
      for (let i = 0; i < str.length; i++) {
        rs += str.substr(i, 1).replace(pattern, '');
      }
    }
    return rs;
  }

  render() {
    const { loading } = this.state;
    const {
      mapHeight = '400px',
      mapWidth = '100%',
      skeletonProps,
      isShowSpinOrSkeleton = true,
    } = this.props;
    return (
      <React.Fragment>
        {isShowSpinOrSkeleton ? (
          <Spin spinning={loading}>
            <div id={this.mapUuid} style={{ width: mapWidth, height: mapHeight }} />
          </Spin>
        ) : (
          <Skeleton loading={loading} {...skeletonProps}>
            <div id={this.mapUuid} style={{ width: mapWidth, height: mapHeight }} />
          </Skeleton>
        )}
      </React.Fragment>
    );
  }
}
