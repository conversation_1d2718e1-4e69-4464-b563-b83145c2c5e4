/**
 * 百度地图组件
 * 官方文档地址:http://jser.wang/bmap/articles/start
 *
 * 客户化参数如下:
 * @param moduleName 必输，模块名称
 * @param moduleId 必输，模块Id
 * @param onSetMarkerLocation 非必输，回调函数，参数为坐标
 * @param hideEditMarkerBtn 非必输，隐藏按钮组
 * @param draggingFlag 非必输，false禁止拖拽
 * @param doubleClickZoom 非必输，false禁止双击缩放
 */
import React from 'react';
import { Map, Marker, Base, InfoWindow } from 'rc-bmap';
import { Bind } from 'lodash-decorators';
import { getResponse } from 'utils/utils';
import { GCJToBD } from '../../utils/mapUtils';
import { saveMarker, searchMarker, deleteMarker } from '../../services/api';
import signImgurl from '../../assets/mark-location.png';
import unsignImgurl from '../../assets/unmarked-location.png';

const { Point, Events } = Base;
const { Content } = InfoWindow;

export default class RCBMap extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // amapkey: "1ce5f4aef24df9b3315632813c7fcaa2", // 这个是高德地图给开发者分配的开发者 Key；可以在高德开放平台申请，此处和老版资产云一致
      // what: '点击下方按钮进行标记',
      showDetailFlag: false,
      createMarkerFlag: false,
      markerPosition: {},
      currentPosition: {},
      markerObj: {},
    };
  }

  componentDidMount() {
    this.handleSearchMarker();
  }

  @Bind()
  drawMarker() {
    this.setState({
      // what: '标记坐标点',
      createMarkerFlag: true,
    });
  }

  @Bind()
  close() {
    this.setState({
      // what: '点击下方按钮进行标记',
      createMarkerFlag: false,
    });
  }

  /**
   * 清除marker
   */
  @Bind()
  clearMarker() {
    const { markerObj } = this.state;
    this.setState({ markerPosition: {}, showDetailFlag: false });
    window.myMap.clearOverlays();
    if (markerObj.mapId) {
      getResponse(deleteMarker(markerObj));
    }
  }

  /**
   * 创建marker
   */
  @Bind()
  createMarker(obj) {
    const { onSetMarkerLocation, moduleName, moduleId, tenantId } = this.props;
    const { createMarkerFlag } = this.state;
    const {
      point: { lng, lat },
    } = obj;

    // this.setState({ currentPosition: `(${lng},${lat})` });
    this.setState();
    if (createMarkerFlag) {
      this.setState({
        markerPosition: { lng, lat },
        currentPosition: { lng, lat },
        showDetailFlag: true,
      });
      if (onSetMarkerLocation) {
        onSetMarkerLocation({ lng, lat });
      }
      // 调用保存接口
      if (lng && moduleId && moduleName) {
        getResponse(
          saveMarker({
            tenantId,
            moduleName,
            moduleId,
            longitude: lng,
            latitude: lat,
            mapType: 'BaiduMap',
          })
        ).then(res => {
          if (res) {
            this.setState({
              markerObj: res,
            });
          }
        });
      }
    }
  }

  /**
   * 查询marker
   */
  @Bind()
  handleSearchMarker() {
    const { moduleName, moduleId, tenantId } = this.props;
    if (moduleId) {
      getResponse(
        searchMarker({
          tenantId,
          moduleName,
          moduleId,
        })
      ).then(res => {
        if (res && res.content[0]) {
          if (res.content[0].mapType !== 'BaiduMap') {
            const ll = GCJToBD(res.content[0].longitude, res.content[0].latitude);
            this.setState({
              markerPosition: { ...ll },
              currentPosition: { ...ll },
              markerObj: res.content[0],
            });
          } else {
            this.setState({
              markerPosition: {
                lng: res.content[0].longitude,
                lat: res.content[0].latitude,
              },
              currentPosition: {
                lng: res.content[0].longitude,
                lat: res.content[0].latitude,
              },
              markerObj: res.content[0],
            });
          }
        }
      });
    }
  }

  @Bind()
  drawMarkerSwitchChange() {
    if (!this.state.createMarkerFlag) {
      this.drawMarker();
    } else {
      this.close();
    }
  }

  @Bind()
  cilckMarker() {
    this.setState({ showDetailFlag: true });
  }

  render() {
    const { hideEditMarkerBtn, draggingFlag, doubleClickZoom } = this.props;
    const {
      // amapkey,
      createMarkerFlag,
      // what,
      currentPosition,
      markerPosition,
      showDetailFlag,
    } = this.state;
    const editMarkerBtnStyle = hideEditMarkerBtn ? { display: 'none' } : {};
    const layerStyle = {
      padding: '10px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '4px',
      position: 'absolute',
      top: '10px',
      left: '10px',
    };
    const popStyle = {
      padding: '10px',
      background: '#fff',
      // border: '1px solid #ddd',
      borderRadius: '4px',
      width: '160px',
      height: '65px',
    };
    const scrollWheelZoom = false;

    return (
      <React.Fragment>
        <Map
          ak="CAyKrU9jUfMsrmeiOehsZtcUvYnNkRIG"
          name="myMap"
          doubleClickZoom={doubleClickZoom !== false}
          scrollWheelZoom={scrollWheelZoom} // 禁止滚动
          defaultCursor={createMarkerFlag ? 'Crosshair' : 'Pointer'}
          zoom={15}
          mapClick={false}
          dragging={draggingFlag !== false} // 禁止拖拽
        >
          <Point
            name="center"
            lng={markerPosition.lng || currentPosition.lng || '116.404'}
            lat={markerPosition.lat || currentPosition.lat || '39.915'}
          />
          <Events click={this.createMarker} />
          {markerPosition.lng ? (
            <Marker id="marker">
              <Point lng={markerPosition.lng} lat={markerPosition.lat} />
              <Events click={this.cilckMarker} />
              {showDetailFlag && (
                <InfoWindow height={0} width={0}>
                  <Point lng={markerPosition.lng} lat={markerPosition.lat} />
                  <Content>
                    <div style={popStyle}>
                      {`${markerPosition.lng}，${markerPosition.lat}`}
                      <br />
                      <button
                        type="button"
                        onMouseUp={() => {
                          this.clearMarker();
                        }}
                      >
                        {' '}
                        删除标记{' '}
                      </button>
                    </div>
                  </Content>
                </InfoWindow>
              )}
            </Marker>
          ) : (
            ''
          )}
          {!hideEditMarkerBtn && (
            <div style={layerStyle}>
              <div style={editMarkerBtnStyle}>
                <img
                  style={{ width: '25px', height: '25px' }}
                  src={createMarkerFlag ? unsignImgurl : signImgurl}
                  title="标记地点"
                  alt="标记地点"
                  onClick={this.drawMarkerSwitchChange}
                />
              </div>
            </div>
          )}
        </Map>
      </React.Fragment>
    );
  }
}
